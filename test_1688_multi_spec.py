#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试1688多规格商品解析
"""

import requests
import json

def test_1688_multi_spec():
    """测试1688多规格商品"""
    print("🔍 测试1688多规格商品解析...")
    
    data = {
        "product_url": "https://detail.1688.com/offer/932222752479.html?test=2",
        "language": "zh"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/v1/upstream/detail", json=data, timeout=60)
        result = response.json()
        
        if result.get('code') == 200:
            product_data = result.get('data', {})
            
            print(f"\n📦 商品基本信息:")
            print(f"  - ID: {product_data.get('id')}")
            print(f"  - 名称: {product_data.get('name', '')[:50]}...")
            print(f"  - 图片数量: {len(product_data.get('sliderPicUrls', []))}")
            
            # 详细分析SKU信息
            skus = product_data.get('skus', [])
            print(f"\n📋 SKU详细分析 (共{len(skus)}个):")
            
            if skus:
                # 显示前10个SKU的详细信息
                for i, sku in enumerate(skus[:10]):
                    properties = sku.get('properties', [])
                    print(f"\n  SKU {i+1}: {len(properties)}个属性")
                    for prop in properties:
                        print(f"    - {prop.get('propertyName')}={prop.get('valueName')} (ID:{prop.get('propertyId')}, ValueID:{prop.get('valueId')})")
                
                # 统计属性维度
                all_properties = {}
                for sku in skus:
                    for prop in sku.get('properties', []):
                        prop_name = prop.get('propertyName')
                        prop_value = prop.get('valueName')
                        if prop_name not in all_properties:
                            all_properties[prop_name] = set()
                        all_properties[prop_name].add(prop_value)
                
                print(f"\n📊 属性维度统计:")
                for prop_name, values in all_properties.items():
                    print(f"  - {prop_name}: {len(values)}个值")
                    if len(values) <= 15:  # 显示所有值
                        sorted_values = sorted(values)
                        print(f"    值: {', '.join(sorted_values)}")
                    else:  # 只显示前几个
                        sample_values = list(sorted(values))[:10]
                        print(f"    值: {', '.join(sample_values)}... (共{len(values)}个)")
                
                # 判断是否为多规格
                is_multi_spec = len(all_properties) > 1
                print(f"\n🎯 规格判断: {'多规格' if is_multi_spec else '单规格'} ({len(all_properties)}个属性维度)")
                
                if is_multi_spec:
                    print("✅ 多规格解析成功！")
                else:
                    print("❌ 多规格解析失败，应该是多规格但被识别为单规格")
                
            else:
                print("  ❌ 没有找到SKU数据")
                
        else:
            print(f"❌ 请求失败: {result.get('code')}")
            print(f"错误信息: {result.get('message')}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_1688_multi_spec()
