# AqentCrawler 上游API接口文档

## 概述

AqentCrawler 为上游代购系统提供标准化的商品搜索和详情获取API接口。本文档详细描述了所有可用的API接口、请求参数、响应格式和使用示例。

## 基础信息

- **Base URL**: `http://your-domain:8000/api/v1/upstream`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Bearer Token / 预置Token

## 认证

### 请求头格式
```http
Authorization: Bearer your-token-here
Content-Type: application/json
```

### 认证方式说明
1. **Bearer Token**: 标准JWT Token认证
2. **预置Token**: 系统预配置的固定Token
3. **可选认证**: 部分接口支持无认证访问

## 支持的平台

| 平台代码 | 平台名称 | 状态 | 说明 |
|---------|---------|------|------|
| `taobao` | 淘宝 | ✅ 已支持 | 完整功能支持 |
| `tmall` | 天猫 | ✅ 已支持 | 完整功能支持 |
| `1688` | 阿里巴巴1688 | ✅ 已支持 | 完整功能支持 |
| `jd` | 京东 | 🚧 开发中 | 计划支持 |
| `pdd` | 拼多多 | 🚧 开发中 | 计划支持 |

## 支持的语言

| 语言代码 | 语言名称 | 翻译支持 |
|---------|---------|----------|
| `zh` | 中文 | ✅ 原生支持 |
| `en` | 英语 | ✅ 自动翻译 |
| `fr` | 法语 | ✅ 自动翻译 |
| `de` | 德语 | ✅ 自动翻译 |
| `es` | 西班牙语 | ✅ 自动翻译 |
| `it` | 意大利语 | ✅ 自动翻译 |
| `ja` | 日语 | ✅ 自动翻译 |
| `ko` | 韩语 | ✅ 自动翻译 |

## API接口

### 1. 商品搜索

根据关键词搜索商品，支持多平台、多语言、排序和价格过滤。

**接口地址**: `POST /search`

#### 请求参数

```json
{
  "keyword": "iPhone 15",
  "platform": "taobao",
  "language": "en",
  "page": 1,
  "page_size": 20,
  "sort": "default",
  "price_min": 100.0,
  "price_max": 2000.0
}
```

| 参数 | 类型 | 必填 | 说明 | 默认值 | 限制 |
|------|------|------|------|--------|------|
| `keyword` | string | ✅ | 搜索关键词 | - | 1-200字符 |
| `platform` | string | ✅ | 平台代码 | - | taobao/tmall/1688/jd/pdd |
| `language` | string | ❌ | 语言代码 | `zh` | 见支持语言表 |
| `page` | integer | ❌ | 页码 | `1` | 1-10 |
| `page_size` | integer | ❌ | 每页数量 | `20` | 1-50 |
| `sort` | string | ❌ | 排序方式 | `default` | default/price_asc/price_desc/sales/newest |
| `price_min` | float | ❌ | 最低价格 | - | ≥0 |
| `price_max` | float | ❌ | 最高价格 | - | ≥0 |

#### 响应示例

```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "products": [
      {
        "id": "123456789",
        "name": "iPhone 15 Pro Max 256GB",
        "introduction": "Apple iPhone 15 Pro Max with advanced features",
        "price": 899900,
        "marketPrice": 999900,
        "picUrl": "https://example.com/image.jpg",
        "sliderPicUrls": ["https://example.com/image1.jpg"],
        "shopName": "Apple Official Store",
        "source": "taobao",
        "sourceLink": "https://item.taobao.com/item.htm?id=123456789",
        "stock": 100,
        "salesCount": 1000,
        "freight": 0,
        "scores": 4.8
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 1000,
      "total_pages": 50
    },
    "search_info": {
      "keyword": "iPhone 15",
      "platform": "taobao",
      "language": "en",
      "search_time": 1640995200
    }
  }
}
```

### 2. 商品详情

根据商品链接获取详细信息，包括SKU、属性、描述等。

**接口地址**: `POST /detail`

#### 请求参数

```json
{
  "product_url": "https://item.taobao.com/item.htm?id=123456789",
  "language": "en"
}
```

| 参数 | 类型 | 必填 | 说明 | 默认值 |
|------|------|------|------|--------|
| `product_url` | string | ✅ | 商品链接 | - |
| `language` | string | ❌ | 语言代码 | `zh` |

#### 响应示例

```json
{
  "code": 200,
  "message": "获取详情成功",
  "data": {
    "id": "123456789",
    "name": "iPhone 15 Pro Max 256GB",
    "introduction": "Apple iPhone 15 Pro Max with advanced features",
    "description": "<div>Detailed product description...</div>",
    "keyword": "iPhone 15 Pro Max",
    "categoryId": 50008090,
    "picUrl": "https://example.com/image.jpg",
    "sliderPicUrls": ["https://example.com/image1.jpg"],
    "specType": true,
    "price": 899900,
    "marketPrice": 999900,
    "stock": 100,
    "type": 0,
    "freight": 0,
    "shopName": "Apple Official Store",
    "source": "taobao",
    "sourceLink": "https://item.taobao.com/item.htm?id=123456789",
    "scores": 4.8,
    "newest": false,
    "sale": true,
    "hot": true,
    "salesCount": 1000,
    "skus": [
      {
        "id": "sku123",
        "price": 899900,
        "marketPrice": 999900,
        "stock": 50,
        "properties": [
          {
            "name": "颜色",
            "valueName": "深空黑色"
          },
          {
            "name": "容量",
            "valueName": "256GB"
          }
        ]
      }
    ],
    "props": [
      {
        "name": "品牌",
        "value": "Apple"
      },
      {
        "name": "型号",
        "value": "iPhone 15 Pro Max"
      }
    ]
  }
}
```

### 3. 健康检查

检查服务状态和可用性。

**接口地址**: `GET /health`

#### 响应示例

```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "healthy",
    "timestamp": "2025-01-20T10:30:00Z",
    "version": "1.0.0"
  }
}
```

### 4. 服务统计

获取服务统计信息。

**接口地址**: `GET /stats`

#### 响应示例

```json
{
  "code": 200,
  "message": "获取统计信息成功",
  "data": {
    "total_requests": 10000,
    "cache_hit_rate": 0.85,
    "average_response_time": 1200,
    "supported_platforms": ["taobao", "tmall", "1688", "jd", "pdd"],
    "supported_languages": ["zh", "en", "fr", "de", "es", "it", "ja", "ko"]
  }
}
```

## 响应格式说明

### 统一响应结构

所有API接口都遵循统一的响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {...}
}
```

### 状态码说明

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | 正常处理 |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 认证失败 | 检查Token |
| 403 | 权限不足 | 联系管理员 |
| 404 | 资源不存在 | 检查请求路径 |
| 429 | 请求过于频繁 | 降低请求频率 |
| 500 | 服务器错误 | 重试或联系技术支持 |

### 价格字段说明

- 所有价格字段以**分**为单位
- 例如：`899900` 表示 `8999.00` 元
- 转换公式：`元 = 分 / 100`

### 图片URL说明

- 所有图片URL都是完整的HTTP/HTTPS链接
- 支持常见图片格式：JPG、PNG、WebP
- 建议缓存图片以提高加载速度

## 错误处理

### 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数错误：keyword不能为空",
  "data": null
}
```

### 常见错误及解决方案

1. **认证失败 (401)**
   - 检查Token是否正确
   - 确认Token是否过期
   - 验证请求头格式

2. **参数错误 (400)**
   - 检查必填参数
   - 验证参数格式
   - 确认参数值范围

3. **服务器错误 (500)**
   - 重试请求
   - 检查网络连接
   - 联系技术支持

## 使用建议

### 1. 认证管理
- 妥善保管Token，避免泄露
- 实现Token自动刷新机制
- 监控Token有效期

### 2. 请求优化
- 合理设置请求超时时间
- 实现请求重试机制
- 使用连接池提高性能

### 3. 缓存策略
- 缓存商品详情数据
- 设置合理的缓存过期时间
- 实现缓存更新机制

### 4. 错误处理
- 实现完整的错误处理逻辑
- 记录错误日志便于排查
- 提供用户友好的错误提示

### 5. 性能监控
- 监控API响应时间
- 统计成功率和错误率
- 设置性能告警阈值

## 技术支持

如有技术问题或需要支持，请联系：
- 邮箱：<EMAIL>
- 技术文档：http://docs.example.com
- 问题反馈：http://issues.example.com

---

**版本**: v1.0.0  
**更新时间**: 2025-01-20  
**文档状态**: 最新
