# 数据库配置
MYSQL_ROOT_PASSWORD=aqent_root_2024_change_me
MYSQL_DATABASE=aqent_crawler
MYSQL_USER=aqent_user
MYSQL_PASSWORD=aqent_pass_2024_change_me

# JWT和API密钥配置
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-32-chars-minimum
API_SECRET_KEY=your-api-secret-key-change-in-production-32-chars-minimum
ENCRYPTION_KEY=your-encryption-key-exactly-32-chars

# 密码加密配置
PASSWORD_SECRET_KEY=AqentCrawler2024PasswordSecretKey

# 翻译服务API密钥
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key
DEEPL_API_KEY=your_deepl_api_key
ALIYUN_ACCESS_KEY=your_aliyun_access_key
ALIYUN_SECRET_KEY=your_aliyun_secret_key

# 代理服务配置
ABUYUN_PROXY_USER=your_abuyun_username
ABUYUN_PROXY_PASS=your_abuyun_password
ZHIMA_PROXY_USER=your_zhima_username
ZHIMA_PROXY_PASS=your_zhima_password

# 打码平台配置
JIJIAN_USERNAME=your_jijian_username
JIJIAN_PASSWORD=your_jijian_password
RUOKUAI_USERNAME=your_ruokuai_username
RUOKUAI_PASSWORD=your_ruokuai_password

# 邮件服务配置
SMTP_SERVER=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password_or_app_password
SMTP_USE_TLS=true
SMTP_USE_SSL=false
SENDER_EMAIL=<EMAIL>
SENDER_NAME=AqentCrawler系统

# 邮件告警配置
EMAIL_ALERTS_ENABLED=true
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
ALERT_COOLDOWN_MINUTES=30

# 监控和日志配置
SENTRY_DSN=your_sentry_dsn_for_error_tracking
LOG_LEVEL=INFO

# 安全配置
ALLOWED_IPS=127.0.0.1,***********/24
CORS_ORIGINS=http://localhost:3000,https://your-admin-domain.com

# 性能配置
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30
CACHE_TTL=3600

# 业务配置
DEFAULT_TRANSLATION_SERVICE=google
ENABLE_TRANSLATION=true
ENABLE_PROXY=true
MAX_RETRY_ATTEMPTS=3
