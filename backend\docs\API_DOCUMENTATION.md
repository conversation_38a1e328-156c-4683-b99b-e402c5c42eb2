# AqentCrawler 上游API文档

## 概述

AqentCrawler 是一个专为代购系统设计的智能爬虫服务，提供中国主流电商平台的商品搜索和详情获取功能。本文档描述了为上游代购系统提供的标准化API接口。

## 基础信息

- **Base URL**: `http://your-domain:8000/api/v1/upstream`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

目前API接口支持以下认证方式：
- Bearer Token认证
- 预置Token认证

### 请求头示例
```http
Authorization: Bearer your-token-here
Content-Type: application/json
```

## 支持的平台

| 平台代码 | 平台名称 | 状态 |
|---------|---------|------|
| `taobao` | 淘宝 | ✅ 已支持 |
| `tmall` | 天猫 | ✅ 已支持 |
| `1688` | 阿里巴巴1688 | ✅ 已支持 |
| `jd` | 京东 | 🚧 开发中 |
| `pdd` | 拼多多 | 🚧 开发中 |

## 支持的语言

| 语言代码 | 语言名称 | 状态 |
|---------|---------|------|
| `zh` | 中文 | ✅ 原生支持 |
| `en` | 英语 | ✅ 翻译支持 |
| `fr` | 法语 | ✅ 翻译支持 |
| `de` | 德语 | ✅ 翻译支持 |
| `es` | 西班牙语 | ✅ 翻译支持 |
| `it` | 意大利语 | ✅ 翻译支持 |
| `ja` | 日语 | ✅ 翻译支持 |
| `ko` | 韩语 | ✅ 翻译支持 |

## API接口

### 1. 商品搜索

根据关键词搜索商品，支持多平台、多语言、排序和价格过滤。

**接口地址**: `POST /search`

#### 请求参数

```json
{
  "keyword": "iPhone 15",
  "platform": "taobao",
  "language": "en",
  "page": 1,
  "page_size": 20,
  "sort": "default",
  "price_min": 100.0,
  "price_max": 2000.0
}
```

| 参数 | 类型 | 必填 | 说明 | 默认值 |
|------|------|------|------|--------|
| `keyword` | string | ✅ | 搜索关键词 (1-200字符) | - |
| `platform` | string | ✅ | 平台代码 | - |
| `language` | string | ❌ | 请求语言代码 | `zh` |
| `page` | integer | ❌ | 页码 (1-10) | `1` |
| `page_size` | integer | ❌ | 每页数量 (1-50) | `20` |
| `sort` | string | ❌ | 排序方式 | `default` |
| `price_min` | float | ❌ | 最低价格(元) | - |
| `price_max` | float | ❌ | 最高价格(元) | - |

#### 排序方式

| 值 | 说明 |
|----|------|
| `default` | 默认排序 |
| `price_asc` | 价格升序 |
| `price_desc` | 价格降序 |
| `sales` | 销量排序 |
| `newest` | 最新商品 |

#### 响应示例

```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "products": [
      {
        "id": "123456789",
        "name": "iPhone 15 Pro Max 256GB",
        "introduction": "Apple iPhone 15 Pro Max with advanced features",
        "price": 899900,
        "marketPrice": 999900,
        "picUrl": "https://example.com/image.jpg",
        "sliderPicUrls": ["https://example.com/image1.jpg"],
        "shopName": "Apple Official Store",
        "source": "taobao",
        "sourceLink": "https://item.taobao.com/item.htm?id=123456789",
        "stock": 100,
        "salesCount": 1000,
        "freight": 0,
        "scores": 4.8
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20,
    "platform": "taobao",
    "keyword": "iPhone 15",
    "timestamp": "2025-06-18T13:49:22.123Z"
  }
}
```

### 2. 商品详情

根据商品链接获取详细信息，包括SKU、属性、描述等。

**接口地址**: `POST /detail`

#### 请求参数

```json
{
  "product_url": "https://item.taobao.com/item.htm?id=123456789",
  "language": "en"
}
```

| 参数 | 类型 | 必填 | 说明 | 默认值 |
|------|------|------|------|--------|
| `product_url` | string | ✅ | 商品链接 (10-500字符) | - |
| `language` | string | ❌ | 请求语言代码 | `zh` |

#### 响应示例

```json
{
  "code": 200,
  "message": "获取详情成功",
  "data": {
    "id": "123456789",
    "name": "iPhone 15 Pro Max 256GB",
    "introduction": "Apple iPhone 15 Pro Max with advanced features",
    "description": "<div>Detailed product description...</div>",
    "keyword": "iPhone 15 Pro Max",
    "categoryId": 50008090,
    "picUrl": "https://example.com/image.jpg",
    "sliderPicUrls": ["https://example.com/image1.jpg"],
    "specType": true,
    "price": 899900,
    "marketPrice": 999900,
    "stock": 100,
    "type": 0,
    "freight": 0,
    "shopName": "Apple Official Store",
    "source": "taobao",
    "sourceLink": "https://item.taobao.com/item.htm?id=123456789",
    "scores": 4.8,
    "newest": false,
    "sale": true,
    "hot": true,
    "salesCount": 1000,
    "skus": [
      {
        "id": "sku123",
        "price": 899900,
        "marketPrice": 999900,
        "stock": 50,
        "picUrl": "https://example.com/sku-image.jpg",
        "properties": [
          {
            "propertyName": "Color",
            "valueName": "Space Black"
          },
          {
            "propertyName": "Storage",
            "valueName": "256GB"
          }
        ]
      }
    ],
    "props": [
      {
        "name": "Brand",
        "value": "Apple"
      },
      {
        "name": "Model",
        "value": "iPhone 15 Pro Max"
      }
    ]
  }
}
```

### 3. 健康检查

检查服务状态。

**接口地址**: `GET /health`

#### 响应示例

```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "healthy",
    "timestamp": "2025-06-18T13:49:22.123Z",
    "version": "1.0.0"
  }
}
```

### 4. 服务统计

获取服务统计信息。

**接口地址**: `GET /stats`

#### 响应示例

```json
{
  "code": 200,
  "message": "获取统计信息成功",
  "data": {
    "total_requests": 10000,
    "cache_hit_rate": 0.85,
    "average_response_time": 1200,
    "supported_platforms": ["taobao", "tmall", "1688", "jd", "pdd"],
    "supported_languages": ["zh", "en", "fr", "de", "es", "it", "ja", "ko"]
  }
}
```

## 数据格式说明

### 价格单位
- 所有价格字段以**分**为单位
- 例如：`899900` 表示 ¥8999.00

### 商品信息字段

| 字段 | 类型 | 说明 |
|------|------|------|
| `id` | string | 商品ID |
| `name` | string | 商品名称 |
| `introduction` | string | 商品介绍 |
| `description` | string | 商品详情描述(HTML) |
| `price` | integer | 实际价格(分) |
| `marketPrice` | integer | 市场价格(分) |
| `picUrl` | string | 主图URL |
| `sliderPicUrls` | array | 轮播图URLs |
| `shopName` | string | 店铺名称 |
| `source` | string | 来源平台 |
| `sourceLink` | string | 原始链接 |
| `stock` | integer | 库存数量 |
| `salesCount` | integer | 销量 |
| `freight` | integer | 运费(分) |
| `scores` | float | 评分 |

## 错误处理

### 错误响应格式

```json
{
  "code": 400,
  "message": "错误描述",
  "data": null
}
```

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率过高 |
| 500 | 服务器内部错误 |

## 缓存机制

- **搜索结果**: 缓存30分钟
- **商品详情**: 缓存1小时
- **翻译结果**: 缓存24小时

## 翻译功能

### 自动翻译
- 当`language`参数不为`zh`时，系统会自动翻译关键词和返回结果
- 支持商品名称、介绍、店铺名称、SKU属性等字段的翻译
- 使用多种翻译引擎，确保翻译质量和可用性

### 翻译引擎
1. **免费引擎**: Google Translate免费API (默认)
2. **Google翻译**: 需要API密钥
3. **百度翻译**: 需要API密钥
4. **有道翻译**: 需要API密钥

## 使用示例

### Python示例

```python
import requests

# 搜索商品
search_data = {
    "keyword": "iPhone 15",
    "platform": "taobao",
    "language": "en",
    "page": 1,
    "page_size": 10
}

response = requests.post(
    "http://your-domain:8000/api/v1/upstream/search",
    json=search_data,
    headers={"Authorization": "Bearer your-token"}
)

result = response.json()
print(f"找到 {result['data']['total']} 个商品")

# 获取商品详情
detail_data = {
    "product_url": "https://item.taobao.com/item.htm?id=123456789",
    "language": "en"
}

response = requests.post(
    "http://your-domain:8000/api/v1/upstream/detail",
    json=detail_data,
    headers={"Authorization": "Bearer your-token"}
)

detail = response.json()
print(f"商品名称: {detail['data']['name']}")
```

### JavaScript示例

```javascript
// 搜索商品
const searchData = {
  keyword: "iPhone 15",
  platform: "taobao",
  language: "en",
  page: 1,
  page_size: 10
};

const searchResponse = await fetch("http://your-domain:8000/api/v1/upstream/search", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Authorization": "Bearer your-token"
  },
  body: JSON.stringify(searchData)
});

const searchResult = await searchResponse.json();
console.log(`找到 ${searchResult.data.total} 个商品`);

// 获取商品详情
const detailData = {
  product_url: "https://item.taobao.com/item.htm?id=123456789",
  language: "en"
};

const detailResponse = await fetch("http://your-domain:8000/api/v1/upstream/detail", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Authorization": "Bearer your-token"
  },
  body: JSON.stringify(detailData)
});

const detailResult = await detailResponse.json();
console.log(`商品名称: ${detailResult.data.name}`);
```

## 性能优化建议

1. **使用缓存**: 相同的搜索请求会返回缓存结果，提高响应速度
2. **合理分页**: 建议每页不超过50个商品
3. **语言设置**: 如果不需要翻译，请使用`language: "zh"`
4. **错误重试**: 建议实现指数退避的重试机制

## 联系支持

如有问题或建议，请联系技术支持团队。
