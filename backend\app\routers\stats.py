"""
系统统计API路由
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime, timezone, timedelta

from app.database import get_db
from app.models import Platform, CrawlerAccount, ProxyPool, ApiCallLog
from app.utils.response import success_response, error_response

router = APIRouter(prefix="/api/v1/stats", tags=["系统统计"])

@router.get("/")
async def get_overview_stats(db: Session = Depends(get_db)):
    """获取系统概览统计"""
    try:
        # 平台统计
        total_platforms = db.query(func.count(Platform.id)).scalar() or 0
        enabled_platforms = db.query(func.count(Platform.id)).filter(
            Platform.is_enabled == True
        ).scalar() or 0
        
        # 账号统计
        total_accounts = db.query(func.count(CrawlerAccount.id)).scalar() or 0
        active_accounts = db.query(func.count(CrawlerAccount.id)).filter(
            CrawlerAccount.status == 'active'
        ).scalar() or 0
        
        # 代理统计
        total_proxies = db.query(func.count(ProxyPool.id)).scalar() or 0
        active_proxies = db.query(func.count(ProxyPool.id)).filter(
            ProxyPool.status == 'active',
            ProxyPool.is_enabled == True
        ).scalar() or 0
        
        # 请求统计（最近24小时）
        yesterday = datetime.now(timezone.utc) - timedelta(days=1)
        recent_requests = db.query(func.count(ApiCallLog.id)).filter(
            ApiCallLog.created_at >= yesterday
        ).scalar() or 0
        
        successful_requests = db.query(func.count(ApiCallLog.id)).filter(
            ApiCallLog.created_at >= yesterday,
            ApiCallLog.response_code == 200
        ).scalar() or 0
        
        # 成功率
        success_rate = (successful_requests / recent_requests * 100) if recent_requests > 0 else 0
        
        stats_data = {
            "total_platforms": total_platforms,
            "enabled_platforms": enabled_platforms,
            "total_accounts": total_accounts,
            "active_accounts": active_accounts,
            "total_proxies": total_proxies,
            "active_proxies": active_proxies,
            "recent_requests": recent_requests,
            "successful_requests": successful_requests,
            "success_rate": round(success_rate, 2),
            "last_updated": datetime.now(timezone.utc).isoformat()
        }
        
        return success_response(stats_data, "获取系统统计成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统统计失败: {str(e)}")

@router.get("/dashboard")
async def get_dashboard_stats(db: Session = Depends(get_db)):
    """获取仪表板统计数据"""
    try:
        # 基础统计
        overview = await get_overview_stats(db)
        overview_data = overview["data"]
        
        # 最近7天的请求趋势
        week_ago = datetime.now(timezone.utc) - timedelta(days=7)
        daily_requests = db.query(
            func.date(ApiCallLog.created_at).label('date'),
            func.count(ApiCallLog.id).label('count')
        ).filter(
            ApiCallLog.created_at >= week_ago
        ).group_by(func.date(ApiCallLog.created_at)).order_by('date').all()
        
        # 平台使用分布
        platform_usage = db.query(
            Platform.name,
            func.count(ApiCallLog.id).label('count')
        ).join(ApiCallLog, Platform.id == ApiCallLog.platform_id, isouter=True).filter(
            ApiCallLog.created_at >= week_ago
        ).group_by(Platform.id, Platform.name).order_by(func.count(ApiCallLog.id).desc()).limit(5).all()
        
        dashboard_data = {
            **overview_data,
            "daily_trends": [
                {"date": str(item.date), "requests": item.count}
                for item in daily_requests
            ],
            "platform_usage": [
                {"platform": item.name, "requests": item.count}
                for item in platform_usage
            ]
        }
        
        return success_response(dashboard_data, "获取仪表板数据成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")

@router.get("/performance")
async def get_performance_stats(db: Session = Depends(get_db)):
    """获取性能统计"""
    try:
        # 最近24小时的性能数据
        yesterday = datetime.now(timezone.utc) - timedelta(days=1)
        
        # 平均响应时间
        avg_response_time = db.query(func.avg(ApiCallLog.response_time_ms)).filter(
            ApiCallLog.created_at >= yesterday
        ).scalar() or 0
        
        # 错误率统计
        total_requests = db.query(func.count(ApiCallLog.id)).filter(
            ApiCallLog.created_at >= yesterday
        ).scalar() or 0
        
        error_requests = db.query(func.count(ApiCallLog.id)).filter(
            ApiCallLog.created_at >= yesterday,
            ApiCallLog.response_code != 200
        ).scalar() or 0
        
        error_rate = (error_requests / total_requests * 100) if total_requests > 0 else 0
        
        # 代理性能
        proxy_performance = db.query(
            func.avg(ProxyPool.success_rate).label('avg_success_rate'),
            func.avg(ProxyPool.avg_response_time).label('avg_response_time')
        ).filter(ProxyPool.is_enabled == True).first()
        
        performance_data = {
            "avg_response_time": round(float(avg_response_time), 2),
            "error_rate": round(error_rate, 2),
            "total_requests_24h": total_requests,
            "error_requests_24h": error_requests,
            "proxy_avg_success_rate": round(float(proxy_performance.avg_success_rate or 0), 2),
            "proxy_avg_response_time": round(float(proxy_performance.avg_response_time or 0), 2)
        }
        
        return success_response(performance_data, "获取性能统计成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能统计失败: {str(e)}")
