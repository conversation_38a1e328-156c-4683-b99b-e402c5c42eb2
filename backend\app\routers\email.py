"""
邮件管理API路由
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, EmailStr
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime

from ..services.email_service import email_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/email", tags=["邮件管理"])

class EmailRequest(BaseModel):
    """邮件发送请求"""
    to_emails: List[EmailStr]
    subject: str
    content: str
    content_type: str = 'html'
    cc_emails: Optional[List[EmailStr]] = None
    bcc_emails: Optional[List[EmailStr]] = None
    reply_to: Optional[EmailStr] = None

class TemplateEmailRequest(BaseModel):
    """模板邮件发送请求"""
    to_emails: List[EmailStr]
    template_name: str
    subject: str
    template_data: Dict[str, Any]
    cc_emails: Optional[List[EmailStr]] = None
    bcc_emails: Optional[List[EmailStr]] = None
    reply_to: Optional[EmailStr] = None

class SystemAlertRequest(BaseModel):
    """系统告警邮件请求"""
    to_emails: List[EmailStr]
    alert_title: str
    alert_message: str
    alert_type: str = 'warning'  # success, warning, danger, info
    alert_details: Optional[Dict[str, Any]] = None
    affected_services: Optional[List[Dict[str, Any]]] = None
    metrics: Optional[List[Dict[str, Any]]] = None
    recommendations: Optional[List[str]] = None
    action_url: Optional[str] = None
    alert_id: Optional[str] = None

class CrawlerStatusRequest(BaseModel):
    """爬虫状态通知邮件请求"""
    to_emails: List[EmailStr]
    notification_title: str
    notification_message: str
    status_type: str = 'info'  # success, warning, danger, info
    crawler_accounts: Optional[List[Dict[str, Any]]] = None
    token_info: Optional[List[Dict[str, Any]]] = None
    failed_operations: Optional[List[Dict[str, Any]]] = None
    statistics: Optional[List[Dict[str, Any]]] = None
    next_actions: Optional[List[str]] = None
    dashboard_url: Optional[str] = None

class ApiReportRequest(BaseModel):
    """API报告邮件请求"""
    to_emails: List[EmailStr]
    report_title: str
    report_description: str
    report_period: Optional[Dict[str, str]] = None
    summary_stats: Optional[List[Dict[str, Any]]] = None
    api_endpoints: Optional[List[Dict[str, Any]]] = None
    platform_stats: Optional[List[Dict[str, Any]]] = None
    error_analysis: Optional[Dict[str, Any]] = None
    performance_metrics: Optional[List[Dict[str, Any]]] = None
    recommendations: Optional[List[Dict[str, Any]]] = None
    dashboard_url: Optional[str] = None
    report_type: str = '日常统计'

@router.post("/send")
async def send_email(request: EmailRequest):
    """
    发送普通邮件
    """
    try:
        success = await email_service.send_email(
            to_emails=request.to_emails,
            subject=request.subject,
            content=request.content,
            content_type=request.content_type,
            cc_emails=request.cc_emails,
            bcc_emails=request.bcc_emails,
            reply_to=request.reply_to
        )
        
        if success:
            return {"code": 200, "message": "邮件发送成功"}
        else:
            return {"code": 500, "message": "邮件发送失败"}
            
    except Exception as e:
        logger.error(f"发送邮件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/send-template")
async def send_template_email(request: TemplateEmailRequest):
    """
    发送模板邮件
    """
    try:
        success = await email_service.send_template_email(
            to_emails=request.to_emails,
            template_name=request.template_name,
            subject=request.subject,
            template_data=request.template_data,
            cc_emails=request.cc_emails,
            bcc_emails=request.bcc_emails,
            reply_to=request.reply_to
        )
        
        if success:
            return {"code": 200, "message": "模板邮件发送成功"}
        else:
            return {"code": 500, "message": "模板邮件发送失败"}
            
    except Exception as e:
        logger.error(f"发送模板邮件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/alert/system")
async def send_system_alert(request: SystemAlertRequest):
    """
    发送系统告警邮件
    """
    try:
        template_data = {
            'alert_title': request.alert_title,
            'alert_message': request.alert_message,
            'alert_type': request.alert_type,
            'alert_details': request.alert_details,
            'affected_services': request.affected_services,
            'metrics': request.metrics,
            'recommendations': request.recommendations,
            'action_url': request.action_url,
            'alert_id': request.alert_id,
            'alert_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        success = await email_service.send_template_email(
            to_emails=request.to_emails,
            template_name='system_alert',
            subject=f"[系统告警] {request.alert_title}",
            template_data=template_data
        )
        
        if success:
            return {"code": 200, "message": "系统告警邮件发送成功"}
        else:
            return {"code": 500, "message": "系统告警邮件发送失败"}
            
    except Exception as e:
        logger.error(f"发送系统告警邮件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/alert/crawler")
async def send_crawler_status(request: CrawlerStatusRequest):
    """
    发送爬虫状态通知邮件
    """
    try:
        template_data = {
            'notification_title': request.notification_title,
            'notification_message': request.notification_message,
            'status_type': request.status_type,
            'crawler_accounts': request.crawler_accounts,
            'token_info': request.token_info,
            'failed_operations': request.failed_operations,
            'statistics': request.statistics,
            'next_actions': request.next_actions,
            'dashboard_url': request.dashboard_url,
            'notification_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_accounts': len(request.crawler_accounts) if request.crawler_accounts else 0,
            'active_accounts': len([acc for acc in (request.crawler_accounts or []) if acc.get('status') == 'active'])
        }
        
        success = await email_service.send_template_email(
            to_emails=request.to_emails,
            template_name='crawler_status',
            subject=f"[爬虫通知] {request.notification_title}",
            template_data=template_data
        )
        
        if success:
            return {"code": 200, "message": "爬虫状态通知邮件发送成功"}
        else:
            return {"code": 500, "message": "爬虫状态通知邮件发送失败"}
            
    except Exception as e:
        logger.error(f"发送爬虫状态通知邮件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/report/api")
async def send_api_report(request: ApiReportRequest):
    """
    发送API调用报告邮件
    """
    try:
        template_data = {
            'report_title': request.report_title,
            'report_description': request.report_description,
            'report_period': request.report_period,
            'summary_stats': request.summary_stats,
            'api_endpoints': request.api_endpoints,
            'platform_stats': request.platform_stats,
            'error_analysis': request.error_analysis,
            'performance_metrics': request.performance_metrics,
            'recommendations': request.recommendations,
            'dashboard_url': request.dashboard_url,
            'report_type': request.report_type,
            'report_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        success = await email_service.send_template_email(
            to_emails=request.to_emails,
            template_name='api_report',
            subject=f"[API报告] {request.report_title}",
            template_data=template_data
        )
        
        if success:
            return {"code": 200, "message": "API报告邮件发送成功"}
        else:
            return {"code": 500, "message": "API报告邮件发送失败"}
            
    except Exception as e:
        logger.error(f"发送API报告邮件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/test")
async def test_email_connection():
    """
    测试邮件服务器连接
    """
    try:
        success = await email_service.test_connection()
        
        if success:
            return {"code": 200, "message": "邮件服务器连接正常"}
        else:
            return {"code": 500, "message": "邮件服务器连接失败"}
            
    except Exception as e:
        logger.error(f"测试邮件连接失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test-send")
async def send_test_email(to_email: EmailStr):
    """
    发送测试邮件
    """
    try:
        template_data = {
            'title': '邮件服务测试',
            'message': '这是一封测试邮件，用于验证邮件服务是否正常工作。',
            'notification_type': 'success',
            'content': '<p>如果您收到这封邮件，说明AqentCrawler的邮件服务配置正确，可以正常发送邮件。</p>',
            'key_value_data': {
                'title': '测试信息',
                'items': [
                    {'key': '测试时间', 'value': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
                    {'key': '发送状态', 'value': '成功', 'type': 'badge', 'class': 'success'},
                    {'key': '邮件服务', 'value': '正常运行', 'type': 'badge', 'class': 'success'}
                ]
            }
        }
        
        success = await email_service.send_template_email(
            to_emails=[to_email],
            template_name='notification',
            subject='[测试邮件] AqentCrawler邮件服务测试',
            template_data=template_data
        )
        
        if success:
            return {"code": 200, "message": f"测试邮件已发送到 {to_email}"}
        else:
            return {"code": 500, "message": "测试邮件发送失败"}
            
    except Exception as e:
        logger.error(f"发送测试邮件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
