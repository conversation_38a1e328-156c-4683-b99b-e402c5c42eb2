-- 数据库迁移脚本：移除PlatformAccount表和外键约束
-- 执行时间：2025-06-15
-- 目的：统一使用CrawlerAccount表，移除外键约束

-- 1. 备份PlatformAccount表数据（如果需要的话）
-- CREATE TABLE platform_accounts_backup AS SELECT * FROM platform_accounts;

-- 2. 删除外键约束（如果存在）
-- 注意：MySQL中删除外键需要知道约束名称，这里提供常见的约束名称

-- 删除crawler_accounts表的外键约束
SET FOREIGN_KEY_CHECKS = 0;

-- 尝试删除可能存在的外键约束
ALTER TABLE crawler_accounts DROP FOREIGN KEY IF EXISTS crawler_accounts_ibfk_1;
ALTER TABLE crawler_accounts DROP FOREIGN KEY IF EXISTS crawler_accounts_ibfk_2;
ALTER TABLE crawler_accounts DROP FOREIGN KEY IF EXISTS fk_crawler_accounts_platform;
ALTER TABLE crawler_accounts DROP FOREIGN KEY IF EXISTS fk_crawler_accounts_proxy;

-- 删除crawler_usage_log表的外键约束
ALTER TABLE crawler_usage_log DROP FOREIGN KEY IF EXISTS crawler_usage_log_ibfk_1;
ALTER TABLE crawler_usage_log DROP FOREIGN KEY IF EXISTS fk_crawler_usage_log_account;

-- 删除platform_accounts表的外键约束（如果存在）
ALTER TABLE platform_accounts DROP FOREIGN KEY IF EXISTS platform_accounts_ibfk_1;
ALTER TABLE platform_accounts DROP FOREIGN KEY IF EXISTS fk_platform_accounts_platform;

SET FOREIGN_KEY_CHECKS = 1;

-- 3. 修改crawler_accounts表结构，移除外键约束
-- 重新创建platform_id字段（移除外键约束）
ALTER TABLE crawler_accounts 
MODIFY COLUMN platform_id BIGINT NOT NULL,
ADD INDEX idx_platform_id (platform_id);

-- 重新创建proxy_id字段（移除外键约束）
ALTER TABLE crawler_accounts 
MODIFY COLUMN proxy_id BIGINT NULL,
ADD INDEX idx_proxy_id (proxy_id);

-- 4. 修改crawler_usage_log表结构，移除外键约束
ALTER TABLE crawler_usage_log 
MODIFY COLUMN account_id BIGINT NOT NULL,
ADD INDEX idx_account_id_new (account_id);

-- 5. 修复login_status字段的ENUM定义
ALTER TABLE crawler_accounts 
MODIFY COLUMN login_status ENUM(
    'not_logged_in', 
    'pending', 
    'logging_in',
    'logged_in', 
    'login_failed', 
    'expired',
    'error'
) DEFAULT 'not_logged_in' COMMENT '登录状态';

-- 6. 删除platform_accounts表（请确认不再需要此表的数据）
-- 注意：执行前请确保已经将需要的数据迁移到crawler_accounts表
-- DROP TABLE IF EXISTS platform_accounts;

-- 7. 验证表结构
-- 查看crawler_accounts表结构
-- DESCRIBE crawler_accounts;

-- 查看索引
-- SHOW INDEX FROM crawler_accounts;

-- 查看外键约束（应该为空）
-- SELECT 
--     TABLE_NAME,
--     COLUMN_NAME,
--     CONSTRAINT_NAME,
--     REFERENCED_TABLE_NAME,
--     REFERENCED_COLUMN_NAME
-- FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
-- WHERE TABLE_SCHEMA = 'agent_crawler'
--   AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 8. 数据一致性检查
-- 检查是否有无效的platform_id
-- SELECT DISTINCT ca.platform_id, p.id, p.name
-- FROM crawler_accounts ca
-- LEFT JOIN platforms p ON ca.platform_id = p.id
-- WHERE p.id IS NULL;

-- 检查是否有无效的proxy_id
-- SELECT DISTINCT ca.proxy_id, pp.id, pp.name
-- FROM crawler_accounts ca
-- LEFT JOIN proxy_pools pp ON ca.proxy_id = pp.id
-- WHERE ca.proxy_id IS NOT NULL AND pp.id IS NULL;

-- 9. 清理无效数据（可选）
-- 删除无效的platform_id记录
-- DELETE FROM crawler_accounts 
-- WHERE platform_id NOT IN (SELECT id FROM platforms);

-- 将无效的proxy_id设置为NULL
-- UPDATE crawler_accounts 
-- SET proxy_id = NULL 
-- WHERE proxy_id IS NOT NULL 
--   AND proxy_id NOT IN (SELECT id FROM proxy_pools);

-- 10. 优化表
-- OPTIMIZE TABLE crawler_accounts;
-- OPTIMIZE TABLE crawler_usage_log;
-- OPTIMIZE TABLE platforms;

-- 迁移完成提示
SELECT '数据库迁移脚本执行完成！' as message;
SELECT '请验证以下内容：' as reminder;
SELECT '1. crawler_accounts表不再有外键约束' as step1;
SELECT '2. login_status字段支持所有必要的状态值' as step2;
SELECT '3. 应用程序可以正常运行' as step3;
SELECT '4. 监控功能正常工作' as step4;
SELECT '5. 如果确认无问题，可以删除platform_accounts表' as step5;
