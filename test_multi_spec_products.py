#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多规格商品解析
"""

import requests
import json
import time

def test_product_detail(url, description):
    """测试单个商品详情"""
    print(f"\n{'='*60}")
    print(f"🔍 测试: {description}")
    print(f"🔗 URL: {url}")
    print(f"{'='*60}")
    
    data = {
        "product_url": url,
        "language": "zh"
    }
    
    try:
        print(f"📡 发送请求到: http://localhost:8000/api/v1/upstream/detail")
        response = requests.post("http://localhost:8000/api/v1/upstream/detail", json=data, timeout=60)
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text[:200]}...")
        result = response.json()
        
        if result.get('code') == 200:
            product_data = result.get('data', {})
            
            print(f"\n📦 商品基本信息:")
            print(f"  - ID: {product_data.get('id')}")
            print(f"  - 名称: {product_data.get('name', '')[:50]}...")
            print(f"  - 图片数量: {len(product_data.get('sliderPicUrls', []))}")
            print(f"  - 规格类型: {product_data.get('specType')}")
            
            # 分析SKU信息
            skus = product_data.get('skus', [])
            print(f"\n📋 SKU分析 (共{len(skus)}个):")
            
            if skus:
                # 分析前5个SKU的属性结构
                for i, sku in enumerate(skus[:5]):
                    properties = sku.get('properties', [])
                    print(f"  SKU {i+1}: {len(properties)}个属性")
                    for j, prop in enumerate(properties):
                        print(f"    - {prop.get('propertyName')}={prop.get('valueName')}")
                
                # 统计属性维度
                all_properties = {}
                for sku in skus:
                    for prop in sku.get('properties', []):
                        prop_name = prop.get('propertyName')
                        prop_value = prop.get('valueName')
                        if prop_name not in all_properties:
                            all_properties[prop_name] = set()
                        all_properties[prop_name].add(prop_value)
                
                print(f"\n📊 属性维度分析:")
                for prop_name, values in all_properties.items():
                    print(f"  - {prop_name}: {len(values)}个值")
                    if len(values) <= 10:  # 如果值不多，显示所有值
                        print(f"    值: {', '.join(sorted(values))}")
                    else:  # 如果值很多，只显示前几个
                        sample_values = list(sorted(values))[:5]
                        print(f"    值: {', '.join(sample_values)}... (共{len(values)}个)")
                
                # 判断是否为多规格
                is_multi_spec = len(all_properties) > 1
                print(f"\n🎯 规格判断: {'多规格' if is_multi_spec else '单规格'} ({len(all_properties)}个属性维度)")
                
            else:
                print("  ❌ 没有找到SKU数据")
            
            # 分析props信息
            props = product_data.get('props', [])
            print(f"\n📋 Props分析 (共{len(props)}个):")
            for prop in props:
                values = prop.get('values', [])
                print(f"  - {prop.get('name')}: {len(values)}个值")
                
        else:
            print(f"❌ 请求失败: {result.get('code')}")
            print(f"错误信息: {result.get('message')}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 开始测试多规格商品解析...")
    
    # 测试商品列表
    test_products = [
        {
            "url": "https://item.taobao.com/item.htm?id=611817583009",
            "description": "淘宝单规格: 16个颜色分类的印度小叶紫檀手串"
        },
        {
            "url": "https://item.taobao.com/item.htm?id=616766692749", 
            "description": "淘宝多规格: 2个颜色3个尺寸的手串"
        },
        {
            "url": "https://detail.1688.com/offer/896739779373.html",
            "description": "1688单规格: 多种颜色的手提手机包"
        },
        {
            "url": "https://detail.1688.com/offer/932222752479.html",
            "description": "1688多规格: 14个颜色和12个适用型号的手机壳"
        }
    ]
    
    # 逐个测试
    for i, product in enumerate(test_products):
        test_product_detail(product["url"], product["description"])
        
        # 在测试之间添加延迟，避免请求过快
        if i < len(test_products) - 1:
            print(f"\n⏳ 等待3秒后继续下一个测试...")
            time.sleep(3)
    
    print(f"\n{'='*60}")
    print("✅ 所有测试完成")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
