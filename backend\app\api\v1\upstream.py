#!/usr/bin/env python3
"""
上游API接口 - 为代购系统提供标准化的商品搜索和详情获取服务
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Request
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import hashlib
import json
import logging
import os
import uuid
from sqlalchemy.orm import Session

from app.database import get_db
from app.services.translation_service import translation_service
from app.services.cache_service import cache_service
from app.crawler.search_manager import SearchManager
from app.crawler.detail_manager import DetailManager
from app.models import ApiCallLog, Platform
from app.services.alert_service import alert_service, AlertLevel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/upstream", tags=["上游API"])

# ==================== 请求模型 ====================

class SearchRequest(BaseModel):
    """商品搜索请求"""
    keyword: str = Field(..., description="搜索关键词", min_length=1, max_length=200)
    platform: str = Field(..., description="平台名称", pattern="^(taobao|tmall|1688|jd|pdd)$")
    language: str = Field(default="zh", description="请求语言", pattern="^(zh|en|fr|de|es|it|ja|ko)$")
    page: int = Field(default=1, description="页码", ge=1, le=10)
    page_size: int = Field(default=20, description="每页数量", ge=1, le=50)
    sort: str = Field(default="default", description="排序方式", pattern="^(default|price_asc|price_desc|sales|newest)$")
    price_min: Optional[float] = Field(None, description="最低价格", ge=0)
    price_max: Optional[float] = Field(None, description="最高价格", ge=0)

class ProductDetailRequest(BaseModel):
    """商品详情请求"""
    product_url: str = Field(..., description="商品链接", min_length=10, max_length=500)
    language: str = Field(default="zh", description="请求语言", pattern="^(zh|en|fr|de|es|it|ja|ko)$")

# ==================== 响应模型 ====================

class ProductInfo(BaseModel):
    """商品信息"""
    id: str = Field(..., description="商品ID")
    name: str = Field(..., description="商品名称")
    introduction: str = Field(..., description="商品介绍")
    price: int = Field(..., description="价格(分)")
    marketPrice: int = Field(..., description="市场价(分)")
    picUrl: str = Field(..., description="主图URL")
    sliderPicUrls: List[str] = Field(..., description="轮播图URLs")
    shopName: str = Field(..., description="店铺名称")
    source: str = Field(..., description="来源平台")
    sourceLink: str = Field(..., description="原始链接")
    stock: int = Field(..., description="库存数量")
    salesCount: int = Field(..., description="销量")
    freight: int = Field(..., description="运费(分)")
    scores: float = Field(..., description="评分")

class ProductDetail(ProductInfo):
    """商品详情"""
    description: str = Field(..., description="商品详情描述")
    keyword: str = Field(..., description="关键词")
    categoryId: int = Field(..., description="分类ID")
    specType: bool = Field(..., description="是否有规格")
    type: int = Field(..., description="商品类型")
    newest: bool = Field(..., description="是否最新")
    sale: bool = Field(..., description="是否在售")
    hot: bool = Field(..., description="是否热门")
    skus: List[Dict[str, Any]] = Field(..., description="SKU列表")
    props: List[Dict[str, Any]] = Field(..., description="商品属性")

class SearchResponse(BaseModel):
    """搜索响应"""
    code: int = Field(..., description="响应码")
    message: str = Field(..., description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")

class DetailResponse(BaseModel):
    """详情响应"""
    code: int = Field(..., description="响应码")
    message: str = Field(..., description="响应消息")
    data: Optional[ProductDetail] = Field(None, description="商品详情")

# ==================== 辅助函数 ====================

def generate_cache_key(request_type: str, **kwargs) -> str:
    """生成缓存键"""
    key_data = f"{request_type}:" + ":".join([f"{k}={v}" for k, v in sorted(kwargs.items())])
    return hashlib.md5(key_data.encode()).hexdigest()

async def log_api_call(
    db: Session,
    request_id: str,
    client_ip: str,
    user_agent: str,
    request_method: str,
    request_path: str,
    platform: str,
    search_type: str,
    search_query: str,
    response_code: int,
    response_time_ms: int,
    response_size: int = None,
    proxy_used: str = None,
    account_used: str = None,
    translation_used: str = None,
    error_type: str = None,
    error_message: str = None
):
    """记录API调用日志"""
    try:
        # 查找平台ID
        platform_id = None
        if platform:
            platform_obj = db.query(Platform).filter(Platform.code == platform).first()
            if platform_obj:
                platform_id = platform_obj.id

        log_entry = ApiCallLog(
            request_id=request_id,
            client_ip=client_ip,
            user_agent=user_agent,
            request_method=request_method,
            request_path=request_path,
            platform_id=platform_id,
            search_type=search_type,
            search_query=search_query,
            response_code=response_code,
            response_time_ms=response_time_ms,
            response_size=response_size,
            proxy_used=proxy_used,
            account_used=account_used,
            translation_used=translation_used,
            error_type=error_type,
            error_message=error_message
        )

        db.add(log_entry)
        db.commit()
        logger.debug(f"API调用日志记录成功: {request_id}")

        # 检查是否需要发送告警
        if response_code >= 500:
            # 系统错误告警
            import asyncio
            asyncio.create_task(_send_api_error_alert(
                platform, search_type, error_type, error_message, response_time_ms
            ))
        elif response_time_ms > 10000:  # 响应时间超过10秒
            # 性能告警
            import asyncio
            asyncio.create_task(_send_api_performance_alert(
                platform, search_type, response_time_ms
            ))

    except Exception as e:
        logger.error(f"记录API调用日志失败: {str(e)}")
        db.rollback()

async def translate_if_needed(text: str, source_lang: str, target_lang: str) -> str:
    """根据需要进行翻译"""
    if source_lang == target_lang:
        return text
    
    try:
        translated = await translation_service.translate(text, source_lang, target_lang)
        return translated
    except Exception as e:
        logger.warning(f"翻译失败: {str(e)}, 返回原文")
        return text

def is_valid_search_data(data: Any) -> bool:
    """验证搜索结果数据是否有效，值得缓存"""
    if not data or not isinstance(data, dict):
        return False

    # 检查是否包含商品列表 - 优先检查list字段，然后是products字段
    products = data.get("list", [])
    if not isinstance(products, list) or len(products) == 0:
        # 如果list字段为空，尝试products字段（向后兼容）
        products = data.get("products", [])
        if not isinstance(products, list) or len(products) == 0:
            return False

    # 检查商品数据质量
    valid_products = 0
    for product in products:
        if isinstance(product, dict) and product.get("name") and product.get("id"):
            valid_products += 1

    # 至少要有一个有效商品才缓存
    return valid_products > 0

def is_valid_detail_data(data: Any) -> bool:
    """验证商品详情数据是否有效，值得缓存"""
    if not data or not isinstance(data, dict):
        return False

    # 检查必要字段
    required_fields = ["id", "name"]
    for field in required_fields:
        if not data.get(field):
            return False

    # 检查ID是否为有效的商品ID（字符串或大于0的数字）
    product_id = data.get("id")
    if isinstance(product_id, str):
        return len(product_id) > 0
    elif isinstance(product_id, (int, float)):
        return product_id > 0

    return False

async def translate_product_info(product: Dict[str, Any], target_lang: str) -> Dict[str, Any]:
    """翻译商品信息"""
    if target_lang == "zh":
        return product

    try:
        # 翻译商品名称和介绍
        if product.get("name"):
            product["name"] = await translate_if_needed(product["name"], "zh", target_lang)

        if product.get("introduction"):
            product["introduction"] = await translate_if_needed(product["introduction"], "zh", target_lang)

        # 翻译店铺名称
        if product.get("shopName"):
            product["shopName"] = await translate_if_needed(product["shopName"], "zh", target_lang)

        # 翻译SKU属性
        if product.get("skus"):
            for sku in product["skus"]:
                if sku.get("properties"):
                    for prop in sku["properties"]:
                        if prop.get("propertyName"):
                            prop["propertyName"] = await translate_if_needed(prop["propertyName"], "zh", target_lang)
                        if prop.get("valueName"):
                            prop["valueName"] = await translate_if_needed(prop["valueName"], "zh", target_lang)

        # 翻译商品属性
        if product.get("props"):
            for prop in product["props"]:
                if prop.get("name"):
                    prop["name"] = await translate_if_needed(prop["name"], "zh", target_lang)
                if prop.get("value"):
                    prop["value"] = await translate_if_needed(prop["value"], "zh", target_lang)

        return product

    except Exception as e:
        logger.warning(f"翻译商品信息失败: {str(e)}")
        return product

# ==================== API接口 ====================

@router.post("/search", response_model=SearchResponse, summary="商品搜索")
async def search_products(
    request: SearchRequest,
    client_request: Request,
    db: Session = Depends(get_db)
):
    """
    根据关键词搜索商品

    - **query**: 搜索关键词
    - **platform**: 平台名称 (taobao/tmall/1688/jd/pdd)
    - **language**: 请求语言 (zh/en/fr/de/es/it/ja/ko)
    - **page**: 页码 (1-10)
    - **page_size**: 每页数量 (1-50)
    - **sort**: 排序方式 (default/price_asc/price_desc/sales/newest)
    - **price_min**: 最低价格
    - **price_max**: 最高价格
    """
    start_time = datetime.now()
    client_ip = client_request.client.host
    
    try:
        # 1. 生成缓存键
        cache_key = generate_cache_key(
            "search",
            query=request.keyword,
            platform=request.platform,
            language=request.language,
            page=request.page,
            page_size=request.page_size,
            sort=request.sort,
            price_min=request.price_min,
            price_max=request.price_max
        )
        
        # 2. 检查缓存
        cached_result = await cache_service.get(cache_key)
        if cached_result:
            logger.info(f"缓存命中: {cache_key}")
            return SearchResponse(
                code=200,
                message="搜索成功 (缓存)",
                data=cached_result
            )
        
        # 3. 翻译关键词
        search_keyword = request.keyword
        if request.language != "zh":
            search_keyword = await translate_if_needed(request.keyword, request.language, "zh")
            logger.info(f"关键词翻译: {request.keyword} -> {search_keyword}")
        
        # 4. 执行搜索
        search_manager = SearchManager(client_ip=client_ip)
        search_result = await search_manager.search_products(
            query=search_keyword,
            platform=request.platform,
            page=request.page,
            page_size=request.page_size,
            sort=request.sort,
            price_min=request.price_min,
            price_max=request.price_max
        )
        
        if not search_result.get("success"):
            raise HTTPException(
                status_code=400,
                detail=search_result.get("message", "搜索失败")
            )
        
        # 5. 翻译搜索结果
        products = search_result.get("data", {}).get("products", [])
        if request.language != "zh":
            for product in products:
                await translate_product_info(product, request.language)
        
        # 6. 验证并缓存结果 (只缓存有效数据)
        search_data = search_result["data"]
        if is_valid_search_data(search_data):
            import os
            cache_ttl = int(os.getenv("CACHE_SEARCH_TTL", "1800"))
            await cache_service.set(cache_key, search_data, expire=cache_ttl)
            logger.info(f"✅ 搜索结果已缓存: {len(search_data.get('products', []))} 个商品")
        else:
            logger.warning(f"⚠️ 搜索结果无效，跳过缓存: {search_data}")
        
        # 7. 记录API调用日志
        response_time = int((datetime.now() - start_time).total_seconds() * 1000)
        request_id = str(uuid.uuid4())
        user_agent = client_request.headers.get("user-agent", "")

        await log_api_call(
            db=db,
            request_id=request_id,
            client_ip=client_ip,
            user_agent=user_agent,
            request_method="POST",
            request_path="/api/v1/upstream/search",
            platform=request.platform,
            search_type="product_search",
            search_query=request.keyword,
            response_code=200,
            response_time_ms=response_time,
            response_size=len(json.dumps(search_result["data"], ensure_ascii=False)),
            translation_used="yes" if request.language != "zh" else "no"
        )

        return SearchResponse(
            code=200,
            message="搜索成功",
            data=search_result["data"]
        )
        
    except HTTPException as he:
        # 记录HTTP异常日志
        response_time = int((datetime.now() - start_time).total_seconds() * 1000)
        request_id = str(uuid.uuid4())
        user_agent = client_request.headers.get("user-agent", "")

        await log_api_call(
            db=db,
            request_id=request_id,
            client_ip=client_ip,
            user_agent=user_agent,
            request_method="POST",
            request_path="/api/v1/upstream/search",
            platform=request.platform,
            search_type="product_search",
            search_query=request.keyword,
            response_code=he.status_code,
            response_time_ms=response_time,
            error_type="HTTPException",
            error_message=str(he.detail)
        )
        raise
    except Exception as e:
        # 记录系统异常日志
        response_time = int((datetime.now() - start_time).total_seconds() * 1000)
        request_id = str(uuid.uuid4())
        user_agent = client_request.headers.get("user-agent", "")

        await log_api_call(
            db=db,
            request_id=request_id,
            client_ip=client_ip,
            user_agent=user_agent,
            request_method="POST",
            request_path="/api/v1/upstream/search",
            platform=request.platform,
            search_type="product_search",
            search_query=request.keyword,
            response_code=500,
            response_time_ms=response_time,
            error_type="Exception",
            error_message=str(e)
        )

        logger.error(f"搜索商品失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.post("/detail", response_model=DetailResponse, summary="商品详情")
async def get_product_detail(
    request: ProductDetailRequest,
    client_request: Request,
    db: Session = Depends(get_db)
):
    """
    根据商品链接获取详情
    
    - **product_url**: 商品链接
    - **language**: 请求语言 (zh/en/fr/de/es/it/ja/ko)
    """
    start_time = datetime.now()
    client_ip = client_request.client.host
    
    try:
        # 1. 生成缓存键
        cache_key = generate_cache_key(
            "detail",
            product_url=request.product_url,
            language=request.language
        )
        
        # 2. 检查缓存
        cached_result = await cache_service.get(cache_key)
        if cached_result:
            logger.info(f"🔍 缓存命中: {cache_key}")
            logger.info(f"🔍 缓存数据类型: {type(cached_result)}")
            logger.info(f"🔍 缓存数据: {cached_result}")
            if cached_result and isinstance(cached_result, dict):
                logger.info(f"🔍 缓存数据ID: {cached_result.get('id')} (类型: {type(cached_result.get('id'))})")

            # 检查缓存数据的ID类型，如果是整数则清除缓存
            if cached_result and isinstance(cached_result, dict) and isinstance(cached_result.get('id'), int):
                logger.warning(f"🚨 发现缓存数据ID为整数类型，清除缓存: {cache_key}")
                await cache_service.delete(cache_key)
            else:
                return DetailResponse(
                    code=200,
                    message="获取详情成功 (缓存)",
                    data=cached_result
                )
        
        # 3. 执行详情获取
        detail_manager = DetailManager()
        detail_result = await detail_manager.get_product_detail(request.product_url)

        logger.info(f"🔍 详情管理器返回结果类型: {type(detail_result)}")
        logger.info(f"🔍 详情管理器返回结果: {detail_result}")

        success_value = detail_result.get("success")
        logger.info(f"🔍 success字段值: {success_value} (类型: {type(success_value)})")

        if not detail_result.get("success"):
            logger.info(f"🔍 详情获取失败，准备抛出异常")
            raise HTTPException(
                status_code=400,
                detail=detail_result.get("message", "获取详情失败")
            )

        logger.info(f"🔍 success检查通过，继续处理...")

        product_detail = detail_result.get("data")
        logger.info(f"🔍 获取到的商品详情数据类型: {type(product_detail)}")
        logger.info(f"🔍 获取到的商品详情数据: {product_detail}")

        if product_detail:
            logger.info(f"🔍 商品详情ID: {product_detail.get('id')} (类型: {type(product_detail.get('id'))})")

        if not product_detail:
            logger.info(f"🔍 商品详情数据为空，抛出404异常")
            raise HTTPException(status_code=404, detail="商品不存在")

        logger.info(f"🔍 准备创建DetailResponse对象...")
        
        # 4. 翻译商品详情
        if request.language != "zh":
            await translate_product_info(product_detail, request.language)
        
        # 5. 验证并缓存结果 (只缓存有效数据)
        if is_valid_detail_data(product_detail):
            import os
            cache_ttl = int(os.getenv("CACHE_DETAIL_TTL", "3600"))
            await cache_service.set(cache_key, product_detail, expire=cache_ttl)
            logger.info(f"✅ 商品详情已缓存: ID={product_detail.get('id')}, 名称={product_detail.get('name', '')[:20]}...")
        else:
            logger.warning(f"⚠️ 商品详情无效，跳过缓存: {product_detail}")
        
        # 6. 记录API调用日志
        response_time = int((datetime.now() - start_time).total_seconds() * 1000)
        request_id = str(uuid.uuid4())
        user_agent = client_request.headers.get("user-agent", "")

        # 从URL中提取平台信息
        platform = "unknown"
        if "taobao.com" in request.product_url or "tmall.com" in request.product_url:
            platform = "taobao"
        elif "1688.com" in request.product_url:
            platform = "1688"
        elif "jd.com" in request.product_url:
            platform = "jd"
        elif "yangkeduo.com" in request.product_url:
            platform = "pdd"

        await log_api_call(
            db=db,
            request_id=request_id,
            client_ip=client_ip,
            user_agent=user_agent,
            request_method="POST",
            request_path="/api/v1/upstream/detail",
            platform=platform,
            search_type="product_detail",
            search_query=request.product_url,
            response_code=200,
            response_time_ms=response_time,
            response_size=len(json.dumps(product_detail, ensure_ascii=False)),
            translation_used="yes" if request.language != "zh" else "no"
        )

        return DetailResponse(
            code=200,
            message="获取详情成功",
            data=product_detail
        )
        
    except HTTPException as he:
        # 记录HTTP异常日志
        response_time = int((datetime.now() - start_time).total_seconds() * 1000)
        request_id = str(uuid.uuid4())
        user_agent = client_request.headers.get("user-agent", "")

        # 从URL中提取平台信息
        platform = "unknown"
        if "taobao.com" in request.product_url or "tmall.com" in request.product_url:
            platform = "taobao"
        elif "1688.com" in request.product_url:
            platform = "1688"
        elif "jd.com" in request.product_url:
            platform = "jd"
        elif "yangkeduo.com" in request.product_url:
            platform = "pdd"

        await log_api_call(
            db=db,
            request_id=request_id,
            client_ip=client_ip,
            user_agent=user_agent,
            request_method="POST",
            request_path="/api/v1/upstream/detail",
            platform=platform,
            search_type="product_detail",
            search_query=request.product_url,
            response_code=he.status_code,
            response_time_ms=response_time,
            error_type="HTTPException",
            error_message=str(he.detail)
        )
        raise
    except Exception as e:
        # 记录系统异常日志
        response_time = int((datetime.now() - start_time).total_seconds() * 1000)
        request_id = str(uuid.uuid4())
        user_agent = client_request.headers.get("user-agent", "")

        # 从URL中提取平台信息
        platform = "unknown"
        if "taobao.com" in request.product_url or "tmall.com" in request.product_url:
            platform = "taobao"
        elif "1688.com" in request.product_url:
            platform = "1688"
        elif "jd.com" in request.product_url:
            platform = "jd"
        elif "yangkeduo.com" in request.product_url:
            platform = "pdd"

        await log_api_call(
            db=db,
            request_id=request_id,
            client_ip=client_ip,
            user_agent=user_agent,
            request_method="POST",
            request_path="/api/v1/upstream/detail",
            platform=platform,
            search_type="product_detail",
            search_query=request.product_url,
            response_code=500,
            response_time_ms=response_time,
            error_type="Exception",
            error_message=str(e)
        )

        logger.error(f"获取商品详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取详情失败: {str(e)}")

@router.get("/health", summary="健康检查")
async def health_check():
    """健康检查接口"""
    return {
        "code": 200,
        "message": "服务正常",
        "data": {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0"
        }
    }

@router.get("/stats", summary="服务统计")
async def get_stats(db: Session = Depends(get_db)):
    """获取服务统计信息"""
    try:
        # TODO: 从数据库获取统计信息
        stats = {
            "total_requests": 0,
            "cache_hit_rate": 0.0,
            "average_response_time": 0,
            "supported_platforms": ["taobao", "tmall", "1688", "jd", "pdd"],
            "supported_languages": ["zh", "en", "fr", "de", "es", "it", "ja", "ko"]
        }
        
        return {
            "code": 200,
            "message": "获取统计信息成功",
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取统计信息失败")


async def _send_api_error_alert(platform: str, search_type: str, error_type: str, error_message: str, response_time_ms: int):
    """发送API错误告警"""
    try:
        api_info = {
            '平台': platform,
            '接口类型': search_type,
            '错误类型': error_type or '未知',
            '响应时间': f"{response_time_ms}ms"
        }

        error_details = {
            '错误信息': error_message or '无详细信息',
            '发生时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '影响范围': f"{platform}平台{search_type}接口"
        }

        await alert_service.send_api_alert(
            title=f"API接口错误告警",
            message=f"{platform}平台的{search_type}接口发生系统错误，需要立即处理。",
            level=AlertLevel.ERROR,
            api_info=api_info,
            error_details=error_details
        )

    except Exception as e:
        logger.error(f"发送API错误告警失败: {e}")


async def _send_api_performance_alert(platform: str, search_type: str, response_time_ms: int):
    """发送API性能告警"""
    try:
        api_info = {
            '平台': platform,
            '接口类型': search_type,
            '响应时间': f"{response_time_ms}ms",
            '性能阈值': '10000ms'
        }

        await alert_service.send_api_alert(
            title=f"API接口性能告警",
            message=f"{platform}平台的{search_type}接口响应时间过长({response_time_ms}ms)，可能影响用户体验。",
            level=AlertLevel.WARNING,
            api_info=api_info
        )

    except Exception as e:
        logger.error(f"发送API性能告警失败: {e}")
