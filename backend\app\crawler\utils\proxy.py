"""
代理管理工具

提供代理轮换和管理功能
"""

import asyncio
import random
from typing import Dict, List, Optional
from dataclasses import dataclass


@dataclass
class ProxyInfo:
    """代理信息"""
    server: str
    username: Optional[str] = None
    password: Optional[str] = None


class ProxyManager:
    """代理管理器"""
    
    def __init__(self):
        self.current_proxy_index = 0
        self.failed_proxies = set()
        self._proxy_cache = []
        self._last_refresh = 0
        self.cache_ttl = 300  # 5分钟缓存
    
    async def get_proxy(self) -> Optional[Dict[str, str]]:
        """获取可用代理"""
        try:
            # 刷新代理缓存
            await self._refresh_proxy_cache()
            
            if not self._proxy_cache:
                return None
            
            # 轮换代理
            available_proxies = [p for p in self._proxy_cache if p.server not in self.failed_proxies]
            
            if not available_proxies:
                # 重置失败代理列表
                self.failed_proxies.clear()
                available_proxies = self._proxy_cache
            
            if not available_proxies:
                return None
            
            proxy = random.choice(available_proxies)
            
            proxy_config = {
                "server": proxy.server
            }
            
            if proxy.username and proxy.password:
                proxy_config["username"] = proxy.username
                proxy_config["password"] = proxy.password
            
            return proxy_config
            
        except Exception as e:
            print(f"获取代理失败: {str(e)}")
            return None
    
    async def _refresh_proxy_cache(self):
        """刷新代理缓存"""
        import time

        current_time = time.time()
        if current_time - self._last_refresh < self.cache_ttl:
            return

        try:
            # 暂时使用免费代理池，后续可以从数据库获取
            # TODO: 集成数据库代理查询
            self._proxy_cache = [
                ProxyInfo(server=proxy)
                for proxy in FREE_PROXIES
            ]

            self._last_refresh = current_time

        except Exception as e:
            print(f"刷新代理缓存失败: {str(e)}")
    
    def mark_proxy_failed(self, proxy_server: str):
        """标记代理失败"""
        self.failed_proxies.add(proxy_server)
    
    def get_stats(self) -> Dict:
        """获取代理统计"""
        total_proxies = len(self._proxy_cache)
        failed_proxies = len(self.failed_proxies)
        available_proxies = total_proxies - failed_proxies
        
        return {
            "total_proxies": total_proxies,
            "available_proxies": available_proxies,
            "failed_proxies": failed_proxies,
            "success_rate": round(available_proxies / max(total_proxies, 1) * 100, 2)
        }


class ProxyRotator:
    """代理轮换器"""
    
    def __init__(self, proxies: List[ProxyInfo]):
        self.proxies = proxies
        self.current_index = 0
        self.failed_count = {}
        self.max_failures = 3
    
    def get_next_proxy(self) -> Optional[ProxyInfo]:
        """获取下一个代理"""
        if not self.proxies:
            return None
        
        # 找到可用的代理
        attempts = 0
        while attempts < len(self.proxies):
            proxy = self.proxies[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.proxies)
            
            # 检查代理是否失败次数过多
            if self.failed_count.get(proxy.server, 0) < self.max_failures:
                return proxy
            
            attempts += 1
        
        # 如果所有代理都失败了，重置失败计数
        self.failed_count.clear()
        return self.proxies[0] if self.proxies else None
    
    def mark_failure(self, proxy_server: str):
        """标记代理失败"""
        self.failed_count[proxy_server] = self.failed_count.get(proxy_server, 0) + 1
    
    def mark_success(self, proxy_server: str):
        """标记代理成功"""
        if proxy_server in self.failed_count:
            self.failed_count[proxy_server] = max(0, self.failed_count[proxy_server] - 1)


# 免费代理池（仅用于测试）
FREE_PROXIES = [
    # 注意：这些是示例代理，实际使用时需要替换为真实可用的代理
    # "http://proxy1.example.com:8080",
    # "http://proxy2.example.com:8080",
    # "http://proxy3.example.com:8080",
]


async def test_proxy(proxy_config: Dict[str, str]) -> bool:
    """测试代理可用性"""
    try:
        import aiohttp
        
        timeout = aiohttp.ClientTimeout(total=10)
        connector = aiohttp.TCPConnector(limit=1)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        ) as session:
            
            proxy_url = proxy_config.get("server")
            if not proxy_url.startswith("http"):
                proxy_url = f"http://{proxy_url}"
            
            async with session.get(
                "http://httpbin.org/ip",
                proxy=proxy_url
            ) as response:
                if response.status == 200:
                    return True
                return False
                
    except Exception:
        return False
