#!/usr/bin/env python3
"""
缓存服务 - 支持Redis和内存缓存
"""

import json
import logging
import asyncio
from typing import Any, Optional, Dict
from datetime import datetime, timedelta
import pickle
import hashlib

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)

class CacheService:
    """缓存服务"""
    
    def __init__(self):
        self.redis_client = None
        self.memory_cache: Dict[str, Dict] = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0
        }
        
        # 内存缓存清理任务
        self._cleanup_task = None
        
    async def initialize(self, redis_url: str = None):
        """初始化缓存服务"""
        if REDIS_AVAILABLE and redis_url:
            try:
                self.redis_client = redis.from_url(redis_url, decode_responses=False)
                # 测试连接
                await self.redis_client.ping()
                logger.info("Redis缓存服务初始化成功")
            except Exception as e:
                logger.warning(f"Redis连接失败，使用内存缓存: {str(e)}")
                self.redis_client = None
        
        if not self.redis_client:
            logger.info("使用内存缓存服务")
            # 启动内存缓存清理任务
            self._cleanup_task = asyncio.create_task(self._cleanup_memory_cache())
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            if self.redis_client:
                value = await self._redis_get(key)
            else:
                value = await self._memory_get(key)
            
            if value is not None:
                self.cache_stats["hits"] += 1
                logger.debug(f"缓存命中: {key}")
                return value
            else:
                self.cache_stats["misses"] += 1
                logger.debug(f"缓存未命中: {key}")
                return None
                
        except Exception as e:
            logger.error(f"获取缓存失败: {key}, 错误: {str(e)}")
            self.cache_stats["misses"] += 1
            return None
    
    async def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        """设置缓存值"""
        try:
            if self.redis_client:
                success = await self._redis_set(key, value, expire)
            else:
                success = await self._memory_set(key, value, expire)
            
            if success:
                self.cache_stats["sets"] += 1
                logger.debug(f"缓存设置成功: {key}, 过期时间: {expire}秒")
            
            return success
            
        except Exception as e:
            logger.error(f"设置缓存失败: {key}, 错误: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            if self.redis_client:
                success = await self._redis_delete(key)
            else:
                success = await self._memory_delete(key)
            
            if success:
                self.cache_stats["deletes"] += 1
                logger.debug(f"缓存删除成功: {key}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除缓存失败: {key}, 错误: {str(e)}")
            return False
    
    async def clear(self, pattern: str = None) -> bool:
        """清空缓存"""
        try:
            if self.redis_client:
                if pattern:
                    keys = await self.redis_client.keys(pattern)
                    if keys:
                        await self.redis_client.delete(*keys)
                else:
                    await self.redis_client.flushdb()
            else:
                if pattern:
                    # 简单的模式匹配
                    keys_to_delete = [k for k in self.memory_cache.keys() if pattern.replace("*", "") in k]
                    for key in keys_to_delete:
                        del self.memory_cache[key]
                else:
                    self.memory_cache.clear()
            
            logger.info(f"缓存清空成功: {pattern or '全部'}")
            return True
            
        except Exception as e:
            logger.error(f"清空缓存失败: {str(e)}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = self.cache_stats.copy()
        
        # 计算命中率
        total_requests = stats["hits"] + stats["misses"]
        if total_requests > 0:
            stats["hit_rate"] = stats["hits"] / total_requests
        else:
            stats["hit_rate"] = 0.0
        
        # 添加缓存类型和大小信息
        if self.redis_client:
            stats["cache_type"] = "redis"
            try:
                info = await self.redis_client.info("memory")
                stats["memory_usage"] = info.get("used_memory_human", "unknown")
            except:
                stats["memory_usage"] = "unknown"
        else:
            stats["cache_type"] = "memory"
            stats["cache_size"] = len(self.memory_cache)
        
        return stats
    
    # ==================== Redis实现 ====================
    
    async def _redis_get(self, key: str) -> Optional[Any]:
        """Redis获取"""
        value = await self.redis_client.get(key)
        if value is None:
            return None
        
        try:
            # 尝试反序列化
            return pickle.loads(value)
        except:
            # 如果反序列化失败，尝试JSON
            try:
                return json.loads(value.decode())
            except:
                return value.decode()
    
    async def _redis_set(self, key: str, value: Any, expire: int) -> bool:
        """Redis设置"""
        try:
            # 序列化值
            serialized_value = pickle.dumps(value)
            await self.redis_client.setex(key, expire, serialized_value)
            return True
        except Exception as e:
            logger.error(f"Redis设置失败: {str(e)}")
            return False
    
    async def _redis_delete(self, key: str) -> bool:
        """Redis删除"""
        result = await self.redis_client.delete(key)
        return result > 0
    
    # ==================== 内存缓存实现 ====================
    
    async def _memory_get(self, key: str) -> Optional[Any]:
        """内存缓存获取"""
        if key not in self.memory_cache:
            return None
        
        cache_item = self.memory_cache[key]
        
        # 检查是否过期
        if cache_item["expire_time"] < datetime.now():
            del self.memory_cache[key]
            return None
        
        return cache_item["value"]
    
    async def _memory_set(self, key: str, value: Any, expire: int) -> bool:
        """内存缓存设置"""
        expire_time = datetime.now() + timedelta(seconds=expire)
        self.memory_cache[key] = {
            "value": value,
            "expire_time": expire_time,
            "created_time": datetime.now()
        }
        return True
    
    async def _memory_delete(self, key: str) -> bool:
        """内存缓存删除"""
        if key in self.memory_cache:
            del self.memory_cache[key]
            return True
        return False
    
    async def _cleanup_memory_cache(self):
        """清理过期的内存缓存"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                
                current_time = datetime.now()
                expired_keys = []
                
                for key, cache_item in self.memory_cache.items():
                    if cache_item["expire_time"] < current_time:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self.memory_cache[key]
                
                if expired_keys:
                    logger.debug(f"清理过期缓存: {len(expired_keys)}个")
                    
            except Exception as e:
                logger.error(f"清理内存缓存失败: {str(e)}")
    
    async def close(self):
        """关闭缓存服务"""
        if self.redis_client:
            await self.redis_client.close()
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass

# 创建全局实例
cache_service = CacheService()
