# 淘宝配置提取器 Chrome 扩展

## 📁 文件结构

```
chrome-extension/
├── manifest.json          # 扩展配置文件
├── popup.html             # 弹窗界面
├── popup.js               # 弹窗脚本
├── content.js             # 内容脚本（在淘宝页面运行）
├── background.js          # 后台脚本
├── settings.html          # 设置页面
├── settings.js            # 设置页面脚本
├── styles.css             # 样式文件
├── icons/                 # 图标文件夹
│   ├── icon16.png         # 16x16 图标
│   ├── icon32.png         # 32x32 图标
│   ├── icon48.png         # 48x48 图标
│   └── icon128.png        # 128x128 图标
└── README.md              # 说明文档
```

## 🚀 安装步骤

### 1. 创建图标文件
由于我无法直接创建图片文件，您需要手动创建图标：

1. 创建 `icons` 文件夹
2. 准备以下尺寸的图标文件：
   - `icon16.png` (16x16 像素)
   - `icon32.png` (32x32 像素)
   - `icon48.png` (48x48 像素)
   - `icon128.png` (128x128 像素)

**图标建议**：
- 使用橙色主题（#ff6600）
- 包含爬虫或淘宝相关元素
- 简洁明了的设计

### 2. 安装扩展

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `chrome-extension` 文件夹
   - 点击"选择文件夹"

4. **验证安装**
   - 扩展应该出现在扩展列表中
   - 浏览器工具栏会显示扩展图标

## 🔧 使用方法

### 基本使用

1. **访问淘宝网站**
   - 打开 taobao.com 或 tmall.com
   - 登录您的淘宝账号

2. **提取配置**
   - 点击浏览器工具栏中的扩展图标
   - 在弹窗中点击"提取配置"按钮
   - 等待提取完成

3. **查看结果**
   - 提取成功后会显示配置预览
   - 配置会自动发送到后端服务

### 高级设置

1. **打开设置页面**
   - 点击扩展弹窗中的"设置"按钮
   - 或者在扩展管理页面点击"详细信息" → "扩展程序选项"

2. **配置后端地址**
   - 设置您的后端服务地址（默认：http://localhost:8000）
   - 点击"测试连接"验证连接状态

3. **启用自动提取**（可选）
   - 勾选"启用自动提取"
   - 设置自动提取间隔时间

## 🛠️ 功能特性

### 核心功能
- ✅ 自动检测淘宝登录状态
- ✅ 提取Cookie和Token信息
- ✅ 验证配置完整性
- ✅ 自动发送到后端服务
- ✅ 本地配置备份

### 智能检测
- ✅ 多种Token提取方法
- ✅ 用户名自动识别
- ✅ 登录状态检测
- ✅ 配置有效性验证

### 用户体验
- ✅ 直观的图形界面
- ✅ 实时状态显示
- ✅ 错误提示和帮助
- ✅ 配置历史记录

## 🔍 故障排除

### 常见问题

**1. 扩展无法加载**
- 检查文件结构是否完整
- 确保所有图标文件存在
- 查看Chrome控制台错误信息

**2. 无法提取Token**
- 确保已登录淘宝账号
- 刷新页面后重试
- 检查浏览器控制台错误

**3. 无法连接后端**
- 检查后端服务是否运行
- 验证后端地址是否正确
- 检查防火墙和网络设置

**4. 配置提取失败**
- 确保在淘宝页面使用
- 检查登录状态
- 查看扩展控制台日志

### 调试方法

1. **查看扩展日志**
   ```
   Chrome → 扩展管理 → 淘宝配置提取器 → 详细信息 → 检查视图
   ```

2. **查看页面控制台**
   ```
   F12 → Console → 查看错误信息
   ```

3. **检查网络请求**
   ```
   F12 → Network → 查看API请求状态
   ```

## 📝 开发说明

### 文件说明

- **manifest.json**: 扩展的配置文件，定义权限和入口点
- **popup.html/js**: 点击扩展图标时显示的弹窗界面
- **content.js**: 注入到淘宝页面的脚本，负责提取配置
- **background.js**: 后台服务脚本，处理扩展逻辑
- **settings.html/js**: 设置页面，用于配置扩展参数

### 权限说明

- `activeTab`: 访问当前活动标签页
- `storage`: 本地存储配置和历史
- `cookies`: 读取淘宝网站的Cookie
- `host_permissions`: 访问淘宝和后端服务

### 安全考虑

- 所有敏感信息仅在本地存储
- 通过HTTPS传输配置数据
- 不收集用户个人信息
- 仅在必要时访问页面内容

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查浏览器控制台错误信息
3. 确保后端服务正常运行
4. 联系技术支持团队

## 🔄 更新日志

### v1.0.0 (2024-06-11)
- ✅ 初始版本发布
- ✅ 基础配置提取功能
- ✅ 图形用户界面
- ✅ 后端服务集成
- ✅ 设置和配置管理

---

**注意**: 此扩展仅用于合法的数据采集目的，请遵守相关网站的使用条款和法律法规。
