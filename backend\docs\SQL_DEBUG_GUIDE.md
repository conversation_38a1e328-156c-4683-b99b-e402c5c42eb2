# SQL调试功能使用指南

## 概述

本项目集成了完整的SQL语句打印和调试功能，支持开发和生产环境的智能切换，方便开发者调试数据库操作问题。

## 功能特性

- ✅ **环境智能检测**：自动根据环境变量区分开发/生产环境
- ✅ **SQL语句打印**：完整显示执行的SQL语句和参数
- ✅ **执行时间统计**：显示每个SQL语句的执行时间
- ✅ **密码安全**：自动隐藏数据库连接字符串中的密码
- ✅ **日志级别控制**：支持不同级别的日志输出
- ✅ **清洁日志输出**：统一的自定义格式化日志，避免重复输出
- ✅ **启动脚本集成**：与项目启动脚本完美集成，无配置冲突

## 环境变量配置

### 配置方式

SQL调试功能通过环境变量控制，支持两种配置方式：

1. **启动脚本自动设置**（推荐）
2. **手动环境变量设置**

在 `backend/.env` 文件中配置以下参数：

```bash
# SQL调试配置
# 注意：ENVIRONMENT由启动脚本自动设置，无需在此配置
SQL_DEBUG=true          # 是否启用SQL语句打印调试
SQL_LOG_LEVEL=INFO      # SQL日志级别: DEBUG/INFO/WARNING/ERROR
```

### 环境变量优先级

系统按以下优先级读取环境变量：
1. **运行时环境变量**（启动脚本设置）- 最高优先级
2. **系统环境变量**
3. **`.env` 文件配置** - 最低优先级

### 配置说明

| 变量名 | 默认值 | 设置方式 | 说明 |
|--------|--------|----------|------|
| `ENVIRONMENT` | `development` | 启动脚本自动设置 | 环境类型：`development`(开发) 或 `production`(生产) |
| `SQL_DEBUG` | `true` | `.env`文件或环境变量 | 是否启用SQL调试：`true`(启用) 或 `false`(禁用) |
| `SQL_LOG_LEVEL` | `INFO` | `.env`文件或环境变量 | 日志级别：`DEBUG`、`INFO`、`WARNING`、`ERROR` |

**重要说明**：
- `ENVIRONMENT` 由启动脚本自动设置，**不要**在 `.env` 文件中配置
- 使用 `python start.py` 选择 `1` (开发环境) 会设置 `ENVIRONMENT=development`
- 使用 `python start.py` 选择 `2` (生产环境) 会设置 `ENVIRONMENT=production`

## 启用规则

SQL调试功能的启用遵循以下规则：

1. **开发环境** (`ENVIRONMENT=development`)
   - `SQL_DEBUG=true` → ✅ 启用SQL调试
   - `SQL_DEBUG=false` → ❌ 禁用SQL调试

2. **生产环境** (`ENVIRONMENT=production`)
   - `SQL_DEBUG=true` → ✅ 强制启用SQL调试（用于生产问题排查）
   - `SQL_DEBUG=false` → ❌ 禁用SQL调试（默认安全设置）

## 日志输出格式

### 统一的自定义格式化日志
```
[SQL] 2025-06-22 16:44:09 [INFO] 数据库连接: mysql+pymysql://root:****@192.168.2.197:3306/agent_crawler
[SQL] 2025-06-22 16:44:09 [INFO] 环境: development
[SQL] 2025-06-22 16:44:09 [INFO] SQL调试: 启用
[SQL] 2025-06-22 16:44:09 [INFO] 执行SQL: SELECT platforms.id AS platforms_id, platforms.code AS platforms_code...
[SQL] 2025-06-22 16:44:09 [INFO] 参数: {'param_1': 3}
[SQL] 2025-06-22 16:44:09 [INFO] 执行时间: 8.001ms
```

**特点**：
- 统一的 `[SQL]` 前缀，便于识别和过滤
- 清洁的输出格式，无重复日志
- 自动隐藏敏感信息（如数据库密码）
- 包含完整的SQL语句、参数和执行时间

## 使用场景

### 开发环境调试
```bash
# 使用启动脚本（推荐）
python start.py  # 选择 1 (开发环境)

# 或者在 .env 文件中配置
SQL_DEBUG=true
SQL_LOG_LEVEL=INFO
```

启动应用后，所有数据库操作都会打印详细的SQL语句，方便调试。

### 生产环境问题排查
```bash
# 方式1: 临时设置环境变量
export SQL_DEBUG=true
python start.py  # 选择 2 (生产环境)

# 方式2: 修改 .env 文件
SQL_DEBUG=true
SQL_LOG_LEVEL=WARNING
```

在生产环境遇到数据库问题时，可以临时启用SQL调试进行问题排查。

### 生产环境正常运行
```bash
# 使用启动脚本（推荐）
python start.py  # 选择 2 (生产环境)

# .env 文件配置
SQL_DEBUG=false
```

生产环境默认禁用SQL调试，确保性能和日志清洁。

## 技术实现

### 核心组件

1. **SQL日志工具** (`app/utils/sql_logger.py`)
   - 环境检测和配置管理
   - SQL语句格式化和输出
   - SQLAlchemy事件监听

2. **数据库配置** (`app/database.py`)
   - 集成SQL日志工具
   - 动态设置SQLAlchemy echo参数
   - 自动设置事件监听器

### 关键代码

```python
# 获取SQL调试设置
SQL_ECHO = get_echo_setting()

# MySQL数据库引擎
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=SQL_ECHO  # 根据环境变量动态设置SQL打印
)

# 设置SQL日志监听器
setup_sql_logging(engine)
```

## 性能影响

- **开发环境**：SQL调试对性能影响很小，主要是日志输出的I/O开销
- **生产环境**：禁用状态下几乎无性能影响
- **生产调试**：启用时会有少量性能开销，建议仅在问题排查时临时启用

## 安全考虑

1. **密码隐藏**：数据库连接字符串中的密码自动用 `****` 替换
2. **SQL长度限制**：超长SQL语句会被截断，避免日志过大
3. **生产环境控制**：生产环境默认禁用，需要明确设置才能启用

## 故障排除

### 问题：SQL调试没有输出
**解决方案**：
1. 检查环境变量设置：`ENVIRONMENT` 和 `SQL_DEBUG`
2. 确认日志级别：`SQL_LOG_LEVEL`
3. 重启应用以加载新的环境变量

### 问题：日志输出过多
**解决方案**：
1. 调整日志级别为 `WARNING` 或 `ERROR`
2. 在生产环境设置 `SQL_DEBUG=false`

### 问题：密码泄露在日志中
**解决方案**：
- 系统自动隐藏密码，如果仍有泄露请检查其他日志组件

## 最佳实践

1. **开发阶段**：始终启用SQL调试，便于发现和解决问题
2. **测试阶段**：根据需要启用，重点关注性能测试时的SQL执行情况
3. **生产部署**：默认禁用，仅在问题排查时临时启用
4. **日志管理**：定期清理SQL调试日志，避免磁盘空间不足
5. **安全审计**：定期检查生产环境的SQL调试配置，确保符合安全要求

## 相关文件

- `backend/.env` - 环境变量配置
- `backend/app/database.py` - 数据库连接配置
- `backend/app/utils/sql_logger.py` - SQL日志工具
- `backend/docs/SQL_DEBUG_GUIDE.md` - 本文档
