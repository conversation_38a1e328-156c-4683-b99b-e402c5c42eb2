"""
1688登录服务

专门处理1688平台的登录功能（不包含Token刷新）
"""

import re
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
from playwright.sync_api import Page

from .base_login import BasePlatformLogin


class Alibaba1688Login(BasePlatformLogin):
    """1688登录服务"""

    def __init__(self):
        super().__init__("1688")

    def get_login_url(self) -> str:
        """获取1688登录页面URL"""
        return "https://login.1688.com/member/signin.htm"

    def get_login_selectors(self) -> Dict[str, str]:
        """获取1688登录页面选择器"""
        return {
            "username": "#fm-login-id",  # 用户名输入框
            "password": "#fm-login-password",  # 密码输入框
            "login_button": "#fm-login-submit"  # 登录按钮
        }

    def get_success_indicators(self) -> list:
        """获取1688登录成功的URL指示器"""
        return [
            "www.1688.com",
            "work.1688.com", 
            "member.1688.com",
            "i.1688.com"
        ]

    def get_verification_indicators(self) -> list:
        """获取1688验证过程的URL指示器"""
        return [
            "checkcode", "verify", "challenge", "captcha",
            "slider", "sms", "phone", "mobile"
        ]

    def extract_token_from_cookies(self, cookies: list) -> Tuple[Optional[str], Optional[datetime]]:
        """从1688 Cookie中提取Token和过期时间"""
        token = None
        token_expires_at = None

        for cookie in cookies:
            if cookie['name'] == '_m_h5_tk':
                m_h5_tk = cookie['value']
                if '_' in m_h5_tk:
                    token_parts = m_h5_tk.split('_')
                    token = token_parts[0]
                    
                    # 提取过期时间
                    if len(token_parts) > 1:
                        try:
                            expires_timestamp = int(token_parts[1]) / 1000
                            token_expires_at = datetime.fromtimestamp(expires_timestamp)
                        except (ValueError, IndexError):
                            # 如果无法解析过期时间，设置为24小时后过期
                            token_expires_at = datetime.now() + timedelta(hours=24)
                    break

        return token, token_expires_at

    def get_critical_cookies(self) -> list:
        """获取1688关键Cookie列表"""
        return [
            'leftMenuLastMode', 'leftMenuModeTip', 'keywordsHistory',
            'plugin_home_downLoad_cookie', 'JSESSIONID', 'EGG_SESS',
            '_m_h5_tk', 'cookie2', 'wk_cookie2'
        ]

    def validate_login_state(self, page: Page) -> bool:
        """验证1688登录状态"""
        try:
            current_url = page.url
            
            # 检查是否在登录页面
            if "login.1688.com" in current_url or "passport.1688.com" in current_url:
                return False
            
            # 检查是否在成功页面
            success_indicators = self.get_success_indicators()
            if any(indicator in current_url for indicator in success_indicators):
                return True
                
            return False
            
        except Exception as e:
            print(f"验证1688登录状态失败: {str(e)}")
            return False

    def filter_cookies(self, cookie_string: str) -> str:
        """过滤1688不必要的Cookie"""
        if not cookie_string:
            return cookie_string
            
        try:
            # 分割Cookie
            cookies = cookie_string.split(';')
            critical_cookies = self.get_critical_cookies()
            
            # 只保留关键Cookie
            filtered_cookies = []
            for cookie in cookies:
                cookie = cookie.strip()
                if '=' in cookie:
                    name = cookie.split('=')[0].strip()
                    if name in critical_cookies:
                        filtered_cookies.append(cookie)
            
            return '; '.join(filtered_cookies)
            
        except Exception as e:
            print(f"过滤1688 Cookie失败: {str(e)}")
            return cookie_string
