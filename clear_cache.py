#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清除特定商品的缓存
"""

import redis
import hashlib
import sys

def generate_cache_key(request_type: str, **kwargs) -> str:
    """生成缓存键"""
    key_data = f"{request_type}:" + ":".join([f"{k}={v}" for k, v in sorted(kwargs.items())])
    return hashlib.md5(key_data.encode()).hexdigest()

def clear_product_cache():
    """清除商品详情缓存"""
    try:
        # 连接Redis
        r = redis.Redis(host='**************', port=16377, password='gy$7&hg243', decode_responses=True)
        
        # 生成缓存键
        cache_key = generate_cache_key(
            "detail",
            product_url="https://detail.1688.com/offer/932222752479.html",
            language="zh"
        )
        
        print(f"🔍 缓存键: {cache_key}")
        
        # 检查缓存是否存在
        if r.exists(cache_key):
            print("✅ 找到缓存数据")
            # 删除缓存
            r.delete(cache_key)
            print("✅ 缓存已清除")
        else:
            print("⚠️ 没有找到缓存数据")
        
        # 清除所有缓存
        all_keys = r.keys("*")
        if all_keys:
            print(f"🔍 找到 {len(all_keys)} 个缓存键")
            for key in all_keys:
                print(f"  - {key}")
                r.delete(key)
            print("✅ 所有缓存已清除")
        else:
            print("⚠️ 没有找到任何缓存")
            
        return True
        
    except Exception as e:
        print(f"❌ 清除缓存失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = clear_product_cache()
    sys.exit(0 if success else 1)
