/**
 * 淘宝配置提取器 - 内容脚本样式
 * 用于在淘宝页面中显示的元素样式
 */

/* 浮动消息样式 */
#taobao-extractor-message {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 999999 !important;
    padding: 12px 20px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: bold !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
    transition: all 0.3s ease !important;
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
    max-width: 300px !important;
    word-wrap: break-word !important;
}

/* 成功消息 */
#taobao-extractor-message.success {
    background-color: #28a745 !important;
}

/* 错误消息 */
#taobao-extractor-message.error {
    background-color: #dc3545 !important;
}

/* 警告消息 */
#taobao-extractor-message.warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

/* 信息消息 */
#taobao-extractor-message.info {
    background-color: #17a2b8 !important;
}

/* 浮动提取面板样式 */
.taobao-extractor-panel {
    position: fixed !important;
    top: 50% !important;
    right: 20px !important;
    transform: translateY(-50%) !important;
    z-index: 999998 !important;
    background: white !important;
    border: 2px solid #ff6600 !important;
    border-radius: 8px !important;
    padding: 15px !important;
    box-shadow: 0 8px 24px rgba(0,0,0,0.3) !important;
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
    font-size: 14px !important;
    min-width: 280px !important;
    max-width: 350px !important;
}

.taobao-extractor-panel h3 {
    margin: 0 0 10px 0 !important;
    color: #ff6600 !important;
    font-size: 16px !important;
    text-align: center !important;
}

.taobao-extractor-panel .status-item {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 8px !important;
    padding: 5px 0 !important;
    border-bottom: 1px solid #eee !important;
}

.taobao-extractor-panel .status-label {
    font-weight: bold !important;
    color: #666 !important;
}

.taobao-extractor-panel .status-value {
    color: #333 !important;
}

.taobao-extractor-panel .status-value.success {
    color: #28a745 !important;
}

.taobao-extractor-panel .status-value.error {
    color: #dc3545 !important;
}

.taobao-extractor-panel .buttons {
    display: flex !important;
    gap: 10px !important;
    margin-top: 15px !important;
}

.taobao-extractor-panel .btn {
    flex: 1 !important;
    padding: 8px 12px !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    font-size: 12px !important;
    font-weight: bold !important;
    transition: background-color 0.3s !important;
}

.taobao-extractor-panel .btn-primary {
    background-color: #ff6600 !important;
    color: white !important;
}

.taobao-extractor-panel .btn-primary:hover {
    background-color: #e55a00 !important;
}

.taobao-extractor-panel .btn-secondary {
    background-color: #6c757d !important;
    color: white !important;
}

.taobao-extractor-panel .btn-secondary:hover {
    background-color: #545b62 !important;
}

.taobao-extractor-panel .btn:disabled {
    background-color: #ccc !important;
    cursor: not-allowed !important;
}

.taobao-extractor-panel .close-btn {
    position: absolute !important;
    top: 5px !important;
    right: 8px !important;
    background: none !important;
    border: none !important;
    font-size: 18px !important;
    cursor: pointer !important;
    color: #999 !important;
    padding: 0 !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.taobao-extractor-panel .close-btn:hover {
    color: #666 !important;
}

/* 加载动画 */
.taobao-extractor-loading {
    display: inline-block !important;
    width: 12px !important;
    height: 12px !important;
    border: 2px solid #f3f3f3 !important;
    border-top: 2px solid #ff6600 !important;
    border-radius: 50% !important;
    animation: taobao-extractor-spin 1s linear infinite !important;
    margin-right: 5px !important;
}

@keyframes taobao-extractor-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 配置预览样式 */
.taobao-extractor-config-preview {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 4px !important;
    padding: 8px !important;
    margin-top: 10px !important;
    max-height: 150px !important;
    overflow-y: auto !important;
    font-size: 11px !important;
}

.taobao-extractor-config-item {
    margin-bottom: 5px !important;
}

.taobao-extractor-config-item .key {
    font-weight: bold !important;
    color: #666 !important;
}

.taobao-extractor-config-item .value {
    color: #333 !important;
    word-break: break-all !important;
    font-family: monospace !important;
}

/* 确保样式不被页面样式覆盖 */
.taobao-extractor-panel * {
    box-sizing: border-box !important;
    line-height: 1.4 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .taobao-extractor-panel {
        right: 10px !important;
        left: 10px !important;
        max-width: none !important;
        min-width: auto !important;
    }
    
    #taobao-extractor-message {
        right: 10px !important;
        left: 10px !important;
        max-width: none !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .taobao-extractor-panel {
        border-width: 3px !important;
    }
    
    .taobao-extractor-panel .btn {
        border: 2px solid currentColor !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .taobao-extractor-panel {
        background: #2d3748 !important;
        color: #e2e8f0 !important;
        border-color: #ff6600 !important;
    }
    
    .taobao-extractor-panel .status-label {
        color: #a0aec0 !important;
    }
    
    .taobao-extractor-panel .status-value {
        color: #e2e8f0 !important;
    }
    
    .taobao-extractor-config-preview {
        background-color: #4a5568 !important;
        border-color: #718096 !important;
        color: #e2e8f0 !important;
    }
}
