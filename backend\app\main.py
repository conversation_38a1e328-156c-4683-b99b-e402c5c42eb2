"""
FastAPI 应用主入口
"""
from fastapi import FastAP<PERSON>, Request, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional
import time
import uuid
import json
import asyncio
from datetime import datetime, timezone

from app.database import get_db, test_database_connection
from app.models import ApiCallLog
from app.routers import proxies, logs, platforms, verification, settings, crawler_config, crawler_pool
from app.auth import auth_manager, get_current_user, optional_auth
from app.preset_tokens import get_preset_token_manager

# 配置更新请求模型
class ConfigUpdateRequest(BaseModel):
    platform: str
    username: str
    cookie: str
    token: str
    user_agent: Optional[str] = None
    extracted_at: Optional[str] = None
    page_url: Optional[str] = None

# 登录请求模型
class LoginRequest(BaseModel):
    username: str
    password: str

# 登录响应模型
class LoginResponse(BaseModel):
    code: int
    message: str
    data: Optional[dict] = None

def log_api_call(db: Session, request_id: str, client_ip: str, platform: str,
                search_query: str, response_code: int, response_time_ms: int,
                error_message: str = None):
    """记录API调用日志"""
    try:
        # 查找平台ID
        platform_id = None
        if platform:
            from .models import Platform
            platform_obj = db.query(Platform).filter(Platform.name == platform).first()
            if platform_obj:
                platform_id = platform_obj.id

        log_entry = ApiCallLog(
            request_id=request_id,
            client_ip=client_ip,
            request_method="POST",
            request_path="/api/v1/search",
            platform_id=platform_id,
            search_type="keyword",
            search_query=search_query,
            response_code=response_code,
            response_time_ms=response_time_ms,
            error_message=error_message,
            created_at=datetime.now(timezone.utc)
        )
        db.add(log_entry)
        db.commit()
    except Exception as e:
        print(f"记录API日志失败: {e}")
        db.rollback()

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("AqentCrawler API 启动中...")

    # 测试数据库连接
    if test_database_connection():
        print("数据库连接正常")
    else:
        print("❌ 数据库连接失败，请检查配置")

    # 初始化缓存服务
    try:
        from app.services.cache_service import cache_service
        import os

        # 优先使用REDIS_URL，如果没有则使用分别的环境变量
        redis_url = os.getenv("REDIS_URL")
        if not redis_url:
            redis_host = os.getenv("REDIS_HOST", "localhost")
            redis_port = os.getenv("REDIS_PORT", "6379")
            redis_password = os.getenv("REDIS_PASSWORD", "")
            redis_db = os.getenv("REDIS_DB", "0")
            if redis_password:
                redis_url = f"redis://:{redis_password}@{redis_host}:{redis_port}/{redis_db}"
            else:
                redis_url = f"redis://{redis_host}:{redis_port}/{redis_db}"

        print(f"🔗 Redis连接URL: {redis_url}")
        await cache_service.initialize(redis_url)
        print("✅ 缓存服务初始化完成")
    except Exception as e:
        print(f"❌ 缓存服务初始化失败: {str(e)}")



    # 启动账号监控服务
    try:
        from app.services.account_monitor_service import account_monitor_service
        asyncio.create_task(account_monitor_service.start_monitoring())
        print("账号监控服务已启动")
    except Exception as e:
        print(f"❌ 账号监控服务启动失败: {str(e)}")

    print("API服务启动完成")

    yield

    # 关闭时执行
    print("🛑 API服务已停止")

    try:
        from app.services.account_monitor_service import account_monitor_service
        account_monitor_service.stop_monitoring()
        print("🛑 账号监控服务已停止")
    except Exception as e:
        print(f"❌ 停止账号监控服务时出错: {str(e)}")

    print("API服务关闭")

app = FastAPI(
    title="AqentCrawler API",
    description="智能代购爬虫系统",
    version="1.0.0",
    lifespan=lifespan
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(platforms.router)
app.include_router(proxies.router)
app.include_router(logs.router)
app.include_router(verification.router)
# app.include_router(login_test.router)
app.include_router(settings.router)
app.include_router(crawler_config.router)

# 注册上游API路由
from app.api.v1 import upstream
app.include_router(upstream.router)

# 导入并注册 stats 路由
from app.routers import stats
app.include_router(stats.router)

# 爬虫池管理路由
app.include_router(crawler_pool.router)

# 邮件管理路由
from app.routers import email
app.include_router(email.router)

# 告警管理路由
from app.routers import alerts
app.include_router(alerts.router)

# 启动健康检查服务
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    try:
        from app.services.health_check_service import health_check_service
        import asyncio

        # 在后台启动健康检查服务
        asyncio.create_task(health_check_service.start_health_check())
        logger.info("健康检查服务已启动")

    except Exception as e:
        logger.error(f"启动健康检查服务失败: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    try:
        from app.services.health_check_service import health_check_service
        health_check_service.stop_health_check()
        logger.info("健康检查服务已停止")

    except Exception as e:
        logger.error(f"停止健康检查服务失败: {e}")

# 请求中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    request.state.request_id = str(uuid.uuid4())
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    response.headers["X-Request-ID"] = request.state.request_id
    
    return response

@app.get("/")
async def root():
    return {"message": "AqentCrawler API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": time.time()}

@app.get("/api/v1/health")
async def api_health_check():
    """API健康检查接口"""
    return {"status": "healthy", "timestamp": time.time(), "service": "AqentCrawler API"}

@app.post("/api/v1/auth/login")
async def login(login_data: LoginRequest):
    """用户登录接口"""
    try:
        token = auth_manager.authenticate_user(login_data.username, login_data.password)

        if token:
            return {
                "code": 200,
                "message": "登录成功",
                "data": {
                    "token": token,
                    "username": login_data.username,
                    "expires_in": 24 * 60 * 60  # 24小时，单位秒
                }
            }
        else:
            return {
                "code": 401,
                "message": "用户名或密码错误",
                "data": None
            }
    except Exception as e:
        return {
            "code": 500,
            "message": f"登录失败: {str(e)}",
            "data": None
        }

@app.post("/api/v1/auth/verify")
async def verify_token(current_user: str = Depends(get_current_user)):
    """验证令牌接口"""
    return {
        "code": 200,
        "message": "令牌有效",
        "data": {
            "username": current_user,
            "valid": True
        }
    }

@app.get("/api/v1/auth/preset-tokens")
async def get_preset_tokens(current_user: str = Depends(get_current_user)):
    """获取预置Token列表（仅管理员可访问）"""
    try:
        # 只有admin用户可以查看预置Token
        if current_user != "admin":
            return {
                "code": 403,
                "message": "权限不足，只有管理员可以查看预置Token",
                "data": None
            }

        preset_manager = get_preset_token_manager()
        tokens = preset_manager.get_all_tokens()

        # 隐藏实际的Token值，只显示配置信息
        safe_tokens = {}
        for token_id, token_info in tokens.items():
            safe_tokens[token_id] = {
                "token_id": token_id,
                "username": token_info["username"],
                "description": token_info["description"],
                "expires_days": token_info["expires_days"],
                "permissions": token_info["permissions"],
                "created_at": token_info["created_at"],
                "expires_at": token_info["expires_at"],
                "token_preview": token_info["token"][:20] + "..."  # 只显示前20个字符
            }

        return {
            "code": 200,
            "message": "success",
            "data": {
                "tokens": safe_tokens,
                "total": len(safe_tokens)
            }
        }

    except Exception as e:
        return {
            "code": 500,
            "message": f"获取预置Token失败: {str(e)}",
            "data": None
        }

@app.get("/api/v1/auth/preset-tokens/{token_id}")
async def get_preset_token(token_id: str, current_user: str = Depends(get_current_user)):
    """获取指定的预置Token（仅管理员可访问）"""
    try:
        # 只有admin用户可以获取预置Token
        if current_user != "admin":
            return {
                "code": 403,
                "message": "权限不足，只有管理员可以获取预置Token",
                "data": None
            }

        preset_manager = get_preset_token_manager()
        token = preset_manager.get_token(token_id)

        if not token:
            return {
                "code": 404,
                "message": f"未找到Token ID: {token_id}",
                "data": None
            }

        token_info = preset_manager.get_all_tokens()[token_id]

        return {
            "code": 200,
            "message": "success",
            "data": {
                "token_id": token_id,
                "token": token,
                "username": token_info["username"],
                "description": token_info["description"],
                "expires_days": token_info["expires_days"],
                "permissions": token_info["permissions"],
                "created_at": token_info["created_at"],
                "expires_at": token_info["expires_at"]
            }
        }

    except Exception as e:
        return {
            "code": 500,
            "message": f"获取预置Token失败: {str(e)}",
            "data": None
        }

@app.post("/api/v1/config/update")
async def update_crawler_config(config: ConfigUpdateRequest, db: Session = Depends(get_db)):
    """更新爬虫配置接口"""
    try:
        print(f"🔧 收到配置更新请求: platform={config.platform}, username={config.username}")

        # 使用配置服务
        from app.services.config_service import ConfigService
        config_service = ConfigService(db)

        # 创建或更新账号
        account = config_service.create_or_update_account(
            platform_code=config.platform,
            username=config.username,
            cookie=config.cookie,
            token=config.token,
            user_agent=config.user_agent,
            extracted_from=config.page_url
        )

        return {
            "code": 200,
            "message": "配置更新成功",
            "data": {
                "platform": config.platform,
                "username": config.username,
                "account_id": account.id,
                "status": account.status,
                "updated_at": account.updated_at.isoformat() if account.updated_at else None
            }
        }

    except Exception as e:
        print(f"❌ 配置更新失败: {str(e)}")
        return {
            "code": 500,
            "message": f"配置更新失败: {str(e)}",
            "data": None
        }

@app.post("/api/v1/config/test")
async def test_config_endpoint():
    """测试配置接口"""
    return {
        "code": 200,
        "message": "配置接口测试成功",
        "data": {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "AqentCrawler Config API"
        }
    }

@app.post("/api/v1/search")
async def search_products(request: Request, db: Session = Depends(get_db), current_user: str = Depends(get_current_user)):
    """商品搜索接口 - 集成数据库版"""
    start_time = time.time()
    request_id = request.state.request_id
    client_ip = request.client.host

    try:
        body = await request.json()
        query = body.get("query", "")
        platform = body.get("platform", "taobao")
        language = body.get("language", "zh")

        # 调试信息
        print(f"🔍 接收到请求体: {body}")
        print(f"🔍 解析后参数: query='{query}' (类型: {type(query)}), platform='{platform}'")

        if not query or query.strip() == "":
            # 记录错误日志
            log_api_call(db, request_id, client_ip, platform, query, 400,
                        int((time.time() - start_time) * 1000), "搜索关键词不能为空")
            return {"code": 400, "message": "搜索关键词不能为空", "data": None}
        
        # 使用爬虫池进行搜索
        try:
            print(f"🔍 开始搜索: 平台={platform}, 关键词={query}")

            # 初始化爬虫池服务
            from .services.crawler_pool_service import CrawlerPoolService
            import uuid

            pool_service = CrawlerPoolService(db)

            # 创建或获取会话
            session_id = pool_service.get_or_create_session(
                client_ip=client_ip,
                user_agent=request.headers.get("user-agent", "SearchAPI/1.0"),
                platform_code=platform
            )

            if not session_id:
                raise Exception("创建会话失败")

            # 选择最佳爬虫账号
            account = pool_service.select_best_crawler(
                platform_code=platform,
                session_id=session_id,
                client_ip=client_ip
            )

            print(f"📋 账号查询结果: {account.username if account else '未找到可用账号'}")

            if not account:
                raise Exception(f"search_products没有可用的{platform}爬虫账号")

            # 使用真实爬虫
            if platform == "taobao":
                print(f"🕷️ 开始初始化淘宝爬虫...")
                from .crawler.platforms.taobao_correct import get_taobao_correct_crawler
                crawler = get_taobao_correct_crawler()
                print(f"✅ 淘宝爬虫初始化完成")

                print(f"🔍 开始执行搜索: query={query}, account={account.username if account else None}")
                search_results = await crawler.search_products(
                    query,
                    account.username if account else None
                )
                print(f"✅ 搜索完成，结果: {search_results.get('total', 0)} 个商品")

                # 记录爬虫池使用统计
                pool_service.record_request_result(
                    session_id=session_id,
                    account_id=account.id,
                    success=True,
                    response_time_ms=int((time.time() - start_time) * 1000)
                )

            elif platform == "1688":
                print(f"🕷️ 开始初始化1688爬虫...")
                from .crawler.platforms.alibaba_1688 import get_alibaba_1688_crawler
                crawler = get_alibaba_1688_crawler()
                print(f"✅ 1688爬虫初始化完成")

                print(f"🔍 开始执行1688搜索: query={query}, account={account.username if account else None}")
                search_results = await crawler.search_products(
                    query,
                    account.username if account else None
                )
                print(f"✅ 1688搜索完成，结果: {search_results.get('total', 0)} 个商品")

                # 记录爬虫池使用统计
                pool_service.record_request_result(
                    session_id=session_id,
                    account_id=account.id,
                    success=True,
                    response_time_ms=int((time.time() - start_time) * 1000)
                )

            elif platform == "jingdong":
                print(f"🕷️ 开始初始化京东爬虫...")
                from .crawler.real_jingdong_crawler import get_jingdong_crawler
                crawler = await get_jingdong_crawler()
                print(f"✅ 京东爬虫初始化完成")

                print(f"🔍 开始执行京东搜索: query={query}, account={account.username if account else None}")
                search_results = await crawler.search_products(
                    query,
                    account.username if account else None
                )
                print(f"✅ 京东搜索完成，结果: {search_results.get('total', 0)} 个商品")

                # 记录爬虫池使用统计
                pool_service.record_request_result(
                    session_id=session_id,
                    account_id=account.id,
                    success=True,
                    response_time_ms=int((time.time() - start_time) * 1000)
                )

            else:
                # 其他平台暂未实现
                search_results = {
                    "platform": platform,
                    "query": query,
                    "total": 0,
                    "products": [],
                    "error": f"{platform}平台爬虫暂未实现，目前支持淘宝和京东平台",
                    "crawl_method": "平台未支持"
                }
        except Exception as crawler_error:
            print(f"❌ 爬虫搜索失败: {str(crawler_error)}")
            print(f"❌ 错误类型: {type(crawler_error).__name__}")
            import traceback
            print(f"❌ 详细错误信息:\n{traceback.format_exc()}")

            # 记录爬虫池错误统计
            if 'pool_service' in locals() and 'session_id' in locals():
                pool_service.record_request_result(
                    session_id=session_id,
                    account_id=account.id if account else None,
                    success=False,
                    response_time_ms=int((time.time() - start_time) * 1000),
                    error_message=str(crawler_error)
                )

            # 爬虫失败时返回错误信息
            search_results = {
                "platform": platform,
                "query": query,
                "total": 0,
                "products": [],
                "response_time": 0,
                "account_used": account.username if account else None,
                "crawl_method": "爬虫失败",
                "error": f"爬虫错误: {str(crawler_error)}"
            }

        # 格式化为上游系统格式
        if search_results.get('total', 0) > 0 and search_results.get('products'):
            # 根据平台选择格式化方法
            if hasattr(crawler, 'format_search_results_for_upstream'):
                formatted_results = crawler.format_search_results_for_upstream(
                    search_results['products'],
                    query,
                    search_results.get('total', 0)
                )
            else:
                # 默认格式化
                formatted_results = {
                    'list': search_results.get('products', []),
                    'total': search_results.get('total', 0),
                    'platform': platform,
                    'query': query,
                    'success': True
                }
        else:
            # 空结果或错误
            formatted_results = {
                'list': [],
                'total': 0,
                'platform': platform,
                'query': query,
                'success': False,
                'error': search_results.get('error', '未找到相关商品')
            }

        # 记录成功日志
        response_time_ms = int((time.time() - start_time) * 1000)
        log_api_call(db, request_id, client_ip, platform, query, 200, response_time_ms)

        return {
            "code": 200,
            "message": "success",
            "data": formatted_results
        }

    except Exception as e:
        # 记录错误日志
        response_time_ms = int((time.time() - start_time) * 1000)
        platform_name = locals().get('platform', 'unknown')
        query_text = locals().get('query', '')
        log_api_call(db, request_id, client_ip, platform_name, query_text, 500, response_time_ms, str(e))

        return {
            "code": 500,
            "message": str(e),
            "data": None
        }

@app.post("/api/v1/product/detail")
async def get_product_detail(request: Request, db: Session = Depends(get_db), current_user: str = Depends(get_current_user)):
    """商品详情获取接口 - 返回完整的商品信息和描述"""
    start_time = time.time()
    request_id = request.state.request_id
    client_ip = request.client.host

    try:
        # 解析请求体
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        # 获取参数
        url = data.get('url', '').strip()

        if not url:
            return {
                "code": 400,
                "message": "请提供商品链接",
                "data": None
            }

        print(f"🔍 开始获取商品详情: url={url}")

        # 导入详情爬虫
        from .crawler.platforms.taobao_detail import get_taobao_detail_crawler
        crawler = get_taobao_detail_crawler()

        # 通过URL获取详情
        detail_result = await crawler.get_product_detail_by_url(url)

        print(f"✅ 商品详情获取完成: {detail_result.get('title', '')[:50]}...")

        # 记录成功日志
        response_time_ms = int((time.time() - start_time) * 1000)
        log_api_call(db, request_id, client_ip, 'taobao',
                    detail_result.get('title', '')[:100], 200, response_time_ms)

        return {
            "code": 200,
            "message": "success",
            "data": detail_result
        }

    except Exception as e:
        # 记录错误日志
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = str(e)
        log_api_call(db, request_id, client_ip, 'taobao',
                    url, 500, response_time_ms, error_msg)

        print(f"❌ 商品详情获取失败: {error_msg}")

        return {
            "code": 500,
            "message": error_msg,
            "data": None
        }

@app.post("/api/v1/product/detail-enhanced")
async def get_product_detail_enhanced(request: Request, db: Session = Depends(get_db), current_user: str = Depends(get_current_user)):
    """增强的商品详情接口 - 分别返回商品信息和描述，支持部分失败"""
    start_time = time.time()
    request_id = request.state.request_id
    client_ip = request.client.host

    try:
        # 解析请求体
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        # 获取参数
        url = data.get('url', '').strip()

        if not url:
            return {
                "code": 400,
                "message": "请提供商品链接",
                "data": None
            }

        print(f"🔍 开始获取增强商品详情: url={url}")

        # 导入详情爬虫
        from .crawler.platforms.taobao_detail import TaobaoDetailCrawler
        crawler = TaobaoDetailCrawler()

        # 提取商品信息
        item_info = crawler.extract_item_info_from_url(url)

        # 获取账号配置
        from app.database import get_db
        from app.models import CrawlerAccount, Platform
        from sqlalchemy import and_

        db_session = next(get_db())
        platform = db_session.query(Platform).filter(Platform.code == 'taobao').first()
        if not platform:
            raise Exception("未找到淘宝平台配置")

        account = db_session.query(CrawlerAccount).filter(
            and_(
                CrawlerAccount.platform_id == platform.id,
                CrawlerAccount.status == 'active',
                CrawlerAccount.is_enabled == True  # 只使用已启用的爬虫
            )
        ).first()

        if not account or not account.cookie or not account.token:
            raise Exception("未找到有效的淘宝账号配置")

        # 结果容器
        result = {
            "item_id": item_info['item_id'],
            "platform_type": item_info['platform_type'],
            "detail_success": False,
            "description_success": False,
            "detail_data": None,
            "description_data": None,
            "detail_error": None,
            "description_error": None
        }

        # 尝试获取商品详情
        try:
            detail_data = crawler.get_product_detail(
                item_info['item_id'],
                account.cookie,
                account.token,
                item_info['platform_type']
            )
            result["detail_success"] = True
            result["detail_data"] = detail_data
            print("✅ 商品详情获取成功")
        except Exception as e:
            result["detail_error"] = str(e)
            print(f"❌ 商品详情获取失败: {str(e)}")

        # 尝试获取商品描述
        try:
            desc_data = crawler.get_product_description(
                item_info['item_id'],
                account.cookie,
                account.token
            )
            result["description_success"] = True
            result["description_data"] = desc_data
            print("✅ 商品描述获取成功")
        except Exception as e:
            result["description_error"] = str(e)
            print(f"❌ 商品描述获取失败: {str(e)}")

        # 判断整体状态
        if result["detail_success"] or result["description_success"]:
            status_code = 200
            message = "success"
            if not result["detail_success"]:
                message = "部分成功：商品详情获取失败"
            elif not result["description_success"]:
                message = "部分成功：商品描述获取失败"
        else:
            status_code = 500
            message = "完全失败：商品详情和描述都获取失败"

        # 记录日志
        response_time_ms = int((time.time() - start_time) * 1000)
        log_api_call(db, request_id, client_ip, 'taobao',
                    item_info['item_id'], status_code, response_time_ms, message)

        return {
            "code": status_code,
            "message": message,
            "data": result
        }

    except Exception as e:
        # 记录错误日志
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = str(e)
        log_api_call(db, request_id, client_ip, 'taobao',
                    url, 500, response_time_ms, error_msg)

        print(f"❌ 增强商品详情获取失败: {error_msg}")

        return {
            "code": 500,
            "message": error_msg,
            "data": None
        }

@app.post("/api/v1/product/detail/1688")
async def get_1688_product_detail(request: Request, db: Session = Depends(get_db), current_user: str = Depends(get_current_user)):
    """1688商品详情获取接口 - 按照上游系统数据格式"""
    start_time = time.time()
    request_id = request.state.request_id
    client_ip = request.client.host

    try:
        # 解析请求体
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        # 获取参数
        url = data.get('url', '').strip()
        account_username = data.get('account_username', None)

        if not url:
            return {
                "code": 400,
                "message": "请提供1688商品链接",
                "data": None
            }

        # 处理1688链接 - 支持跳转链接和直接链接
        processed_url = url

        # 如果是跳转链接，尝试提取商品ID并构建标准链接
        if 'dj.1688.com' in url or ('1688.com' in url and 'detail.1688.com' not in url):
            # 尝试从跳转链接中提取商品ID
            import re

            # 方法1: 从URL参数中提取
            offer_id_patterns = [
                r'offerId["\']?\s*:\s*["\']?(\d+)',  # offerId参数
                r'offer[/\\](\d+)',                   # offer/数字格式
                r'id[=:](\d+)',                       # id=数字格式
            ]

            offer_id = None
            for pattern in offer_id_patterns:
                match = re.search(pattern, url)
                if match:
                    offer_id = match.group(1)
                    break

            if offer_id:
                processed_url = f"https://detail.1688.com/offer/{offer_id}.html"
                print(f"🔄 转换跳转链接: {url[:100]}... → {processed_url}")
            else:
                # 如果无法提取商品ID，尝试访问跳转链接获取真实URL
                try:
                    import requests
                    response = requests.head(url, allow_redirects=True, timeout=10)
                    if response.url and 'detail.1688.com' in response.url:
                        processed_url = response.url
                        print(f"🔄 通过重定向获取真实链接: {processed_url}")
                    else:
                        return {
                            "code": 400,
                            "message": "无法从URL中提取商品ID",
                            "data": None
                        }
                except Exception as redirect_e:
                    print(f"⚠️ 重定向获取失败: {str(redirect_e)}")
                    return {
                        "code": 400,
                        "message": f"无效的商品链接: 无法从URL中提取商品ID",
                        "data": None
                    }

        # 验证最终URL格式
        if 'detail.1688.com' not in processed_url:
            return {
                "code": 400,
                "message": "请提供有效的1688商品详情链接",
                "data": None
            }

        print(f"🔍 开始获取1688商品详情: url={processed_url}")

        # 导入1688爬虫
        from .crawler.platforms.alibaba_1688 import get_alibaba_1688_crawler
        crawler = get_alibaba_1688_crawler()

        # 获取商品详情
        detail_result = await crawler.get_product_detail(processed_url, account_username)

        if detail_result.get('success'):
            product_data = detail_result.get('data')
            print(f"✅ 1688商品详情获取成功: {product_data.get('name', '')[:50]}...")

            # 记录成功日志
            response_time_ms = int((time.time() - start_time) * 1000)
            log_api_call(db, request_id, client_ip, '1688',
                        product_data.get('name', '')[:100], 200, response_time_ms)

            return {
                "code": 200,
                "message": "获取1688商品详情成功",
                "data": product_data
            }
        else:
            error_msg = detail_result.get('error', '未知错误')
            print(f"❌ 1688商品详情获取失败: {error_msg}")

            # 记录错误日志
            response_time_ms = int((time.time() - start_time) * 1000)
            log_api_call(db, request_id, client_ip, '1688',
                        url, 400, response_time_ms, error_msg)

            return {
                "code": 400,
                "message": error_msg,
                "data": None
            }

    except Exception as e:
        # 记录错误日志
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = str(e)
        log_api_call(db, request_id, client_ip, '1688',
                    url, 500, response_time_ms, error_msg)

        print(f"❌ 1688商品详情获取失败: {error_msg}")

        return {
            "code": 500,
            "message": f"获取1688商品详情失败: {error_msg}",
            "data": None
        }

@app.post("/api/v1/product/detail/platform")
async def get_product_detail_by_platform(request: Request, db: Session = Depends(get_db), current_user: str = Depends(get_current_user)):
    """通用商品详情获取接口 - 支持多平台"""
    start_time = time.time()
    request_id = request.state.request_id
    client_ip = request.client.host

    try:
        # 解析请求体
        body = await request.body()
        data = json.loads(body.decode('utf-8'))

        # 获取参数
        url = data.get('url', '').strip()
        platform = data.get('platform', '').strip().lower()
        account_username = data.get('account_username', None)

        if not url:
            return {
                "code": 400,
                "message": "请提供商品链接",
                "data": None
            }

        if not platform:
            # 自动检测平台
            if 'detail.1688.com' in url:
                platform = '1688'
            elif 'item.taobao.com' in url or 'detail.tmall.com' in url:
                platform = 'taobao'
            else:
                return {
                    "code": 400,
                    "message": "无法识别商品链接平台，请指定platform参数",
                    "data": None
                }

        print(f"🔍 开始获取{platform}商品详情: url={url}")

        # 根据平台选择爬虫
        if platform == '1688':
            from .crawler.platforms.alibaba_1688 import get_alibaba_1688_crawler
            crawler = get_alibaba_1688_crawler()
            detail_result = await crawler.get_product_detail(url, account_username)

            if detail_result.get('success'):
                product_data = detail_result.get('data')
                print(f"✅ {platform}商品详情获取成功: {product_data.get('name', '')[:50]}...")

                # 记录成功日志
                response_time_ms = int((time.time() - start_time) * 1000)
                log_api_call(db, request_id, client_ip, platform,
                            product_data.get('name', '')[:100], 200, response_time_ms)

                return {
                    "code": 200,
                    "message": f"获取{platform}商品详情成功",
                    "data": product_data
                }
            else:
                error_msg = detail_result.get('error', '未知错误')
                print(f"❌ {platform}商品详情获取失败: {error_msg}")

                # 记录错误日志
                response_time_ms = int((time.time() - start_time) * 1000)
                log_api_call(db, request_id, client_ip, platform,
                            url, 400, response_time_ms, error_msg)

                return {
                    "code": 400,
                    "message": error_msg,
                    "data": None
                }

        elif platform == 'taobao':
            from .crawler.platforms.taobao_detail import get_taobao_detail_crawler
            crawler = get_taobao_detail_crawler()
            detail_result = crawler.get_product_detail_by_url_for_upstream(url)

            if detail_result:
                print(f"✅ {platform}商品详情获取成功: {detail_result.get('name', '')[:50]}...")

                # 记录成功日志
                response_time_ms = int((time.time() - start_time) * 1000)
                log_api_call(db, request_id, client_ip, platform,
                            detail_result.get('name', '')[:100], 200, response_time_ms)

                return {
                    "code": 200,
                    "message": f"获取{platform}商品详情成功",
                    "data": detail_result
                }
            else:
                error_msg = "商品详情获取失败"
                print(f"❌ {platform}商品详情获取失败: {error_msg}")

                # 记录错误日志
                response_time_ms = int((time.time() - start_time) * 1000)
                log_api_call(db, request_id, client_ip, platform,
                            url, 400, response_time_ms, error_msg)

                return {
                    "code": 400,
                    "message": error_msg,
                    "data": None
                }
        else:
            return {
                "code": 400,
                "message": f"暂不支持{platform}平台的商品详情获取",
                "data": None
            }

    except Exception as e:
        # 记录错误日志
        response_time_ms = int((time.time() - start_time) * 1000)
        error_msg = str(e)
        log_api_call(db, request_id, client_ip, platform if 'platform' in locals() else 'unknown',
                    url if 'url' in locals() else '', 500, response_time_ms, error_msg)

        print(f"❌ 商品详情获取失败: {error_msg}")

        return {
            "code": 500,
            "message": f"获取商品详情失败: {error_msg}",
            "data": None
        }

# 移除旧的硬编码平台路由，使用新的动态平台管理

# 统计API已移至 app.routers.stats 模块

# 验证和登录相关API已移至专门的路由文件中

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
