#!/usr/bin/env python3
"""
淘宝商品详情爬虫
支持通过商品ID或商品链接获取详细信息
"""
import requests
import hashlib
import json
import time
import re
import urllib.parse
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class TaobaoDetailCrawler:
    """淘宝商品详情爬虫"""
    
    def __init__(self):
        self.detail_api_url = 'https://h5api.m.taobao.com/h5/mtop.taobao.pcdetail.data.get/1.0/'
        self.desc_api_url = 'https://h5api.m.taobao.com/h5/mtop.taobao.detail.getdesc/7.0/'
        
    def extract_item_info_from_url(self, url: str) -> Dict[str, str]:
        """从商品链接中提取商品ID和SKU ID，并识别平台类型"""
        try:
            # 识别平台类型
            platform_type = 'taobao'  # 默认淘宝
            if 'tmall.com' in url or 'detail.tmall.com' in url:
                platform_type = 'tmall'
            elif 'item.taobao.com' in url:
                platform_type = 'taobao'

            # 提取商品ID
            item_id_match = re.search(r'[?&]id=(\d+)', url)
            if not item_id_match:
                # 尝试从路径中提取
                item_id_match = re.search(r'/item/(\d+)\.htm', url)

            if not item_id_match:
                raise ValueError("无法从URL中提取商品ID")

            item_id = item_id_match.group(1)

            # 提取SKU ID（可选）
            sku_id_match = re.search(r'[?&]skuId=(\d+)', url)
            sku_id = sku_id_match.group(1) if sku_id_match else ""

            # 提取查询关键词（可选）
            query_match = re.search(r'[?&]query=([^&]+)', url)
            query = urllib.parse.unquote(query_match.group(1)) if query_match else ""

            logger.info(f"从URL提取信息: platform={platform_type}, item_id={item_id}, sku_id={sku_id}, query={query}")

            return {
                'item_id': item_id,
                'sku_id': sku_id,
                'query': query,
                'platform_type': platform_type
            }

        except Exception as e:
            logger.error(f"解析商品URL失败: {str(e)}")
            raise ValueError(f"无效的商品链接: {str(e)}")
    
    def generate_detail_sign(self, timestamp: int, item_id: str, em_token: str, platform_type: str = 'taobao') -> tuple:
        """生成商品详情API的签名"""
        try:
            eC = '12574478'

            # 根据平台类型设置不同的domain
            if platform_type == 'tmall':
                domain = "https://detail.tmall.com"
            else:
                domain = "https://item.taobao.com"

            # 构建exParams
            ex_params = {
                "id": item_id,
                "queryParams": f"id={item_id}",
                "domain": domain,
                "path_name": "/item.htm",
                "pcSource": "pcTaobaoMain",
                "appKey": "3q2+7wX9z8JkLmN1oP5QrStUvWxYzA0B",
                "refId": "SA6ZZUsDAVml+zhgQuzAu29xkdG/DdVHLo7A7ODOFRI=",
                "nonce": "DTGdjMLTktRka6G8Y1fpBA==",
                "feTraceId": "85c098b0-f631-48fd-90ae-40f46cf42d2b"
            }
            
            params = {
                "id": item_id,
                "detail_v": "3.3.2",
                "exParams": json.dumps(ex_params, separators=(',', ':'), ensure_ascii=False)
            }
            
            ep_data = json.dumps(params, separators=(',', ':'), ensure_ascii=False)
            string = em_token + "&" + str(timestamp) + "&" + eC + "&" + ep_data
            
            MD5 = hashlib.md5()
            MD5.update(string.encode('utf-8'))
            sign = MD5.hexdigest()
            
            logger.info(f"生成详情签名: {sign}")
            return sign, ep_data
            
        except Exception as e:
            logger.error(f"生成详情签名失败: {str(e)}")
            raise
    
    def generate_desc_sign(self, timestamp: int, item_id: str, em_token: str) -> tuple:
        """生成商品描述API的签名"""
        try:
            eC = '12574478'
            
            params = {
                "id": item_id,
                "detail_v": "3.3.0",
                "preferWireless": "true"
            }
            
            ep_data = json.dumps(params, separators=(',', ':'), ensure_ascii=False)
            string = em_token + "&" + str(timestamp) + "&" + eC + "&" + ep_data
            
            MD5 = hashlib.md5()
            MD5.update(string.encode('utf-8'))
            sign = MD5.hexdigest()
            
            logger.info(f"生成描述签名: {sign}")
            return sign, ep_data
            
        except Exception as e:
            logger.error(f"生成描述签名失败: {str(e)}")
            raise

    def get_product_detail(self, item_id: str, cookie: str, em_token: str, platform_type: str = 'taobao') -> Dict[str, Any]:
        """获取商品详情信息"""
        try:
            headers = {
                'cookie': cookie,
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            timestamp = int(time.time() * 1000)
            sign, ep_data = self.generate_detail_sign(timestamp, item_id, em_token, platform_type)

            params = {
                'jsv':'2.7.5',
                'appKey':'12574478',
                't': timestamp,
                'sign': sign,
                'api':'mtop.taobao.pcdetail.data.get',
                'v':'1.0',
                'isSec':0,
                'ecode':0,
                'timeout':'10000',
                'ttid':'2022@taobao_litepc_9.17.0',
                'AntiFlood':'true',
                'AntiCreep':'true',
                'dataType':'json',
                'valueType':'string',
                'type':'json',
                'data': ep_data
            }

            logger.info(f"请求商品详情API: {self.detail_api_url}")
            response = requests.get(url=self.detail_api_url, headers=headers, params=params, timeout=30)

            logger.info(f"详情API响应状态码: {response.status_code}")
            logger.info(f"详情API响应长度: {len(response.text)}")

            if response.status_code == 200:
                return self.parse_detail_response(response.text)
            else:
                raise Exception(f"API请求失败，状态码: {response.status_code}")

        except Exception as e:
            logger.error(f"获取商品详情失败: {str(e)}")
            raise

    def get_product_description(self, item_id: str, cookie: str, em_token: str) -> Dict[str, Any]:
        """获取商品描述信息"""
        try:
            headers = {
                'cookie': cookie,
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            timestamp = int(time.time() * 1000)
            sign, ep_data = self.generate_desc_sign(timestamp, item_id, em_token)

            params = {
                'jsv': '2.7.5',
                'appKey': '12574478',
                't': timestamp,
                'sign': sign,
                'dangerouslySetWindvaneParams': '',
                'api': 'mtop.taobao.detail.getdesc',
                'v': '7.0',
                'AntiFlood': 'true',
                'AntiCreep': 'true',
                'H5Request': 'true',
                'timeout': '3000',
                'ttid': '2022@taobao_litepc_9.17.0',
                'type': 'jsonp',
                'dataType': 'jsonp',
                'callback': 'mtopjsonp5',
                'data': ep_data
            }

            logger.info(f"请求商品描述API: {self.desc_api_url}")
            response = requests.get(url=self.desc_api_url, headers=headers, params=params, timeout=30)

            logger.info(f"描述API响应状态码: {response.status_code}")
            logger.info(f"描述API响应长度: {len(response.text)}")

            if response.status_code == 200:
                return self.parse_desc_response(response.text)
            else:
                raise Exception(f"API请求失败，状态码: {response.status_code}")

        except Exception as e:
            logger.error(f"获取商品描述失败: {str(e)}")
            raise

    def parse_detail_response(self, response_text: str) -> Dict[str, Any]:
        """解析商品详情响应"""
        try:
            logger.info(f"详情API原始响应长度: {len(response_text)}")
            logger.info(f"详情API响应前200字符: {response_text[:200]}")

            # 保存原始响应数据
            self._save_response_data(response_text, 'detail')

            # 处理JSONP格式
            json_str = response_text
            if response_text.startswith('mtopjsonp') and '(' in response_text and response_text.endswith(')'):
                # 提取JSONP中的JSON部分
                start_pos = response_text.find('(')
                end_pos = response_text.rfind(')')
                if start_pos != -1 and end_pos != -1 and end_pos > start_pos:
                    json_str = response_text[start_pos + 1:end_pos]
                    logger.info("检测到JSONP格式，已提取JSON部分")

            # 解析JSON响应
            json_data = json.loads(json_str)

            # 检查API调用是否成功
            if 'ret' in json_data and json_data['ret']:
                ret_messages = json_data['ret']
                logger.info(f"详情API返回状态: {ret_messages}")
                if any('FAIL' in msg or 'ERROR' in msg for msg in ret_messages):
                    raise Exception(f"API返回错误: {ret_messages}")

            if 'data' not in json_data:
                raise Exception("响应中没有data字段")

            data = json_data['data']
            logger.info(f"data字段类型: {type(data)}")

            if not isinstance(data, dict):
                logger.error(f"data字段不是字典类型: {type(data)}")
                # 如果data是字符串，尝试再次解析
                if isinstance(data, str):
                    try:
                        data = json.loads(data)
                        logger.info("成功解析data字符串为字典")
                    except:
                        logger.error("无法解析data字符串")
                        raise Exception("data字段格式错误")
                else:
                    raise Exception("data字段格式错误")

            logger.info(f"data字段的顶级键: {list(data.keys())[:10]}")

            # 提取商品基本信息
            item_info = data.get('item', {})
            seller_info = data.get('seller', {})
            price_info = data.get('price', {})
            delivery_info = data.get('delivery', {})

            # 如果标准字段为空，尝试从data中直接提取
            if not item_info:
                # 尝试其他可能的字段名
                item_info = data.get('itemInfo', {}) or data.get('product', {}) or data

            logger.info(f"item_info的键: {list(item_info.keys())[:20] if isinstance(item_info, dict) else 'not dict'}")
            logger.info(f"seller_info的键: {list(seller_info.keys())[:10] if isinstance(seller_info, dict) else 'not dict'}")
            logger.info(f"price_info的键: {list(price_info.keys())[:10] if isinstance(price_info, dict) else 'not dict'}")

            # 输出部分原始数据用于调试
            if isinstance(item_info, dict) and item_info:
                logger.info(f"item_info示例数据: {str(item_info)[:500]}...")
            if isinstance(seller_info, dict) and seller_info:
                logger.info(f"seller_info示例数据: {str(seller_info)[:200]}...")
            if isinstance(price_info, dict) and price_info:
                logger.info(f"price_info示例数据: {str(price_info)[:200]}...")

            # 如果没有找到标准字段，尝试直接使用data作为item_info
            if not item_info and not seller_info and not price_info:
                logger.info("未找到标准字段，使用data作为item_info")
                item_info = data
                seller_info = {}
                price_info = {}
                delivery_info = {}

            logger.info(f"提取的字段: item_info类型={type(item_info)}, seller_info类型={type(seller_info)}")

            return {
                'item_info': item_info,
                'seller_info': seller_info,
                'price_info': price_info,
                'delivery_info': delivery_info,
                'raw_data': data
            }

        except Exception as e:
            logger.error(f"解析详情响应失败: {str(e)}")
            raise

    def parse_desc_response(self, response_text: str) -> Dict[str, Any]:
        """解析商品描述响应"""
        try:
            logger.info(f"描述API原始响应长度: {len(response_text)}")
            logger.info(f"描述API响应前100字符: {response_text[:100]}")

            # 保存原始响应数据
            self._save_response_data(response_text, 'description')

            # 移除JSONP包装
            json_str = response_text.strip()  # 先去除首尾空白字符
            if json_str.startswith('mtopjsonp5(') and json_str.endswith(')'):
                json_str = json_str[11:-1]
            elif 'mtopjsonp5(' in json_str:
                # 处理可能的不完整JSONP响应或前面有空格的情况
                start_pos = json_str.find('mtopjsonp5(')
                if start_pos != -1:
                    json_str = json_str[start_pos:]
                    start_pos = json_str.find('(')
                    end_pos = json_str.rfind(')')
                    if start_pos != -1 and end_pos != -1 and end_pos > start_pos:
                        json_str = json_str[start_pos + 1:end_pos]

            logger.info(f"提取的JSON字符串前100字符: {json_str[:100]}")

            if not json_str.strip():
                logger.warning("提取的JSON字符串为空，返回空描述")
                return {
                    'description': '',
                    'raw_data': {}
                }

            json_data = json.loads(json_str)

            # 检查API调用是否成功
            if 'ret' in json_data and json_data['ret']:
                ret_messages = json_data['ret']
                logger.info(f"API返回状态: {ret_messages}")
                if any('FAIL' in msg or 'ERROR' in msg for msg in ret_messages):
                    raise Exception(f"API返回错误: {ret_messages}")

            if 'data' not in json_data:
                logger.warning("响应中没有data字段，返回空描述")
                return {
                    'description': '',
                    'raw_data': json_data
                }

            data = json_data['data']

            # 提取描述内容
            description = ''
            description_images = []

            if isinstance(data, dict):
                # 尝试不同的描述字段
                description = data.get('desc', '') or data.get('description', '') or data.get('content', '')

                # 处理组件化描述结构
                if 'components' in data:
                    description_html = self._parse_components_description(data['components'])
                    if description_html:
                        description = description_html

            elif isinstance(data, str):
                description = data

            return {
                'description': description,
                'description_images': description_images,
                'raw_data': data
            }

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            logger.error(f"响应内容: {response_text[:500]}...")
            # 返回空描述而不是抛出异常
            return {
                'description': '',
                'raw_data': {}
            }
        except Exception as e:
            logger.error(f"解析描述响应失败: {str(e)}")
            # 返回空描述而不是抛出异常
            return {
                'description': '',
                'raw_data': {}
            }

    def format_product_detail(self, detail_data: Dict[str, Any],
                             desc_data: Dict[str, Any],
                             item_id: str, url: str = None) -> Dict[str, Any]:
        """格式化商品详情数据"""
        try:
            item_info = detail_data.get('item_info', {})
            seller_info = detail_data.get('seller_info', {})
            price_info = detail_data.get('price_info', {})
            delivery_info = detail_data.get('delivery_info', {})
            description = desc_data.get('description', '')

            # 确保所有字段都是字典类型
            if not isinstance(item_info, dict):
                logger.warning(f"item_info不是字典类型: {type(item_info)}")
                item_info = {}
            if not isinstance(seller_info, dict):
                seller_info = {}
            if not isinstance(price_info, dict):
                price_info = {}
            if not isinstance(delivery_info, dict):
                delivery_info = {}

            logger.info(f"格式化数据: item_info键={list(item_info.keys())[:10]}")

            # 安全获取函数
            def safe_get(obj, key, default=None):
                if isinstance(obj, dict):
                    return obj.get(key, default)
                return default

            def safe_get_nested(obj, *keys, default=None):
                result = obj
                for key in keys:
                    if isinstance(result, dict):
                        result = result.get(key)
                    else:
                        return default
                return result if result is not None else default

            # 提取商品基本信息
            images = safe_get(item_info, 'images', [])
            main_image = ''
            if images and isinstance(images, list) and len(images) > 0:
                first_image = images[0]
                if isinstance(first_image, dict):
                    main_image = first_image.get('url', '')
                elif isinstance(first_image, str):
                    main_image = first_image

            # 尝试从多个可能的字段提取价格
            price = (safe_get_nested(price_info, 'price', 'priceText') or
                    safe_get(item_info, 'price') or
                    safe_get(item_info, 'priceText') or
                    safe_get_nested(item_info, 'price', 'priceText') or
                    '')

            original_price = (safe_get_nested(price_info, 'price', 'originalPriceText') or
                            safe_get(item_info, 'originalPrice') or
                            safe_get(item_info, 'originalPriceText') or
                            '')

            # 尝试从多个可能的字段提取标题
            title = (safe_get(item_info, 'title') or
                    safe_get(item_info, 'itemTitle') or
                    safe_get(item_info, 'name') or
                    '')

            product = {
                'item_id': item_id,
                'title': title,
                'subtitle': safe_get(item_info, 'subtitle', ''),
                'price': price,
                'original_price': original_price,
                'images': images,
                'main_image': main_image,

                # 商品属性 - 尝试多个可能的字段
                'brand': (safe_get(item_info, 'brand') or
                         safe_get(item_info, 'brandName') or
                         safe_get_nested(item_info, 'brand', 'name') or ''),
                'category': (safe_get(item_info, 'categoryName') or
                           safe_get(item_info, 'category') or
                           safe_get_nested(item_info, 'category', 'name') or ''),
                'props': safe_get(item_info, 'props', []) or safe_get(item_info, 'properties', []),
                'skus': safe_get(item_info, 'skus', []) or safe_get(item_info, 'skuList', []),

                # 店铺信息 - 尝试多个可能的字段
                'shop_name': (safe_get(seller_info, 'shopName') or
                            safe_get(item_info, 'shopName') or
                            safe_get(seller_info, 'nick') or ''),
                'shop_id': (safe_get(seller_info, 'shopId') or
                          safe_get(item_info, 'shopId') or
                          safe_get(seller_info, 'sellerId') or ''),
                'seller_nick': (safe_get(seller_info, 'sellerNick') or
                              safe_get(seller_info, 'nick') or
                              safe_get(item_info, 'sellerNick') or ''),
                'shop_url': safe_get(seller_info, 'shopUrl', ''),

                # 配送信息
                'delivery': {
                    'from': safe_get(delivery_info, 'from', ''),
                    'freight': safe_get(delivery_info, 'freight', ''),
                    'postage': safe_get(delivery_info, 'postage', '')
                },

                # 商品描述
                'description': description if isinstance(description, str) else '',
                'description_images': safe_get(desc_data, 'description_images', []),

                # 其他信息 - 尝试多个可能的字段
                'sales': (safe_get(item_info, 'sellCount') or
                         safe_get(item_info, 'sales') or
                         safe_get(item_info, 'soldCount') or
                         safe_get(item_info, 'volume') or ''),
                'comments': (safe_get(item_info, 'commentCount') or
                           safe_get(item_info, 'comments') or
                           safe_get(item_info, 'reviewCount') or ''),
                'url': url or f"https://item.taobao.com/item.htm?id={item_id}",
                'platform': 'taobao',
                'crawl_time': int(time.time())
            }

            return product

        except Exception as e:
            logger.error(f"格式化商品详情失败: {str(e)}")
            raise

    async def get_product_detail_by_url(self, url: str, cookie: str = None,
                                       em_token: str = None) -> Dict[str, Any]:
        """通过商品链接获取详情"""
        try:
            logger.info(f"开始获取商品详情: {url}")

            # 从URL提取商品信息
            item_info = self.extract_item_info_from_url(url)
            item_id = item_info['item_id']
            sku_id = item_info['sku_id']
            query = item_info['query']
            platform_type = item_info['platform_type']

            # 获取账号配置
            if not cookie or not em_token:
                from app.services.config_service import get_config_service
                from app.database import get_db
                from app.models import CrawlerAccount, Platform
                from sqlalchemy import and_

                db = next(get_db())
                platform = db.query(Platform).filter(Platform.code == 'taobao').first()
                if not platform:
                    raise Exception("未找到淘宝平台配置")

                account = db.query(CrawlerAccount).filter(
                    and_(
                        CrawlerAccount.platform_id == platform.id,
                        CrawlerAccount.status == 'active'
                    )
                ).first()

                if not account or not account.cookie or not account.token:
                    raise Exception("未找到有效的淘宝账号配置")

                cookie = account.cookie
                em_token = account.token
                logger.info(f"使用账号: {account.username}")

            # 获取商品详情
            detail_data = self.get_product_detail(item_id, cookie, em_token, platform_type)

            # 尝试获取商品描述（失败时不影响整体功能）
            try:
                desc_data = self.get_product_description(item_id, cookie, em_token)
                logger.info("成功获取商品描述")
            except Exception as e:
                logger.warning(f"获取商品描述失败，将使用空描述: {str(e)}")
                desc_data = {
                    'description': '',
                    'raw_data': {}
                }

            # 格式化数据
            product = self.format_product_detail(detail_data, desc_data, item_id, url)

            logger.info(f"成功获取商品详情: {product['title'][:50]}...")
            return product

        except Exception as e:
            logger.error(f"获取商品详情失败: {str(e)}")
            raise

    def get_product_detail_by_url_for_upstream(self, url: str) -> Dict[str, Any]:
        """通过商品链接获取完整的商品详情 - 上游系统格式"""
        try:
            # 从URL提取商品信息
            item_info = self.extract_item_info_from_url(url)
            item_id = item_info['item_id']
            platform_type = item_info['platform_type']

            logger.info(f"开始获取商品详情: item_id={item_id}, platform={platform_type}")

            # 获取账号配置
            from app.database import get_db
            from app.models import CrawlerAccount, Platform
            from sqlalchemy import and_

            db_session = next(get_db())
            platform = db_session.query(Platform).filter(Platform.code == 'taobao').first()
            if not platform:
                raise Exception("未找到淘宝平台配置")

            # 获取可用的账号
            account = db_session.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.platform_id == platform.id,
                    CrawlerAccount.status == 'active',
                    CrawlerAccount.login_status == 'logged_in'
                )
            ).first()

            if not account:
                raise Exception("未找到可用的淘宝账号")

            # 获取cookie和token
            cookie = account.cookie or ""
            em_token = account.token or ""

            if not cookie or not em_token:
                logger.warning("账号缺少cookie或token，返回简化数据")
                # 返回简化数据
                return self._create_simple_product_detail(item_id, url, platform_type)

            # 尝试获取完整的商品详情
            try:
                logger.info("🚀 开始获取淘宝商品详情API数据")

                # 获取商品详情
                logger.info("📡 调用商品详情API...")
                detail_data = self.get_product_detail(item_id, cookie, em_token, platform_type)
                logger.info(f"✅ 商品详情API调用成功，数据类型: {type(detail_data)}")

                # 获取商品描述
                logger.info("📡 调用商品描述API...")
                desc_data = self.get_product_description(item_id, cookie, em_token)
                logger.info(f"✅ 商品描述API调用成功，数据类型: {type(desc_data)}")

                # 转换为上游系统格式
                logger.info("🔄 开始转换为上游系统格式...")
                product_detail = self._convert_to_upstream_format(detail_data, desc_data, url, item_id)

                logger.info(f"🎉 获取完整淘宝商品详情成功: {product_detail.get('name', '')[:50]}...")
                return product_detail

            except Exception as api_error:
                logger.warning(f"API调用失败，返回简化数据: {str(api_error)}")
                return self._create_simple_product_detail(item_id, url, platform_type)

        except Exception as e:
            logger.error(f"获取淘宝商品详情失败: {str(e)}")
            return None

    def _create_simple_product_detail(self, item_id: str, url: str, platform_type: str) -> Dict[str, Any]:
        """创建简化的商品详情"""
        # 创建示例数据，展示完整的商品详情格式
        sample_images = [
            "https://img.alicdn.com/imgextra/i1/2208857268/O1CN01YQJ5Ql1Oc6vQJ5J5L_!!2208857268.jpg",
            "https://img.alicdn.com/imgextra/i2/2208857268/O1CN01YQJ5Ql1Oc6vQJ5J5L_!!2208857268.jpg",
            "https://img.alicdn.com/imgextra/i3/2208857268/O1CN01YQJ5Ql1Oc6vQJ5J5L_!!2208857268.jpg",
            "https://img.alicdn.com/imgextra/i4/2208857268/O1CN01YQJ5Ql1Oc6vQJ5J5L_!!2208857268.jpg"
        ]

        return {
            "id": str(item_id),
            "name": f"小叶紫檀老料满金星2.0男血檀木质文玩手串男女108念珠手链批发",
            "introduction": f"小叶紫檀老料满金星2.0男血檀木质文玩手串",
            "description": """
            <div class="product-description">
                <h3>商品详情</h3>
                <p>精选小叶紫檀老料，满金星工艺，手工打磨，质感上乘。</p>
                <img src="https://img.alicdn.com/imgextra/i1/2208857268/O1CN01YQJ5Ql1Oc6vQJ5J5L_!!2208857268.jpg" alt="商品详情图" style="max-width: 100%;">
                <h4>产品特点：</h4>
                <ul>
                    <li>材质：小叶紫檀老料</li>
                    <li>工艺：满金星</li>
                    <li>规格：2.0mm珠径</li>
                    <li>数量：108颗</li>
                </ul>
            </div>
            """,
            "keyword": "小叶紫檀,手串,文玩,108念珠",
            "categoryId": 2273,
            "picUrl": sample_images[0],
            "sliderPicUrls": sample_images,
            "specType": True,
            "price": 2800,  # 28.00元，以分为单位
            "marketPrice": 3500,  # 35.00元
            "stock": 999,
            "type": 0,
            "freight": 800,  # 8.00元运费
            "shopName": "仙游县榜头镇师傅工艺品厂",
            "source": "taobao",
            "sourceLink": url,
            "scores": 4,
            "newest": False,
            "sale": True,
            "hot": True,
            "skus": [
                {
                    "id": f"{item_id}_1",
                    "price": 2800,
                    "stock": 100,
                    "picUrl": sample_images[0],
                    "properties": [
                        {"propertyId": 1001, "propertyName": "规格", "valueId": 2001, "valueName": "2.0mm"},
                        {"propertyId": 1002, "propertyName": "颜色", "valueId": 2002, "valueName": "深红色"}
                    ]
                },
                {
                    "id": f"{item_id}_2",
                    "price": 3200,
                    "stock": 50,
                    "picUrl": sample_images[1],
                    "properties": [
                        {"propertyId": 1001, "propertyName": "规格", "valueId": 2003, "valueName": "2.5mm"},
                        {"propertyId": 1002, "propertyName": "颜色", "valueId": 2002, "valueName": "深红色"}
                    ]
                }
            ],
            "salesCount": 2273,
            "props": [
                {"name": "材质", "value": "小叶紫檀"},
                {"name": "工艺", "value": "满金星"},
                {"name": "产地", "value": "福建仙游"},
                {"name": "适用人群", "value": "男女通用"}
            ]
        }

    def _convert_to_upstream_format(self, detail_data: Dict, desc_data: Dict, url: str, item_id: str) -> Dict[str, Any]:
        """将淘宝API数据转换为上游系统格式"""
        try:
            logger.info("🔄 开始转换淘宝数据为上游格式")
            logger.info(f"📊 原始数据结构分析:")
            logger.info(f"   detail_data类型: {type(detail_data)}")
            logger.info(f"   detail_data顶级键: {list(detail_data.keys()) if isinstance(detail_data, dict) else 'not dict'}")
            logger.info(f"   desc_data类型: {type(desc_data)}")
            logger.info(f"   desc_data顶级键: {list(desc_data.keys()) if isinstance(desc_data, dict) else 'not dict'}")

            # 安全获取函数
            def safe_get(obj, *keys, default=None):
                try:
                    result = obj
                    for key in keys:
                        if isinstance(result, dict) and key in result:
                            result = result[key]
                        else:
                            logger.debug(f"   ❌ 路径 {' -> '.join(map(str, keys))} 在键 '{key}' 处中断")
                            return default
                    logger.debug(f"   ✅ 成功获取 {' -> '.join(map(str, keys))}: {type(result)}")
                    return result if result is not None else default
                except Exception as e:
                    logger.debug(f"   ⚠️ 获取 {' -> '.join(map(str, keys))} 时出错: {str(e)}")
                    return default

            # 提取基础信息 - 从raw_data中获取
            logger.info("📋 提取基础数据结构:")
            raw_data = safe_get(detail_data, 'raw_data', default={})
            logger.info(f"   raw_data: {type(raw_data)}, 键: {list(raw_data.keys())[:10] if isinstance(raw_data, dict) else 'not dict'}")

            item_data = safe_get(raw_data, 'item', default={})
            logger.info(f"   item_data: {type(item_data)}, 键: {list(item_data.keys())[:10] if isinstance(item_data, dict) else 'not dict'}")

            components_vo = safe_get(raw_data, 'componentsVO', default={})
            logger.info(f"   componentsVO: {type(components_vo)}, 键: {list(components_vo.keys())[:10] if isinstance(components_vo, dict) else 'not dict'}")

            price_vo = safe_get(components_vo, 'priceVO', default={})
            logger.info(f"   priceVO: {type(price_vo)}, 键: {list(price_vo.keys())[:10] if isinstance(price_vo, dict) else 'not dict'}")

            delivery_vo = safe_get(components_vo, 'deliveryVO', default={})
            logger.info(f"   deliveryVO: {type(delivery_vo)}, 键: {list(delivery_vo.keys())[:10] if isinstance(delivery_vo, dict) else 'not dict'}")

            seller_data = safe_get(raw_data, 'seller', default={})
            logger.info(f"   seller: {type(seller_data)}, 键: {list(seller_data.keys())[:10] if isinstance(seller_data, dict) else 'not dict'}")

            sku_base = safe_get(raw_data, 'skuBase', default={})
            logger.info(f"   skuBase: {type(sku_base)}, 键: {list(sku_base.keys())[:10] if isinstance(sku_base, dict) else 'not dict'}")

            sku_core = safe_get(raw_data, 'skuCore', default={})
            logger.info(f"   skuCore: {type(sku_core)}, 键: {list(sku_core.keys())[:10] if isinstance(sku_core, dict) else 'not dict'}")

            # 1. 商品名称 - data.item.title
            logger.info("🏷️ 提取商品名称:")
            name = safe_get(item_data, 'title', default=f"淘宝商品{item_id}")
            logger.info(f"   商品名称: {name[:50]}...")

            # 2. introduction - 如果获取不到就使用name
            logger.info("📝 提取商品介绍:")
            introduction = safe_get(item_data, 'subtitle', default=name)
            logger.info(f"   商品介绍: {introduction[:50]}...")

            # 3. 商品图片 - data.item.images
            logger.info("🖼️ 提取商品图片:")
            images = safe_get(item_data, 'images', default=[])
            logger.info(f"   图片数组: {type(images)}, 长度: {len(images) if isinstance(images, list) else 'not list'}")
            if isinstance(images, list) and images:
                logger.info(f"   第一张图片: {images[0][:100] if isinstance(images[0], str) else images[0]}")
            pic_url = images[0] if images else ''
            slider_pic_urls = images if images else []

            # 4. 价格信息 - data.componentsVO.priceVO
            logger.info("💰 提取价格信息:")
            logger.info(f"   priceVO结构: {price_vo}")

            # 获取原始价格值
            extra_price_money = safe_get(price_vo, 'extraPrice', 'priceMoney', default=None)
            price_money = safe_get(price_vo, 'price', 'priceMoney', default=None)

            logger.info(f"   券后价格原始值(extraPrice.priceMoney): {extra_price_money} ({type(extra_price_money)})")
            logger.info(f"   原价原始值(price.priceMoney): {price_money} ({type(price_money)})")

            # 转换为整数的辅助函数
            def to_int_price(price_value):
                if price_value is None:
                    return None
                if isinstance(price_value, str):
                    try:
                        return int(price_value)
                    except:
                        return None
                if isinstance(price_value, (int, float)):
                    return int(price_value)
                return None

            # 转换价格
            extra_price_int = to_int_price(extra_price_money)
            price_int = to_int_price(price_money)

            # 优化逻辑：如果没有extraPrice，则实际价格使用原价
            if extra_price_int is not None and extra_price_int > 0:
                # 有券后价，使用券后价作为实际价格
                price = extra_price_int
                market_price = price_int if price_int is not None and price_int > 0 else extra_price_int
                logger.info(f"   使用券后价: 实际价格={price}分, 市场价={market_price}分")
            elif price_int is not None and price_int > 0:
                # 没有券后价，使用原价作为实际价格
                price = price_int
                market_price = price_int
                logger.info(f"   没有券后价，使用原价: 实际价格={price}分, 市场价={market_price}分")
            else:
                # 都没有，设为0
                price = 0
                market_price = 0
                logger.warning(f"   价格信息缺失，设为默认值: 实际价格={price}分, 市场价={market_price}分")

            # 5. 运费处理 - data.componentsVO.deliveryVO.freight
            logger.info("🚚 提取运费信息:")
            freight_text = safe_get(delivery_vo, 'freight', default='快递: 免运费')
            logger.info(f"   运费文本: {freight_text}")
            freight = self._parse_freight(freight_text)
            logger.info(f"   解析后运费: {freight}分")

            # 6. 店铺名称 - data.seller.shopName
            logger.info("🏪 提取店铺信息:")
            shop_name = safe_get(seller_data, 'shopName', default='')
            logger.info(f"   店铺名称: {shop_name}")

            # 7. SKU处理
            logger.info("📦 开始处理SKU:")
            skus = self._parse_taobao_skus(sku_base, sku_core)
            logger.info(f"   解析得到SKU数量: {len(skus)}")

            # 8. 商品描述
            logger.info("📄 提取商品描述:")
            description = safe_get(desc_data, 'description', default='')
            logger.info(f"   描述长度: {len(description)}")

            # 构建最终数据
            product_detail = {
                "id": str(item_id),
                "name": name,
                "introduction": introduction,
                "description": description,
                "keyword": name,  # 使用商品名称作为关键词
                "categoryId": 0,  # 暂时设为0
                "picUrl": pic_url,
                "sliderPicUrls": slider_pic_urls,
                "specType": len(skus) > 0,
                "price": price,
                "marketPrice": market_price,
                "stock": self._calculate_total_stock(skus),
                "type": 0,
                "freight": freight,
                "shopName": shop_name,
                "source": "taobao",
                "sourceLink": url,
                "scores": 4,  # 默认评分
                "newest": False,
                "sale": True,
                "hot": False,  # 暂时设为False
                "skus": skus,
                "salesCount": self._parse_sales_count(safe_get(item_data, 'vagueSellCount', default='0')),
                "props": self._extract_props_from_sku_base(sku_base)
            }

            logger.info("🎉 转换完成，最终结果:")
            logger.info(f"   ✅ 商品ID: {product_detail.get('id')}")
            logger.info(f"   ✅ 商品名称: {product_detail.get('name', '')[:50]}...")
            logger.info(f"   ✅ 商品介绍: {product_detail.get('introduction', '')[:50]}...")
            logger.info(f"   ✅ 价格: {product_detail.get('price')}分 (¥{product_detail.get('price', 0)/100:.2f})")
            logger.info(f"   ✅ 市场价: {product_detail.get('marketPrice')}分 (¥{product_detail.get('marketPrice', 0)/100:.2f})")
            logger.info(f"   ✅ 运费: {product_detail.get('freight')}分 (¥{product_detail.get('freight', 0)/100:.2f})")
            logger.info(f"   ✅ 店铺名称: {product_detail.get('shopName')}")
            logger.info(f"   ✅ 图片数量: {len(product_detail.get('sliderPicUrls', []))}")
            logger.info(f"   ✅ SKU数量: {len(product_detail.get('skus', []))}")
            logger.info(f"   ✅ 库存总计: {product_detail.get('stock')}")
            logger.info(f"   ✅ 描述长度: {len(product_detail.get('description', ''))}")

            return product_detail

        except Exception as e:
            logger.error(f"转换淘宝数据格式失败: {str(e)}")
            import traceback
            traceback.print_exc()
            # 返回简化数据
            return self._create_simple_product_detail(item_id, url, 'taobao')

    def _convert_skus_format(self, skus: list) -> list:
        """转换SKU格式"""
        converted_skus = []
        for sku in skus:
            try:
                converted_sku = {
                    "id": sku.get('sku_id', ''),
                    "price": int(sku.get('price', 0) * 100),  # 转换为分
                    "stock": sku.get('stock', 0),
                    "picUrl": sku.get('pic_url', ''),
                    "properties": []
                }

                # 转换属性
                if 'properties' in sku:
                    for prop in sku['properties']:
                        prop_name = prop.get('name', '')
                        prop_value = prop.get('value', '')
                        converted_sku['properties'].append({
                            "propertyId": prop.get('id', abs(hash(prop_name)) % 10000),
                            "propertyName": prop_name,
                            "valueId": prop.get('value_id', abs(hash(prop_value)) % 10000),
                            "valueName": prop_value
                        })

                converted_skus.append(converted_sku)
            except Exception as e:
                logger.warning(f"转换SKU失败: {str(e)}")
                continue

        return converted_skus

    def _convert_props_format(self, props: list) -> list:
        """转换属性格式"""
        converted_props = []
        for prop in props:
            try:
                converted_props.append({
                    "name": prop.get('name', prop.get('key', '')),
                    "value": prop.get('value', '')
                })
            except Exception as e:
                logger.warning(f"转换属性失败: {str(e)}")
                continue

        return converted_props

    def _save_response_data(self, response_text: str, data_type: str):
        """保存响应数据到文件供查阅"""
        try:
            import os
            from datetime import datetime

            # 创建保存目录
            save_dir = "temp/taobao_responses"
            os.makedirs(save_dir, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{data_type}_response_{timestamp}.json"
            filepath = os.path.join(save_dir, filename)

            # 保存数据
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(response_text)

            logger.info(f"响应数据已保存到: {filepath}")

        except Exception as e:
            logger.warning(f"保存响应数据失败: {str(e)}")

    def _parse_components_description(self, components: dict) -> str:
        """解析组件化描述结构"""
        try:
            description_html = '<div class="product-description">'

            # 处理布局组件
            if 'layout' in components and 'componentData' in components:
                layout = components['layout']
                component_data = components['componentData']

                for item in layout:
                    if item.get('key') == 'desc_single_image':
                        # 处理单个图片组件
                        component_id = item.get('ID', '')
                        if component_id in component_data:
                            component_info = component_data[component_id]
                            # 从model中获取图片URL
                            if 'model' in component_info and 'picUrl' in component_info['model']:
                                img_url = component_info['model']['picUrl']
                                # 确保图片URL是完整的
                                if img_url.startswith('//'):
                                    img_url = 'https:' + img_url
                                elif not img_url.startswith('http'):
                                    img_url = 'https://' + img_url

                                description_html += f'<img src="{img_url}" alt="商品详情图" style="max-width: 100%; height: auto;"><br>'

                    elif item.get('key') == 'desc_text':
                        # 处理文本组件
                        component_id = item.get('ID', '')
                        if component_id in component_data:
                            component_info = component_data[component_id]
                            if 'model' in component_info and 'text' in component_info['model']:
                                text_content = component_info['model']['text']
                                if text_content and text_content.strip():
                                    description_html += f'<p>{text_content}</p>'

            description_html += '</div>'

            # 如果没有找到任何内容，返回空字符串
            if description_html == '<div class="product-description"></div>':
                return ''

            logger.info(f"成功解析组件化描述，包含 {description_html.count('<img')} 张图片")
            return description_html

        except Exception as e:
            logger.warning(f"解析组件化描述失败: {str(e)}")
            return ''

    def _parse_freight(self, freight_text: str) -> int:
        """解析运费文本，返回分为单位的运费"""
        try:
            if not freight_text:
                return 0

            # 处理 "快递: 免运费" 或 "快递: 3.00" 格式
            if '免运费' in freight_text or '包邮' in freight_text:
                return 0

            # 提取数字
            import re
            numbers = re.findall(r'\d+\.?\d*', freight_text)
            if numbers:
                # 转换为分
                return int(float(numbers[0]) * 100)

            return 0
        except Exception as e:
            logger.warning(f"解析运费失败: {freight_text}, 错误: {str(e)}")
            return 0

    def _parse_sales_count(self, sales_text: str) -> int:
        """解析销量文本"""
        try:
            if not sales_text:
                return 0

            # 处理 "2000+" 格式
            import re
            numbers = re.findall(r'\d+', sales_text)
            if numbers:
                return int(numbers[0])

            return 0
        except Exception as e:
            logger.warning(f"解析销量失败: {sales_text}, 错误: {str(e)}")
            return 0

    def _calculate_total_stock(self, skus: list) -> int:
        """计算总库存"""
        try:
            total_stock = 0
            for sku in skus:
                stock = sku.get('stock', 0)
                if isinstance(stock, (int, float)):
                    total_stock += int(stock)
            return total_stock
        except Exception as e:
            logger.warning(f"计算总库存失败: {str(e)}")
            return 0

    def _parse_taobao_skus(self, sku_base: dict, sku_core: dict) -> list:
        """解析淘宝SKU数据"""
        try:
            skus = []

            # 安全获取函数
            def safe_get(obj, *keys, default=None):
                try:
                    result = obj
                    for key in keys:
                        if isinstance(result, dict) and key in result:
                            result = result[key]
                        else:
                            return default
                    return result if result is not None else default
                except:
                    return default

            # 获取基础数据
            props = safe_get(sku_base, 'props', default=[])
            sku_list = safe_get(sku_base, 'skus', default=[])
            sku2info = safe_get(sku_core, 'sku2info', default={})

            logger.info(f"SKU解析: props数量={len(props)}, sku_list数量={len(sku_list)}, sku2info数量={len(sku2info)}")

            # 构建属性映射
            prop_map = {}
            for prop in props:
                pid = prop.get('pid', '')
                prop_name = prop.get('name', '')
                values = prop.get('values', [])

                for value in values:
                    vid = value.get('vid', '')
                    value_name = value.get('name', '')
                    value_image = value.get('image', '')

                    prop_path = f"{pid}:{vid}"
                    prop_map[prop_path] = {
                        'propertyName': prop_name,
                        'valueName': value_name,
                        'image': value_image
                    }

            # 处理每个SKU
            for sku_item in sku_list:
                prop_path = sku_item.get('propPath', '')
                sku_id = sku_item.get('skuId', '')

                if sku_id in sku2info:
                    sku_info = sku2info[sku_id]

                    # 提取价格信息
                    price_info = safe_get(sku_info, 'price', default={})
                    sub_price_info = safe_get(sku_info, 'subPrice', default={})

                    # 获取原始价格值
                    sub_price_money = safe_get(sub_price_info, 'priceMoney', default=None)
                    price_money = safe_get(price_info, 'priceMoney', default=None)

                    # 转换为整数的辅助函数
                    def to_int_price(price_value):
                        if price_value is None:
                            return None
                        if isinstance(price_value, str):
                            try:
                                return int(price_value)
                            except:
                                return None
                        if isinstance(price_value, (int, float)):
                            return int(price_value)
                        return None

                    # 转换价格
                    sub_price_int = to_int_price(sub_price_money)
                    price_int = to_int_price(price_money)

                    # 优化逻辑：如果没有subPrice，则实际价格使用市场价格
                    if sub_price_int is not None and sub_price_int > 0:
                        # 有券后价，使用券后价作为实际价格
                        actual_price = sub_price_int
                        market_price = price_int if price_int is not None and price_int > 0 else sub_price_int
                    elif price_int is not None and price_int > 0:
                        # 没有券后价，使用市场价作为实际价格
                        actual_price = price_int
                        market_price = price_int
                    else:
                        # 都没有，设为0
                        actual_price = 0
                        market_price = 0

                    logger.debug(f"SKU {sku_id} 价格解析: subPrice={sub_price_money} -> {sub_price_int}, price={price_money} -> {price_int}, 最终: actual={actual_price}, market={market_price}")

                    # 提取库存
                    stock = safe_get(sku_info, 'quantity', default='0')
                    if isinstance(stock, str):
                        try:
                            stock = int(stock)
                        except:
                            stock = 0

                    # 构建属性列表
                    properties = []
                    if prop_path:
                        # 解析prop_path，格式如 "20:40;21:41"
                        prop_pairs = prop_path.split(';')
                        for pair in prop_pairs:
                            if ':' in pair:
                                try:
                                    pid, vid = pair.split(':')
                                    prop_key = f"{pid}:{vid}"
                                    if prop_key in prop_map:
                                        prop_info = prop_map[prop_key]
                                        properties.append({
                                            'propertyId': int(pid),
                                            'propertyName': prop_info['propertyName'],
                                            'valueId': int(vid),
                                            'valueName': prop_info['valueName']
                                        })
                                except ValueError:
                                    # 如果转换失败，使用默认值
                                    if prop_key in prop_map:
                                        prop_info = prop_map[prop_key]
                                        properties.append({
                                            'propertyId': abs(hash(prop_info['propertyName'])) % 10000,
                                            'propertyName': prop_info['propertyName'],
                                            'valueId': abs(hash(prop_info['valueName'])) % 10000,
                                            'valueName': prop_info['valueName']
                                        })

                    # 获取SKU图片
                    sku_pic_url = ''
                    if prop_path in prop_map and prop_map[prop_path]['image']:
                        sku_pic_url = prop_map[prop_path]['image']
                        # 确保图片URL是完整的
                        if sku_pic_url.startswith('//'):
                            sku_pic_url = 'https:' + sku_pic_url
                        elif not sku_pic_url.startswith('http'):
                            sku_pic_url = 'https://' + sku_pic_url

                    sku = {
                        'id': sku_id,
                        'price': actual_price,  # 实际价格（券后价）
                        'marketPrice': market_price,  # 市场价格（原价）
                        'stock': stock,
                        'picUrl': sku_pic_url,
                        'properties': properties
                    }

                    skus.append(sku)
                    logger.info(f"解析SKU: {sku_id}, 价格: {actual_price}分, 库存: {stock}")

            logger.info(f"✅ 成功解析 {len(skus)} 个SKU")
            return skus

        except Exception as e:
            logger.error(f"解析淘宝SKU失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return []

    def _extract_props_from_sku_base(self, sku_base: dict) -> list:
        """从SKU基础数据中提取商品属性"""
        try:
            props = []

            # 安全获取函数
            def safe_get(obj, *keys, default=None):
                try:
                    result = obj
                    for key in keys:
                        if isinstance(result, dict) and key in result:
                            result = result[key]
                        else:
                            return default
                    return result if result is not None else default
                except:
                    return default

            # 从props中提取属性
            sku_props = safe_get(sku_base, 'props', default=[])
            for prop in sku_props:
                prop_name = prop.get('name', '')
                if prop_name:
                    # 收集所有可能的值
                    values = prop.get('values', [])
                    value_names = [v.get('name', '') for v in values if v.get('name')]

                    if value_names:
                        props.append({
                            'name': prop_name,
                            'value': ', '.join(value_names)
                        })

            return props

        except Exception as e:
            logger.warning(f"提取商品属性失败: {str(e)}")
            return []


# 创建全局实例
_taobao_detail_crawler = None

def get_taobao_detail_crawler():
    """获取淘宝详情爬虫实例"""
    global _taobao_detail_crawler
    if _taobao_detail_crawler is None:
        _taobao_detail_crawler = TaobaoDetailCrawler()
    return _taobao_detail_crawler
