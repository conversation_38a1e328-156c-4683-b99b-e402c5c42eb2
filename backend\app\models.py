"""
数据库模型定义
"""
from sqlalchemy import Column, Integer, BigInteger, String, Text, DateTime, Boolean, Enum as SQLEnum, Index, Numeric, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import enum

# 移除硬编码的平台枚举，改为动态平台管理

class PlatformStatus(enum.Enum):
    """平台状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEVELOPING = "developing"
    MAINTENANCE = "maintenance"

# AccountStatus枚举已废弃，CrawlerAccount使用字符串状态

class ProxyStatus(enum.Enum):
    """代理状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"

class ProxySource(enum.Enum):
    """代理来源枚举"""
    SELF_HOSTED = "self_hosted"      # 自建代理服务器
    THIRD_PARTY_API = "third_party_api"  # 第三方API代理
    STATIC = "static"                # 静态代理配置

class TranslationProvider(enum.Enum):
    """翻译服务提供商枚举"""
    GOOGLE = "google"
    BAIDU = "baidu"
    YOUDAO = "youdao"
    ALIYUN = "aliyun"

# 1. 平台管理表
class Platform(Base):
    __tablename__ = 'platforms'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    code = Column(String(50), unique=True, nullable=False, index=True)  # 平台代码，如 taobao, 1688
    name = Column(String(100), nullable=False)  # 平台名称，如 淘宝, 1688
    name_en = Column(String(100), nullable=True)  # 英文名称

    # 平台信息
    website_url = Column(String(255), nullable=True)  # 官网地址
    logo_url = Column(String(255), nullable=True)  # Logo地址
    description = Column(Text, nullable=True)  # 平台描述

    # 状态和配置
    status = Column(SQLEnum(PlatformStatus), default=PlatformStatus.DEVELOPING, index=True)
    is_enabled = Column(Boolean, default=True, index=True)  # 是否启用
    priority = Column(Integer, default=1)  # 优先级，数字越大优先级越高

    # 爬虫配置
    crawler_config = Column(Text, nullable=True)  # JSON格式的爬虫配置
    rate_limit = Column(Integer, default=60)  # 每分钟请求限制
    timeout = Column(Integer, default=30)  # 超时时间（秒）

    # 统计信息
    total_requests = Column(BigInteger, default=0)
    successful_requests = Column(BigInteger, default=0)
    failed_requests = Column(BigInteger, default=0)

    # 元数据
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    created_by = Column(String(100), nullable=True)

    # 关系（手动查询，不使用外键）
    # crawler_accounts 通过 platform_id 手动查询

    # 索引
    __table_args__ = (
        Index('idx_code_status', 'code', 'status'),
        Index('idx_enabled_priority', 'is_enabled', 'priority'),
    )

# 2. 平台账号表已废弃，统一使用CrawlerAccount表

# 2. 代理池表
class ProxyPool(Base):
    __tablename__ = 'proxy_pools'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    provider_name = Column(String(100), nullable=False)

    # 代理服务器信息
    host = Column(String(255), nullable=True)  # 代理服务器地址
    port = Column(Integer, nullable=True)  # 代理端口
    username = Column(String(100), nullable=True)  # 代理用户名
    password = Column(String(255), nullable=True)  # 代理密码（加密存储）

    proxy_type = Column(String(50), nullable=False)  # residential, datacenter, mobile
    proxy_source = Column(SQLEnum(ProxySource), default=ProxySource.STATIC, index=True)  # 代理来源
    api_config = Column(Text, nullable=True)  # 第三方API配置（JSON格式）
    test_url = Column(String(255), default='https://httpbin.org/ip')  # 测试URL

    # 地理位置信息
    country = Column(String(50), nullable=True)  # 代理所在国家
    region = Column(String(100), nullable=True)  # 代理所在地区

    # 状态和性能
    status = Column(SQLEnum(ProxyStatus), default=ProxyStatus.TESTING, index=True)
    success_rate = Column(Numeric(5, 2), default=0.00)
    avg_response_time = Column(Integer, default=0)  # 毫秒

    # 使用统计
    total_requests = Column(BigInteger, default=0)
    successful_requests = Column(BigInteger, default=0)
    failed_requests = Column(BigInteger, default=0)
    consecutive_failures = Column(Integer, default=0)  # 连续失败次数

    # 配置
    priority = Column(Integer, default=1)  # 1-10, 数字越大优先级越高
    is_enabled = Column(Boolean, default=True, index=True)

    # 元数据
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_checked_at = Column(DateTime, nullable=True)
    last_test_at = Column(DateTime, nullable=True)  # 最后测试时间
    last_success_at = Column(DateTime, nullable=True)  # 最后成功时间

    # 索引
    __table_args__ = (
        Index('idx_status_priority', 'status', 'priority'),
        Index('idx_host_port', 'host', 'port'),
        Index('idx_proxy_source', 'proxy_source'),
        Index('idx_last_test', 'last_test_at'),
        Index('idx_consecutive_failures', 'consecutive_failures'),
    )

    @property
    def proxy_url(self):
        """获取代理URL"""
        if not self.host or not self.port:
            return None

        if self.username and self.password:
            return f"http://{self.username}:{self.password}@{self.host}:{self.port}"
        else:
            return f"http://{self.host}:{self.port}"

    @property
    def is_healthy(self):
        """判断代理是否健康"""
        if self.status != ProxyStatus.ACTIVE:
            return False
        if self.consecutive_failures >= 3:
            return False
        if self.success_rate < 50:  # 成功率低于50%认为不健康
            return False
        return True


# 代理测试日志表
class ProxyTestLog(Base):
    __tablename__ = 'proxy_test_logs'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    proxy_id = Column(BigInteger, nullable=False, index=True)

    # 测试信息
    test_time = Column(DateTime, default=func.now(), nullable=False)
    test_result = Column(String(20), nullable=False)  # success, failed, timeout
    response_time_ms = Column(Integer, nullable=True)
    error_message = Column(Text, nullable=True)
    test_ip = Column(String(45), nullable=True)  # 测试获得的IP地址
    test_url = Column(String(255), nullable=True)  # 测试使用的URL
    user_agent = Column(String(500), nullable=True)  # 测试使用的User-Agent

    # 元数据
    created_at = Column(DateTime, default=func.now())

    # 索引
    __table_args__ = (
        Index('idx_proxy_time', 'proxy_id', 'test_time'),
        Index('idx_test_result', 'test_result'),
        Index('idx_test_time', 'test_time'),
    )

# 3. 翻译服务配置表
class TranslationService(Base):
    __tablename__ = 'translation_services'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    provider = Column(SQLEnum(TranslationProvider), nullable=False, index=True)
    service_name = Column(String(100), nullable=False)
    
    # 状态和性能
    status = Column(String(50), default='active', index=True)
    priority = Column(Integer, default=1)
    is_default = Column(Boolean, default=False)
    
    # 使用统计
    total_characters = Column(BigInteger, default=0)
    total_requests = Column(BigInteger, default=0)
    successful_requests = Column(BigInteger, default=0)
    avg_response_time = Column(Integer, default=0)
    
    # 限制和配额
    daily_limit = Column(BigInteger, nullable=True)
    monthly_limit = Column(BigInteger, nullable=True)
    current_daily_usage = Column(BigInteger, default=0)
    current_monthly_usage = Column(BigInteger, default=0)
    
    # 元数据
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

# 4. API调用日志表
class ApiCallLog(Base):
    __tablename__ = 'api_call_logs'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    request_id = Column(String(36), unique=True, index=True)
    
    # 请求信息
    client_ip = Column(String(45), nullable=False, index=True)
    user_agent = Column(Text, nullable=True)
    request_method = Column(String(10), nullable=False)
    request_path = Column(String(255), nullable=False)
    
    # 业务信息
    platform_id = Column(BigInteger, nullable=True, index=True)
    search_type = Column(String(50), nullable=True)  # keyword, url
    search_query = Column(Text, nullable=True)

    # 关联关系（手动查询，不使用外键）
    # platform 通过 platform_id 手动查询
    
    # 响应信息
    response_code = Column(Integer, nullable=False, index=True)
    response_time_ms = Column(Integer, nullable=False)
    response_size = Column(Integer, nullable=True)
    
    # 服务使用情况
    proxy_used = Column(String(100), nullable=True)
    account_used = Column(String(100), nullable=True)
    translation_used = Column(String(50), nullable=True)
    
    # 错误信息
    error_type = Column(String(100), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime, default=func.now(), nullable=False, index=True)
    
    # 索引优化
    __table_args__ = (
        Index('idx_ip_time', 'client_ip', 'created_at'),
        Index('idx_platform_time', 'platform_id', 'created_at'),
        Index('idx_response_code_time', 'response_code', 'created_at'),
    )

# 5. 系统配置表
class SystemConfig(Base):
    __tablename__ = 'system_configs'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    category = Column(String(50), nullable=False, index=True)  # crawler, translation, security
    config_key = Column(String(100), nullable=False, index=True)
    config_value = Column(Text, nullable=False)
    value_type = Column(String(20), default='string')  # string, int, float, bool, json
    description = Column(String(255), nullable=True)
    is_sensitive = Column(Boolean, default=False)  # 是否为敏感配置
    
    # 元数据
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    updated_by = Column(String(100), nullable=True)
    
    # 唯一约束
    __table_args__ = (
        Index('idx_category_key', 'category', 'config_key', unique=True),
    )

# 6. 管理员用户表
class AdminUser(Base):
    __tablename__ = 'admin_users'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False)
    
    # 认证信息
    password_hash = Column(String(255), nullable=False)
    
    # 权限和状态
    role = Column(String(50), default='operator')  # admin, operator, viewer
    is_active = Column(Boolean, default=True, index=True)
    
    # 登录信息
    last_login_at = Column(DateTime, nullable=True)
    login_count = Column(Integer, default=0)
    
    # 元数据
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


# 爬虫账号配置表
class CrawlerAccount(Base):
    """爬虫账号配置表"""
    __tablename__ = "crawler_accounts"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    platform_id = Column(BigInteger, nullable=False, index=True)
    username = Column(String(100), nullable=False)
    display_name = Column(String(100))

    # 认证信息
    password = Column(String(500), nullable=True)  # 加密存储的密码
    cookie = Column(Text, nullable=True)  # 改为可空，支持自动登录获取
    token = Column(String(500))
    user_agent = Column(String(1000))

    # 代理配置
    proxy_id = Column(BigInteger, nullable=True, index=True)

    # 登录和token管理
    login_status = Column(String(20), default='not_logged_in')  # not_logged_in, pending, logging_in, logged_in, login_failed, expired, error
    token_expires_at = Column(DateTime, nullable=True)  # token过期时间
    auto_refresh_enabled = Column(Boolean, default=True)  # 是否启用自动刷新
    last_login_at = Column(DateTime, nullable=True)  # 最后登录时间

    # 状态和统计
    status = Column(String(20), default='active')  # active, inactive, suspended, error
    is_enabled = Column(Boolean, default=True)  # 是否启用（爬虫池开关）
    priority = Column(Integer, default=1)
    max_requests_per_hour = Column(Integer, default=100)
    current_requests_count = Column(Integer, default=0)
    success_rate = Column(Numeric(5, 2), default=100.00)
    total_requests = Column(Integer, default=0)
    success_requests = Column(Integer, default=0)
    error_count = Column(Integer, default=0)
    last_error_message = Column(Text)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_used_at = Column(DateTime)
    extracted_from = Column(String(500))
    notes = Column(Text)

    # 关系定义
    platform = relationship("Platform", foreign_keys=[platform_id], primaryjoin="CrawlerAccount.platform_id == Platform.id")
    # proxy 通过 proxy_id 手动查询（如果需要的话）

    # 索引
    __table_args__ = (
        Index('idx_platform_status', 'platform_id', 'status'),
        Index('idx_last_used', 'last_used_at'),
        Index('idx_platform_username', 'platform_id', 'username', unique=True),
        Index('idx_login_status', 'login_status'),
        Index('idx_token_expires', 'token_expires_at'),
    )

    @property
    def is_healthy(self):
        """判断账号是否健康"""
        if self.status not in ['active']:
            return False
        if self.error_count >= 3:
            return False
        if self.success_rate < 80:
            return False
        return True

    @property
    def can_use(self):
        """判断账号是否可以使用（爬虫池调度用）"""
        # 必须启用
        if not self.is_enabled:
            return False
        # 必须健康
        if not self.is_healthy:
            return False
        # 必须已登录或token有效
        if self.login_status not in ['logged_in']:
            return False
        # 不能超过请求限制
        if self.current_requests_count >= self.max_requests_per_hour:
            return False
        return True

    @property
    def pool_status(self):
        """获取爬虫池状态（用于前端显示）"""
        if not self.is_enabled:
            return 'disabled'
        elif not self.is_healthy:
            return 'unhealthy'
        elif self.login_status != 'logged_in':
            return 'not_ready'
        elif self.current_requests_count >= self.max_requests_per_hour:
            return 'rate_limited'
        else:
            return 'available'


# 爬虫使用日志表
class CrawlerUsageLog(Base):
    """爬虫使用日志表"""
    __tablename__ = "crawler_usage_log"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    account_id = Column(BigInteger, nullable=False, index=True)
    platform = Column(String(50))
    search_query = Column(String(500))
    status = Column(String(20))
    response_time_ms = Column(Integer)
    error_message = Column(Text)
    created_at = Column(DateTime, default=func.now())

    # 关系（手动查询，不使用外键）
    # account 通过 account_id 手动查询

    # 索引
    __table_args__ = (
        Index('idx_account_id', 'account_id'),
        Index('idx_created_at', 'created_at'),
    )


# 爬虫会话管理表
class CrawlerSession(Base):
    """爬虫会话管理表"""
    __tablename__ = "crawler_sessions"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    session_id = Column(String(64), nullable=False, unique=True, index=True)
    client_ip = Column(String(45), nullable=False, index=True)
    user_agent = Column(Text)
    platform_code = Column(String(50), nullable=False)
    account_id = Column(BigInteger, index=True)
    proxy_id = Column(BigInteger)

    # 会话状态
    status = Column(String(20), default='active')
    request_count = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    error_count = Column(Integer, default=0)

    # 反爬检测
    risk_score = Column(Numeric(5, 2), default=0.00)
    last_request_at = Column(DateTime)
    blocked_until = Column(DateTime)

    # 时间戳
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    expires_at = Column(DateTime)

    # 索引
    __table_args__ = (
        Index('idx_client_ip', 'client_ip'),
        Index('idx_platform_account', 'platform_code', 'account_id'),
        Index('idx_status_expires', 'status', 'expires_at'),
        Index('idx_last_request', 'last_request_at'),
    )


# 爬虫池调度表
class CrawlerPoolSchedule(Base):
    """爬虫池调度表"""
    __tablename__ = "crawler_pool_schedule"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    platform_code = Column(String(50), nullable=False)
    account_id = Column(BigInteger, nullable=False, index=True)

    # 调度状态
    status = Column(String(20), default='available')
    priority_score = Column(Numeric(8, 2), default=100.00)
    load_factor = Column(Numeric(5, 2), default=0.00)

    # 使用统计
    current_sessions = Column(Integer, default=0)
    max_concurrent_sessions = Column(Integer, default=5)
    requests_per_minute = Column(Integer, default=0)
    max_requests_per_minute = Column(Integer, default=60)

    # 健康度指标
    health_score = Column(Numeric(5, 2), default=100.00)
    success_rate_1h = Column(Numeric(5, 2), default=100.00)
    success_rate_24h = Column(Numeric(5, 2), default=100.00)
    avg_response_time_ms = Column(Integer, default=0)

    # 冷却和限制
    last_used_at = Column(DateTime)
    cooling_until = Column(DateTime)
    blocked_until = Column(DateTime)
    next_available_at = Column(DateTime)

    # 时间戳
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 索引
    __table_args__ = (
        Index('uk_platform_account', 'platform_code', 'account_id', unique=True),
        Index('idx_status_priority', 'status', 'priority_score'),
        Index('idx_platform_status', 'platform_code', 'status'),
        Index('idx_next_available', 'next_available_at'),
        Index('idx_health_score', 'health_score'),
    )


# 请求频率控制表
class CrawlerRateLimit(Base):
    """请求频率控制表"""
    __tablename__ = "crawler_rate_limits"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    identifier = Column(String(100), nullable=False)
    identifier_type = Column(String(20), nullable=False)
    platform_code = Column(String(50))

    # 频率限制
    window_size_seconds = Column(Integer, nullable=False, default=60)
    max_requests = Column(Integer, nullable=False, default=60)
    current_requests = Column(Integer, default=0)

    # 时间窗口
    window_start_at = Column(DateTime, nullable=False)
    window_end_at = Column(DateTime, nullable=False)

    # 违规记录
    violation_count = Column(Integer, default=0)
    last_violation_at = Column(DateTime)
    blocked_until = Column(DateTime)

    # 时间戳
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 索引
    __table_args__ = (
        Index('uk_identifier_type_platform', 'identifier', 'identifier_type', 'platform_code', unique=True),
        Index('idx_window_end', 'window_end_at'),
        Index('idx_blocked_until', 'blocked_until'),
        Index('idx_platform_type', 'platform_code', 'identifier_type'),
    )


# 爬虫性能监控表
class CrawlerPerformanceMetrics(Base):
    """爬虫性能监控表"""
    __tablename__ = "crawler_performance_metrics"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    account_id = Column(BigInteger, nullable=False, index=True)
    platform_code = Column(String(50), nullable=False)

    # 性能指标
    metric_type = Column(String(20), nullable=False)
    metric_value = Column(Numeric(10, 4), nullable=False)
    metric_unit = Column(String(20))

    # 时间维度
    time_window = Column(String(10), nullable=False)
    window_start_at = Column(DateTime, nullable=False)
    window_end_at = Column(DateTime, nullable=False)

    # 采样信息
    sample_count = Column(Integer, default=1)
    min_value = Column(Numeric(10, 4))
    max_value = Column(Numeric(10, 4))
    avg_value = Column(Numeric(10, 4))

    # 时间戳
    created_at = Column(DateTime, default=func.now())

    # 索引
    __table_args__ = (
        Index('idx_account_metric_window', 'account_id', 'metric_type', 'time_window'),
        Index('idx_platform_metric_window', 'platform_code', 'metric_type', 'time_window'),
        Index('idx_window_start', 'window_start_at'),
        Index('idx_window_end', 'window_end_at'),
    )


# 反爬检测记录表
class AntiCrawlDetection(Base):
    """反爬检测记录表"""
    __tablename__ = "anti_crawl_detections"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    account_id = Column(BigInteger, index=True)
    session_id = Column(String(64), index=True)
    client_ip = Column(String(45), nullable=False, index=True)
    platform_code = Column(String(50), nullable=False)

    # 检测信息
    detection_type = Column(String(30), nullable=False)
    severity = Column(String(20), default='medium')
    confidence = Column(Numeric(5, 2), default=50.00)

    # 详细信息
    detection_details = Column(JSON)
    response_code = Column(Integer)
    response_headers = Column(JSON)
    error_message = Column(Text)

    # 处理状态
    status = Column(String(20), default='detected')
    auto_resolved = Column(Boolean, default=False)
    resolved_at = Column(DateTime)

    # 时间戳
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 索引
    __table_args__ = (
        Index('idx_account_platform', 'account_id', 'platform_code'),
        Index('idx_client_ip_platform', 'client_ip', 'platform_code'),
        Index('idx_detection_type_severity', 'detection_type', 'severity'),
        Index('idx_status_created', 'status', 'created_at'),
    )
