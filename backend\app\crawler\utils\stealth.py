"""
反检测工具

提供浏览器指纹伪装和反爬虫检测功能
"""

import random
from playwright.async_api import BrowserContext


async def apply_stealth(context: BrowserContext):
    """应用反检测措施"""
    
    # 1. 隐藏webdriver属性
    await context.add_init_script("""
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
    """)
    
    # 2. 伪装Chrome对象
    await context.add_init_script("""
        window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
        };
    """)
    
    # 3. 伪装插件信息
    await context.add_init_script("""
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
    """)
    
    # 4. 伪装语言
    await context.add_init_script("""
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
    """)
    
    # 5. 伪装权限API
    await context.add_init_script("""
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
    """)
    
    # 6. 移除自动化痕迹
    await context.add_init_script("""
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
    """)
    
    # 7. 伪装屏幕信息
    screen_width = random.randint(1366, 1920)
    screen_height = random.randint(768, 1080)
    
    await context.add_init_script(f"""
        Object.defineProperty(screen, 'width', {{
            get: () => {screen_width},
        }});
        Object.defineProperty(screen, 'height', {{
            get: () => {screen_height},
        }});
        Object.defineProperty(screen, 'availWidth', {{
            get: () => {screen_width},
        }});
        Object.defineProperty(screen, 'availHeight', {{
            get: () => {screen_height - 40},
        }});
    """)
    
    # 8. 伪装时区
    await context.add_init_script("""
        Object.defineProperty(Intl.DateTimeFormat.prototype, 'resolvedOptions', {
            value: function() {
                return {
                    locale: 'zh-CN',
                    calendar: 'gregory',
                    numberingSystem: 'latn',
                    timeZone: 'Asia/Shanghai'
                };
            }
        });
    """)
    
    # 9. 伪装Canvas指纹
    await context.add_init_script("""
        const getImageData = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function(type) {
            if (type === 'image/png') {
                const canvas = this;
                const ctx = canvas.getContext('2d');
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                for (let i = 0; i < imageData.data.length; i += 4) {
                    imageData.data[i] += Math.floor(Math.random() * 10) - 5;
                    imageData.data[i + 1] += Math.floor(Math.random() * 10) - 5;
                    imageData.data[i + 2] += Math.floor(Math.random() * 10) - 5;
                }
                ctx.putImageData(imageData, 0, 0);
            }
            return getImageData.apply(this, arguments);
        };
    """)
    
    # 10. 伪装WebGL指纹
    await context.add_init_script("""
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            if (parameter === 37445) {
                return 'Intel Inc.';
            }
            if (parameter === 37446) {
                return 'Intel(R) Iris(TM) Graphics 6100';
            }
            return getParameter(parameter);
        };
    """)


async def random_delay(min_seconds: float = 1.0, max_seconds: float = 3.0):
    """随机延迟"""
    import asyncio
    delay = random.uniform(min_seconds, max_seconds)
    await asyncio.sleep(delay)


def get_random_viewport():
    """获取随机视口大小"""
    viewports = [
        {"width": 1920, "height": 1080},
        {"width": 1366, "height": 768},
        {"width": 1440, "height": 900},
        {"width": 1536, "height": 864},
        {"width": 1280, "height": 720},
    ]
    return random.choice(viewports)


def get_random_user_agent():
    """获取随机User-Agent"""
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    ]
    return random.choice(user_agents)
