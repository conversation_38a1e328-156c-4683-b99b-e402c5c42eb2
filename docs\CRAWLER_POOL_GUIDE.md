# 🏊‍♂️ 爬虫池系统完整指南

## 📋 系统概述

爬虫池是AqentCrawler项目的核心组件，负责智能管理和调度多个爬虫账号，实现负载均衡、会话管理、频率控制和反爬检测等功能。通过爬虫池，系统能够自动选择最优的爬虫账号执行搜索任务，提高成功率和稳定性。

## 🎯 核心功能

### 1. 智能爬虫选择
- **多维度评分**：基于成功率、健康度、负载、使用时间等计算综合评分
- **负载均衡**：优先选择负载较低的爬虫账号
- **实时状态检查**：考虑Token过期、冷却期、阻塞状态等因素
- **随机性**：在高分账号中加权随机选择，避免单点过载

### 2. 会话管理
- **会话创建**：基于客户端IP、User-Agent、平台自动创建会话
- **会话绑定**：将会话与最优爬虫账号绑定
- **会话跟踪**：记录请求数、成功率、错误率等指标
- **会话过期**：自动清理过期会话，释放资源

### 3. 频率控制
- **多级限制**：IP级别、账号级别、会话级别、平台级别
- **时间窗口**：滑动窗口算法控制请求频率
- **违规处理**：自动阻塞违规IP，递增阻塞时间
- **白名单机制**：支持可信IP白名单

### 4. 反爬检测
- **多种检测**：验证码、频率限制、IP封禁、账号封禁等
- **风险评分**：动态计算会话和账号的风险评分
- **自动处理**：检测到反爬时自动切换爬虫或暂停服务
- **学习机制**：基于历史数据优化检测策略

## 🗄️ 数据库表结构

### 1. 爬虫账号表 (crawler_accounts)
**用途**：存储所有爬虫账号的基本信息和状态
```sql
主要字段：
- id: 账号唯一标识
- platform_id: 所属平台ID
- username: 账号用户名
- login_status: 登录状态 (logged_in/expired/error)
- is_enabled: 是否启用（爬虫池开关）
- success_rate: 成功率
- total_requests: 总请求数
- success_requests: 成功请求数
- error_count: 错误次数
- last_used_at: 最后使用时间
```

### 2. 爬虫会话表 (crawler_sessions)
**用途**：管理客户端会话，跟踪每个会话的使用情况
```sql
主要字段：
- session_id: 会话唯一标识
- client_ip: 客户端IP地址
- platform_code: 平台代码
- account_id: 绑定的爬虫账号ID
- status: 会话状态 (active/expired/blocked/error)
- request_count: 请求总数
- success_count: 成功请求数
- error_count: 错误请求数
- risk_score: 风险评分 (0-100)
- expires_at: 会话过期时间
```

### 3. 爬虫池调度表 (crawler_pool_schedule)
**用途**：管理爬虫账号的调度状态和性能指标
```sql
主要字段：
- account_id: 爬虫账号ID
- platform_code: 平台代码
- status: 调度状态 (available/busy/cooling/blocked/maintenance)
- priority_score: 优先级评分
- load_factor: 负载因子 (0-1)
- current_sessions: 当前会话数
- max_concurrent_sessions: 最大并发会话数
- requests_per_minute: 每分钟请求数
- max_requests_per_minute: 每分钟最大请求数
- health_score: 健康度评分 (0-100)
- cooling_until: 冷却到期时间
- blocked_until: 阻塞到期时间
```

### 4. 频率限制表 (crawler_rate_limits)
**用途**：控制不同维度的请求频率
```sql
主要字段：
- identifier: 标识符(IP/账号/会话)
- identifier_type: 标识符类型 (ip/account/session/platform)
- platform_code: 平台代码
- window_size_seconds: 时间窗口(秒)
- max_requests: 最大请求数
- current_requests: 当前请求数
- window_start_at: 窗口开始时间
- violation_count: 违规次数
- blocked_until: 阻塞到期时间
```

### 5. 性能监控表 (crawler_performance_metrics)
**用途**：记录爬虫账号的性能指标
```sql
主要字段：
- account_id: 爬虫账号ID
- platform_code: 平台代码
- metric_type: 指标类型 (response_time/success_rate/error_rate)
- metric_value: 指标值
- time_window: 时间窗口 (1h/24h/7d)
- recorded_at: 记录时间
```

### 6. 反爬检测表 (anti_crawl_detections)
**用途**：记录反爬检测事件和处理结果
```sql
主要字段：
- account_id: 爬虫账号ID
- session_id: 会话ID
- client_ip: 客户端IP
- platform_code: 平台代码
- detection_type: 检测类型 (captcha/rate_limit/ip_ban/account_ban)
- severity: 严重程度 (low/medium/high/critical)
- confidence: 置信度 (0-100)
- status: 处理状态 (detected/handled/ignored)
- response_action: 响应动作
```

## 🔄 工作流程

### 1. 搜索请求处理流程
```
1. 接收搜索请求 (client_ip, platform, query)
2. 创建或获取会话 (CrawlerPoolService.get_or_create_session)
3. 检查频率限制 (_check_rate_limit)
4. 获取可用爬虫账号 (_get_available_crawlers)
5. 选择最佳爬虫 (_select_optimal_crawler)
6. 绑定会话和爬虫 (_bind_session_crawler)
7. 更新调度状态 (_update_crawler_schedule)
8. 执行爬虫搜索
9. 记录请求结果 (record_request_result)
10. 更新性能指标
```

### 2. 爬虫选择算法
```python
def _select_optimal_crawler(self, available_crawlers, session_id):
    """
    评分因子：
    - 成功率权重: 40%
    - 健康度权重: 30%
    - 负载权重: 20%
    - 使用时间权重: 10%
    
    计算公式：
    score = (success_rate * 0.4) + (health_score * 0.3) + 
            ((1 - load_factor) * 0.2) + (time_factor * 0.1)
    """
```

### 3. 数据更新逻辑

#### 账号统计更新
- **请求成功时**：
  - `total_requests += 1`
  - `success_requests += 1`
  - `error_count = max(0, error_count - 1)` (减少错误计数)
  - `success_rate = (success_requests / total_requests) * 100`
  - `last_used_at = now()`

- **请求失败时**：
  - `total_requests += 1`
  - `error_count += 1`
  - `success_rate = (success_requests / total_requests) * 100`

#### 调度状态更新
- **选择爬虫时**：
  - `status = 'busy'`
  - `current_sessions += 1`
  - `requests_per_minute += 1`
  - `load_factor = current_sessions / max_concurrent_sessions`
  - `last_used_at = now()`

- **请求完成时**：
  - `current_sessions -= 1`
  - 如果 `current_sessions == 0`，则 `status = 'available'`

#### 健康度计算
```python
def calculate_health_score(account):
    """
    健康度评分 = 成功率 * 0.6 + 响应时间因子 * 0.3 + 稳定性因子 * 0.1
    """
    success_factor = account.success_rate
    response_factor = min(100, 5000 / max(account.avg_response_time, 100))
    stability_factor = max(0, 100 - account.error_count * 5)
    
    return success_factor * 0.6 + response_factor * 0.3 + stability_factor * 0.1
```

## 🚀 使用场景

### 1. 主搜索接口 (/api/v1/search)
**位置**：`backend/app/main.py:419-580`
```python
# 创建会话
session_id = pool_service.get_or_create_session(
    client_ip=client_ip,
    user_agent=request.headers.get("user-agent"),
    platform_code=platform
)

# 选择爬虫
account = pool_service.select_best_crawler(
    platform_code=platform,
    session_id=session_id,
    client_ip=client_ip
)

# 记录结果
pool_service.record_request_result(
    session_id=session_id,
    account_id=account.id,
    success=True,
    response_time_ms=response_time
)
```

### 2. 搜索管理器 (SearchManager)
**位置**：`backend/app/crawler/search_manager.py`
- 集成爬虫池管理的搜索管理器
- 支持多平台并发搜索
- 自动会话管理和结果记录

### 3. 详情管理器 (DetailManager)
**位置**：`backend/app/crawler/detail_manager.py`
- 商品详情获取时的爬虫选择
- 会话复用和状态跟踪

## 🎛️ 管理界面

### 1. 爬虫池管理页面
**位置**：`frontend/src/views/CrawlerPool.vue`

**功能模块**：
- **状态概览**：显示总爬虫数、可用数、忙碌数等
- **调度状态**：查看每个爬虫的调度状态、负载、健康度
- **活跃会话**：监控当前活跃的会话信息
- **频率限制**：查看和管理频率限制规则
- **反爬检测**：查看反爬检测记录和处理状态

**操作功能**：
- 重置爬虫状态
- 更新爬虫调度状态
- 移除频率限制
- 数据清理

### 2. API接口
**路由前缀**：`/api/v1/crawler-pool`

**主要接口**：
- `GET /status` - 获取爬虫池状态
- `GET /schedules` - 获取调度信息
- `GET /sessions` - 获取会话信息
- `GET /rate-limits` - 获取频率限制
- `GET /anti-crawl-detections` - 获取反爬检测记录
- `POST /schedules/{account_id}/update-status` - 更新爬虫状态
- `POST /reset-crawler/{account_id}` - 重置爬虫状态
- `POST /cleanup` - 数据清理

## ⚙️ 配置参数

### 爬虫池服务配置
```python
class CrawlerPoolService:
    def __init__(self):
        self.session_timeout = 3600  # 会话超时时间(秒)
        self.max_sessions_per_ip = 3  # 每个IP最大会话数
        self.max_requests_per_minute = 60  # 每分钟最大请求数
        self.cooling_period = 300  # 冷却期(秒)
        self.health_threshold = 50.0  # 健康度阈值
```

### 账号属性配置
```python
# 爬虫账号模型中的关键属性
class CrawlerAccount:
    is_enabled: bool  # 爬虫池开关
    max_requests_per_hour: int  # 每小时最大请求数
    priority: int  # 优先级
    success_rate: float  # 成功率
```

## 🔧 最佳实践

### 1. 爬虫配置建议
- 合理设置每个爬虫的并发限制 (`max_concurrent_sessions`)
- 定期检查和更新Token状态
- 监控爬虫健康度，及时处理异常账号
- 设置合适的优先级和请求频率限制

### 2. 频率控制策略
- 根据平台特性调整请求频率
- 设置合理的违规阻塞时间
- 建立可信IP白名单
- 监控频率限制触发情况

### 3. 反爬应对策略
- 多样化User-Agent和请求头
- 使用代理IP池分散请求
- 实现智能重试和降级策略
- 及时响应反爬检测事件

### 4. 监控告警
- 设置关键指标告警阈值
- 建立自动化故障恢复机制
- 定期分析和优化性能
- 监控爬虫池整体健康状况

## 📊 性能优化

### 1. 数据库优化
- 为高频查询字段添加索引
- 定期清理过期数据
- 使用连接池优化数据库连接
- 监控慢查询并优化

### 2. 缓存策略
- 缓存爬虫状态信息
- 缓存频率限制计数
- 使用Redis提高查询性能

### 3. 并发控制
- 合理设置并发会话数
- 避免单个爬虫过载
- 实现优雅的降级策略

这个爬虫池系统通过智能调度、负载均衡、状态监控等机制，确保了爬虫资源的高效利用和系统的稳定运行。
