#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试组件结构
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

import asyncio
from app.crawler.platforms.alibaba_1688 import Alibaba1688Crawler

async def test_component_structure():
    """测试组件结构"""
    
    print("🧪 测试1688组件结构")
    print("📦 测试商品: https://detail.1688.com/offer/591940079968.html")
    print("="*80)
    
    try:
        # 创建爬虫实例
        crawler = Alibaba1688Crawler()
        
        # 获取商品详情（这会触发组件查找）
        print("📡 开始获取商品详情...")
        result = await crawler.get_product_detail("https://detail.1688.com/offer/591940079968.html")
        
        if result.get('success'):
            print("✅ 商品详情获取成功!")
        else:
            print(f"❌ 商品详情获取失败: {result.get('error', '')}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_component_structure())
