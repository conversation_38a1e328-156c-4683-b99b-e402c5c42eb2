#!/usr/bin/env python3
"""
AqentCrawler 打包脚本
用于创建生产环境部署包
"""

import os
import sys
import shutil
import zipfile
import json
from datetime import datetime
from pathlib import Path

class PackageBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.build_dir = self.project_root / "build"
        self.package_name = f"aqentcrawler-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        self.package_dir = self.build_dir / self.package_name
        
    def clean_build_dir(self):
        """清理构建目录"""
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        self.build_dir.mkdir(parents=True)
        print(f"✅ 清理构建目录: {self.build_dir}")
    
    def build_frontend(self):
        """构建前端"""
        print("🔨 构建前端...")
        frontend_dir = self.project_root / "frontend"
        
        # 安装依赖
        os.system(f"cd {frontend_dir} && npm install")
        
        # 构建生产版本
        result = os.system(f"cd {frontend_dir} && npm run build")
        if result != 0:
            raise Exception("前端构建失败")
        
        print("✅ 前端构建完成")
    
    def copy_backend(self):
        """复制后端文件"""
        print("📁 复制后端文件...")
        backend_src = self.project_root / "backend"
        backend_dst = self.package_dir / "backend"
        
        # 复制后端代码
        shutil.copytree(backend_src, backend_dst, ignore=shutil.ignore_patterns(
            '__pycache__', '*.pyc', '*.pyo', '.pytest_cache', 'logs', 'temp'
        ))
        
        print("✅ 后端文件复制完成")
    
    def copy_frontend(self):
        """复制前端构建文件"""
        print("📁 复制前端构建文件...")
        frontend_src = self.project_root / "frontend" / "dist"
        frontend_dst = self.package_dir / "frontend"
        
        if not frontend_src.exists():
            raise Exception("前端构建文件不存在，请先构建前端")
        
        shutil.copytree(frontend_src, frontend_dst)
        print("✅ 前端文件复制完成")
    
    def copy_scripts(self):
        """复制脚本文件"""
        print("📁 复制脚本文件...")
        scripts_src = self.project_root / "scripts"
        scripts_dst = self.package_dir / "scripts"
        
        shutil.copytree(scripts_src, scripts_dst, ignore=shutil.ignore_patterns(
            '__pycache__', '*.pyc', 'package.py'
        ))
        
        print("✅ 脚本文件复制完成")
    
    def copy_docs(self):
        """复制文档文件"""
        print("📁 复制文档文件...")
        docs_to_copy = [
            "README.md",
            "DEPLOYMENT_GUIDE.md", 
            "Linux部署指南.md",
            "项目总结文档.md",
            "上游API接口文档.md"
        ]
        
        for doc in docs_to_copy:
            src = self.project_root / doc
            if src.exists():
                shutil.copy2(src, self.package_dir / doc)
        
        print("✅ 文档文件复制完成")
    
    def copy_chrome_extension(self):
        """复制Chrome扩展"""
        print("📁 复制Chrome扩展...")
        ext_src = self.project_root / "chrome-extension"
        ext_dst = self.package_dir / "chrome-extension"
        
        if ext_src.exists():
            shutil.copytree(ext_src, ext_dst, ignore=shutil.ignore_patterns(
                'test-*.html'
            ))
        
        print("✅ Chrome扩展复制完成")
    
    def create_config_templates(self):
        """创建配置模板"""
        print("⚙️ 创建配置模板...")
        
        # 创建环境配置模板
        env_template = """# AqentCrawler 生产环境配置

# 环境标识
ENVIRONMENT=production

# 数据库配置
DATABASE_URL=mysql+pymysql://aqentcrawler:password@localhost:3306/aqentcrawler
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=aqentcrawler
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=aqentcrawler

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 安全配置
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# 管理员账号
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=4
DEBUG=false

# Chrome配置
CHROME_PATH=/usr/bin/google-chrome
CHROME_USER_DATA_DIR=/home/<USER>/chrome_data

# 代理配置（可选）
PROXY_SERVER=
PROXY_USERNAME=
PROXY_PASSWORD=

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/home/<USER>/logs/app.log

# 缓存配置
CACHE_TTL=3600
SEARCH_CACHE_TTL=1800
DETAIL_CACHE_TTL=7200
"""
        
        with open(self.package_dir / ".env.production", "w", encoding="utf-8") as f:
            f.write(env_template)
        
        print("✅ 配置模板创建完成")
    
    def create_deployment_scripts(self):
        """创建部署脚本"""
        print("🚀 创建部署脚本...")
        
        # Linux部署脚本
        deploy_script = """#!/bin/bash
# AqentCrawler Linux 部署脚本

set -e

echo "🚀 开始部署 AqentCrawler..."

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo "❌ 请不要使用root用户运行此脚本"
    exit 1
fi

# 设置变量
APP_USER="aqentcrawler"
APP_DIR="/home/<USER>/aqentcrawler"
SERVICE_NAME="aqentcrawler"

# 创建应用目录
mkdir -p $APP_DIR
cd $APP_DIR

# 复制文件
echo "📁 复制应用文件..."
cp -r backend $APP_DIR/
cp -r frontend $APP_DIR/
cp -r scripts $APP_DIR/
cp -r chrome-extension $APP_DIR/
cp *.md $APP_DIR/

# 设置环境配置
if [ ! -f "$APP_DIR/backend/.env" ]; then
    cp .env.production $APP_DIR/backend/.env
    echo "⚠️  请编辑 $APP_DIR/backend/.env 配置文件"
fi

# 安装Python依赖
echo "📦 安装Python依赖..."
cd $APP_DIR/backend
pip3 install --user -r requirements.txt

# 创建日志目录
mkdir -p /home/<USER>/logs
mkdir -p /home/<USER>/chrome_data

# 设置权限
chmod +x $APP_DIR/scripts/*.py
chmod +x $APP_DIR/scripts/*.sh

echo "✅ 部署完成！"
echo "📝 下一步："
echo "1. 编辑配置文件: $APP_DIR/backend/.env"
echo "2. 初始化数据库: python3 $APP_DIR/scripts/init_db.py"
echo "3. 启动服务: python3 $APP_DIR/scripts/start_prod.py"
"""
        
        with open(self.package_dir / "deploy.sh", "w", encoding="utf-8") as f:
            f.write(deploy_script)
        
        os.chmod(self.package_dir / "deploy.sh", 0o755)
        
        print("✅ 部署脚本创建完成")
    
    def create_package_info(self):
        """创建包信息文件"""
        package_info = {
            "name": "AqentCrawler",
            "version": "1.0.0",
            "build_time": datetime.now().isoformat(),
            "platform": "linux",
            "python_version": "3.8+",
            "node_version": "16+",
            "description": "智能代购爬虫系统生产环境部署包"
        }
        
        with open(self.package_dir / "package.json", "w", encoding="utf-8") as f:
            json.dump(package_info, f, indent=2, ensure_ascii=False)
        
        print("✅ 包信息文件创建完成")
    
    def create_zip_package(self):
        """创建ZIP压缩包"""
        print("📦 创建ZIP压缩包...")
        zip_path = self.build_dir / f"{self.package_name}.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(self.package_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, self.package_dir)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ 压缩包创建完成: {zip_path}")
        return zip_path
    
    def build(self):
        """执行完整构建流程"""
        try:
            print("🚀 开始构建 AqentCrawler 部署包...")
            
            self.clean_build_dir()
            self.build_frontend()
            
            # 创建包目录
            self.package_dir.mkdir(parents=True)
            
            self.copy_backend()
            self.copy_frontend()
            self.copy_scripts()
            self.copy_docs()
            self.copy_chrome_extension()
            self.create_config_templates()
            self.create_deployment_scripts()
            self.create_package_info()
            
            zip_path = self.create_zip_package()
            
            print(f"\n🎉 构建完成！")
            print(f"📦 部署包: {zip_path}")
            print(f"📁 解压目录: {self.package_dir}")
            print(f"\n📝 部署步骤:")
            print(f"1. 将 {zip_path.name} 上传到Linux服务器")
            print(f"2. 解压: unzip {zip_path.name}")
            print(f"3. 运行部署脚本: ./deploy.sh")
            
        except Exception as e:
            print(f"❌ 构建失败: {e}")
            sys.exit(1)

if __name__ == "__main__":
    builder = PackageBuilder()
    builder.build()
