import axios from 'axios'

const api = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 600000  // 10分钟超时，匹配后端登录超时时间
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => Promise.reject(error)
)

// 响应拦截器
api.interceptors.response.use(
  response => response.data,
  error => {
    console.error('API Error:', error)

    // 处理认证失败
    if (error.response?.status === 401) {
      // 清除本地存储的认证信息
      localStorage.removeItem('token')
      localStorage.removeItem('username')

      // 跳转到登录页面
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    }

    return Promise.reject(error)
  }
)

// 验证相关API
export const verificationAPI = {
  // 获取待处理验证任务
  getPendingTasks: () => api.get('/verification/pending'),

  // 完成验证任务
  completeTask: (data) => api.post('/verification/complete', data),

  // 获取验证类型
  getTypes: () => api.get('/verification/types'),

  // 获取验证统计
  getStats: () => api.get('/verification/stats'),

  // 测试验证系统
  testSystem: () => api.post('/verification/test')
}

// 登录测试相关API
export const loginAPI = {
  // 测试账号登录
  testLogin: (data) => api.post('/login/test', data),

  // 模拟登录流程
  simulateLogin: (data) => api.post('/login/simulate', data),

  // 获取登录方式
  getMethods: () => api.get('/login/methods'),

  // 获取登录状态
  getStatus: (platform, username) => api.get(`/login/status/${platform}/${username}`)
}

// 主要API对象
const apiMethods = {
  // 平台管理
  platforms: {
    getList: () => api.get('/platforms'),
    getAll: () => api.get('/platforms'),
    create: (data) => api.post('/platforms', data),
    update: (id, data) => api.put(`/platforms/${id}`, data),
    delete: (id) => api.delete(`/platforms/${id}`)
  },

  // 账号管理
  accounts: {
    getAll: () => api.get('/accounts'),
    create: (data) => api.post('/accounts', data),
    update: (id, data) => api.put(`/accounts/${id}`, data),
    delete: (id) => api.delete(`/accounts/${id}`)
  },

  // 代理管理
  proxies: {
    getAll: () => api.get('/proxies'),
    create: (data) => api.post('/proxies', data),
    update: (id, data) => api.put(`/proxies/${id}`, data),
    delete: (id) => api.delete(`/proxies/${id}`),
    test: (id) => api.post(`/proxies/${id}/test`),
    batchTest: () => api.post('/proxies/batch-test'),
    toggle: (id) => api.post(`/proxies/${id}/toggle`),
    getTestLogs: (id, params) => api.get(`/proxies/${id}/test-logs`, { params })
  },

  // 日志查询
  logs: {
    getAll: (params) => api.get('/logs', { params }),
    getStats: () => api.get('/logs/stats')
  },

  // 商品搜索
  search: {
    products: (data) => api.post('/search', data)
  },

  // 商品详情
  product: {
    detail: (data) => api.post('/product/detail', data),
    detail1688: (data) => api.post('/product/detail/1688', data),
    detailByPlatform: (data) => api.post('/product/detail/platform', data)
  },

  // 认证相关
  auth: {
    login: (data) => api.post('/auth/login', data),
    verify: () => api.post('/auth/verify')
  },

  // 爬虫配置管理
  crawlerConfig: {
    // 获取所有爬虫账号
    getAccounts: (params) => api.get('/crawler/accounts', { params }),
    // 获取单个爬虫账号详情
    getAccount: (id) => api.get(`/crawler/accounts/${id}`),
    // 创建爬虫账号
    createAccount: (data) => api.post('/crawler/accounts', data),
    // 更新爬虫账号
    updateAccount: (id, data) => api.put(`/crawler/accounts/${id}`, data),
    // 删除爬虫账号
    deleteAccount: (id) => api.delete(`/crawler/accounts/${id}`),
    // 获取账号统计
    getStats: (platform) => api.get('/crawler/stats', { params: { platform } }),
    // 更新配置（Chrome扩展使用）
    updateConfig: (data) => api.post('/config/update', data),
    // 账号自动登录
    loginAccount: (data) => api.post('/crawler/accounts/login', data),
    // 刷新账号Token
    refreshToken: (data) => api.post('/crawler/accounts/refresh-token', data),
    // 获取账号登录状态
    getLoginStatus: (id) => api.get(`/crawler/accounts/${id}/login-status`),
    // 获取所有账号登录状态概览
    getAllLoginStatus: (params) => api.get('/crawler/accounts/login-status', { params }),
    // 批量登录账号
    batchLogin: (data) => api.post('/crawler/accounts/batch-login', data),
    // 批量刷新Token
    batchRefreshToken: (data) => api.post('/crawler/accounts/batch-refresh-token', data),
    // 切换账号启用状态
    toggleAccount: (id) => api.post(`/crawler/accounts/${id}/toggle`),

    // 监控相关接口
    getMonitorStatus: () => api.get('/crawler/monitor/status'),
    startMonitor: () => api.post('/crawler/monitor/start'),
    stopMonitor: () => api.post('/crawler/monitor/stop'),
    updateMonitorSettings: (data) => api.put('/crawler/monitor/settings', data),
    getMonitorAlerts: () => api.get('/crawler/monitor/alerts'),
    forceHealthCheck: () => api.post('/crawler/monitor/force-health-check')
  },

  // 爬虫池管理
  crawlerPool: {
    // 获取爬虫池状态
    getStatus: () => api.get('/crawler-pool/status'),
    // 获取调度信息
    getSchedules: (params) => api.get('/crawler-pool/schedules', { params }),
    // 获取会话信息
    getSessions: (params) => api.get('/crawler-pool/sessions', { params }),
    // 获取频率限制
    getRateLimits: (params) => api.get('/crawler-pool/rate-limits', { params }),
    // 获取反爬检测记录
    getDetections: (params) => api.get('/crawler-pool/anti-crawl-detections', { params }),
    // 更新爬虫状态
    updateCrawlerStatus: (accountId, status) => api.post(`/crawler-pool/schedules/${accountId}/update-status?status=${status}`),
    // 重置爬虫状态
    resetCrawler: (accountId) => api.post(`/crawler-pool/reset-crawler/${accountId}`),
    // 移除频率限制
    removeRateLimit: (limitId) => api.delete(`/crawler-pool/rate-limits/${limitId}`),
    // 数据清理
    cleanup: (params) => api.post('/crawler-pool/cleanup', null, { params })
  },

  // 系统统计
  stats: {
    getOverview: () => api.get('/stats')
  },

  // 验证管理
  verification: verificationAPI,

  // 登录测试
  login: loginAPI
}

export default apiMethods
