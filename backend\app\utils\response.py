"""
统一响应格式工具类
"""
from typing import Any, Optional
from fastapi.responses import JSONResponse


class ApiResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> dict:
        """成功响应"""
        return {
            "code": 200,
            "message": message,
            "data": data
        }
    
    @staticmethod
    def error(message: str = "操作失败", code: int = 400, data: Any = None) -> dict:
        """错误响应"""
        return {
            "code": code,
            "message": message,
            "data": data
        }
    
    @staticmethod
    def server_error(message: str = "服务器内部错误") -> dict:
        """服务器错误响应"""
        return {
            "code": 500,
            "message": message,
            "data": None
        }
    
    @staticmethod
    def not_found(message: str = "资源不存在") -> dict:
        """资源不存在响应"""
        return {
            "code": 404,
            "message": message,
            "data": None
        }
    
    @staticmethod
    def forbidden(message: str = "权限不足") -> dict:
        """权限不足响应"""
        return {
            "code": 403,
            "message": message,
            "data": None
        }
    
    @staticmethod
    def unauthorized(message: str = "未授权访问") -> dict:
        """未授权响应"""
        return {
            "code": 401,
            "message": message,
            "data": None
        }


def success_response(data: Any = None, message: str = "操作成功") -> dict:
    """快捷成功响应函数"""
    return ApiResponse.success(data, message)


def error_response(message: str = "操作失败", code: int = 400, data: Any = None) -> dict:
    """快捷错误响应函数"""
    return ApiResponse.error(message, code, data)
