#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试上游API响应格式
"""

import requests
import json
import sys

def test_1688_detail_api():
    """测试1688商品详情API"""
    url = "http://localhost:8000/api/v1/upstream/detail"
    data = {
        "product_url": "https://detail.1688.com/offer/932222752479.html",
        "language": "zh"
    }
    
    try:
        print("🔍 测试1688商品详情API...")
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功")
            
            # 验证响应格式
            print("\n📋 验证响应格式:")
            print(f"  - code: {result.get('code')}")
            print(f"  - message: {result.get('message')}")
            print(f"  - data存在: {'data' in result}")
            
            if 'data' in result and result['data']:
                data = result['data']
                print(f"\n📦 商品信息:")
                print(f"  - ID: {data.get('id')}")
                print(f"  - 名称: {data.get('name', '')[:50]}...")
                print(f"  - 价格: {data.get('price')}分")
                print(f"  - 市场价: {data.get('marketPrice')}分")
                print(f"  - 库存: {data.get('stock')}")
                print(f"  - 销量: {data.get('salesCount')}")
                print(f"  - 来源: {data.get('source')}")
                print(f"  - 店铺: {data.get('shopName', '')[:30]}...")
                
                # 验证SKU格式
                skus = data.get('skus', [])
                print(f"\n📋 SKU信息 (共{len(skus)}个):")
                if skus:
                    sku = skus[0]  # 检查第一个SKU
                    print(f"  - SKU ID: {sku.get('id')}")
                    print(f"  - 价格: {sku.get('price')}分")
                    print(f"  - 库存: {sku.get('stock')}")
                    
                    properties = sku.get('properties', [])
                    print(f"  - 属性数量: {len(properties)}")
                    if properties:
                        prop = properties[0]
                        print(f"  - 属性示例:")
                        print(f"    * propertyId: {prop.get('propertyId')}")
                        print(f"    * propertyName: {prop.get('propertyName')}")
                        print(f"    * valueId: {prop.get('valueId')}")
                        print(f"    * valueName: {prop.get('valueName')}")
                
                # 验证props字段
                props = data.get('props', [])
                print(f"\n🏷️ 商品属性 (共{len(props)}个):")
                for prop in props[:3]:  # 显示前3个
                    print(f"  - {prop.get('name')}: {prop.get('value')}")
                
                print("\n✅ 数据格式验证通过！")
                return True
            else:
                print("❌ 响应中没有data字段或data为空")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_1688_detail_api()
    sys.exit(0 if success else 1)
