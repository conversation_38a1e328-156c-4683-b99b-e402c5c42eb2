"""
平台管理API路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import Optional
from datetime import datetime, timezone
from pydantic import BaseModel
import json

from app.database import get_db
from app.models import Platform, PlatformStatus
from app.utils.response import success_response, error_response

router = APIRouter(prefix="/api/v1/platforms", tags=["平台管理"])

# Pydantic模型
class PlatformCreate(BaseModel):
    code: str
    name: str
    name_en: Optional[str] = None
    website_url: Optional[str] = None
    logo_url: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = "developing"
    is_enabled: Optional[bool] = True
    priority: Optional[int] = 1
    crawler_config: Optional[dict] = None
    rate_limit: Optional[int] = 60
    timeout: Optional[int] = 30

class PlatformUpdate(BaseModel):
    code: Optional[str] = None
    name: Optional[str] = None
    name_en: Optional[str] = None
    website_url: Optional[str] = None
    logo_url: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    is_enabled: Optional[bool] = None
    priority: Optional[int] = None
    crawler_config: Optional[dict] = None
    rate_limit: Optional[int] = None
    timeout: Optional[int] = None

class PlatformResponse(BaseModel):
    id: int
    code: str
    name: str
    name_en: Optional[str]
    website_url: Optional[str]
    logo_url: Optional[str]
    description: Optional[str]
    status: str
    is_enabled: bool
    priority: int
    crawler_config: Optional[dict]
    rate_limit: int
    timeout: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]

    class Config:
        from_attributes = True

@router.get("/")
async def get_platforms(
    db: Session = Depends(get_db),
    status: Optional[str] = Query(None, description="状态筛选"),
    enabled_only: bool = Query(False, description="仅显示启用的平台"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取平台列表"""
    try:
        query = db.query(Platform)

        # 筛选条件
        if status:
            query = query.filter(Platform.status == status)
        if enabled_only:
            query = query.filter(Platform.is_enabled == True)

        # 分页
        offset = (page - 1) * size
        platforms = query.order_by(desc(Platform.priority), desc(Platform.created_at)).offset(offset).limit(size).all()

        # 处理crawler_config字段
        for platform in platforms:
            if platform.crawler_config:
                try:
                    platform.crawler_config = json.loads(platform.crawler_config)
                except:
                    platform.crawler_config = {}
            else:
                platform.crawler_config = {}

        return success_response(platforms, "获取平台列表成功")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取平台列表失败: {str(e)}")

@router.post("/", response_model=PlatformResponse)
async def create_platform(platform: PlatformCreate, db: Session = Depends(get_db)):
    """创建新平台"""
    try:
        # 验证平台代码唯一性
        existing = db.query(Platform).filter(Platform.code == platform.code).first()
        if existing:
            raise HTTPException(status_code=400, detail=f"平台代码 '{platform.code}' 已存在")
        
        # 验证状态
        try:
            status_enum = PlatformStatus(platform.status)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的平台状态: {platform.status}")
        
        # 处理爬虫配置
        crawler_config_str = None
        if platform.crawler_config:
            crawler_config_str = json.dumps(platform.crawler_config, ensure_ascii=False)
        
        # 创建平台
        db_platform = Platform(
            code=platform.code,
            name=platform.name,
            name_en=platform.name_en,
            website_url=platform.website_url,
            logo_url=platform.logo_url,
            description=platform.description,
            status=status_enum,
            is_enabled=platform.is_enabled,
            priority=platform.priority,
            crawler_config=crawler_config_str,
            rate_limit=platform.rate_limit,
            timeout=platform.timeout,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        
        db.add(db_platform)
        db.commit()
        db.refresh(db_platform)
        
        # 处理返回的crawler_config
        if db_platform.crawler_config:
            try:
                db_platform.crawler_config = json.loads(db_platform.crawler_config)
            except:
                db_platform.crawler_config = {}
        else:
            db_platform.crawler_config = {}
        
        return success_response(db_platform, "平台创建成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建平台失败: {str(e)}")

@router.get("/{platform_id}", response_model=PlatformResponse)
async def get_platform(platform_id: int, db: Session = Depends(get_db)):
    """获取单个平台详情"""
    platform = db.query(Platform).filter(Platform.id == platform_id).first()
    if not platform:
        raise HTTPException(status_code=404, detail="平台不存在")
    
    # 处理crawler_config字段
    if platform.crawler_config:
        try:
            platform.crawler_config = json.loads(platform.crawler_config)
        except:
            platform.crawler_config = {}
    else:
        platform.crawler_config = {}
    
    return success_response(platform, "获取平台详情成功")

@router.put("/{platform_id}", response_model=PlatformResponse)
async def update_platform(platform_id: int, platform_update: PlatformUpdate, db: Session = Depends(get_db)):
    """更新平台信息"""
    try:
        platform = db.query(Platform).filter(Platform.id == platform_id).first()
        if not platform:
            raise HTTPException(status_code=404, detail="平台不存在")
        
        # 更新字段
        update_data = platform_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if field == "status" and value:
                setattr(platform, field, PlatformStatus(value))
            elif field == "crawler_config" and value is not None:
                setattr(platform, field, json.dumps(value, ensure_ascii=False))
            elif field == "code" and value:
                # 检查代码唯一性
                existing = db.query(Platform).filter(
                    Platform.code == value, 
                    Platform.id != platform_id
                ).first()
                if existing:
                    raise HTTPException(status_code=400, detail=f"平台代码 '{value}' 已存在")
                setattr(platform, field, value)
            elif value is not None:
                setattr(platform, field, value)
        
        platform.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(platform)
        
        # 处理返回的crawler_config
        if platform.crawler_config:
            try:
                platform.crawler_config = json.loads(platform.crawler_config)
            except:
                platform.crawler_config = {}
        else:
            platform.crawler_config = {}
        
        return success_response(platform, "平台更新成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新平台失败: {str(e)}")

@router.delete("/{platform_id}")
async def delete_platform(platform_id: int, db: Session = Depends(get_db)):
    """删除平台"""
    try:
        platform = db.query(Platform).filter(Platform.id == platform_id).first()
        if not platform:
            raise HTTPException(status_code=404, detail="平台不存在")
        
        # 检查是否有关联的账号
        from app.models import CrawlerAccount
        account_count = db.query(func.count(CrawlerAccount.id)).filter(
            CrawlerAccount.platform_id == platform_id
        ).scalar()
        
        if account_count > 0:
            raise HTTPException(
                status_code=400, 
                detail=f"无法删除平台，还有 {account_count} 个关联账号"
            )
        
        db.delete(platform)
        db.commit()
        
        return success_response(None, "平台删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除平台失败: {str(e)}")

@router.get("/stats/summary")
async def get_platforms_stats(db: Session = Depends(get_db)):
    """获取平台统计信息"""
    try:
        # 总平台数
        total_platforms = db.query(func.count(Platform.id)).scalar() or 0
        
        # 启用平台数
        enabled_platforms = db.query(func.count(Platform.id)).filter(
            Platform.is_enabled == True
        ).scalar() or 0
        
        # 活跃平台数（状态为active）
        active_platforms = db.query(func.count(Platform.id)).filter(
            Platform.status == PlatformStatus.ACTIVE
        ).scalar() or 0
        
        # 按状态统计
        status_stats = db.query(
            Platform.status,
            func.count(Platform.id).label('count')
        ).group_by(Platform.status).all()
        
        # 总请求统计
        total_requests = db.query(func.sum(Platform.total_requests)).scalar() or 0
        total_successful = db.query(func.sum(Platform.successful_requests)).scalar() or 0
        
        # 成功率
        success_rate = (total_successful / total_requests * 100) if total_requests > 0 else 0
        
        stats_data = {
            "total_platforms": total_platforms,
            "enabled_platforms": enabled_platforms,
            "active_platforms": active_platforms,
            "total_requests": total_requests,
            "total_successful": total_successful,
            "success_rate": round(success_rate, 2),
            "status_distribution": [
                {"status": stat.status.value, "count": stat.count}
                for stat in status_stats
            ]
        }
        return success_response(stats_data, "获取平台统计成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.post("/{platform_id}/toggle")
async def toggle_platform(platform_id: int, db: Session = Depends(get_db)):
    """切换平台启用状态"""
    try:
        platform = db.query(Platform).filter(Platform.id == platform_id).first()
        if not platform:
            raise HTTPException(status_code=404, detail="平台不存在")
        
        platform.is_enabled = not platform.is_enabled
        platform.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(platform)
        
        toggle_data = {
            "platform_id": platform_id,
            "is_enabled": platform.is_enabled
        }
        message = f"平台已{'启用' if platform.is_enabled else '禁用'}"
        return success_response(toggle_data, message)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"切换平台状态失败: {str(e)}")

@router.get("/enabled/list")
async def get_enabled_platforms(db: Session = Depends(get_db)):
    """获取所有启用的平台列表（用于下拉选择）"""
    try:
        platforms = db.query(Platform).filter(
            Platform.is_enabled == True
        ).order_by(desc(Platform.priority), Platform.name).all()
        
        enabled_platforms = [
            {
                "id": platform.id,
                "code": platform.code,
                "name": platform.name,
                "status": platform.status.value
            }
            for platform in platforms
        ]
        return success_response(enabled_platforms, "获取启用平台列表成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取启用平台列表失败: {str(e)}")
