<template>
  <div class="crawler-pool-page">
    <div class="page-header">
      <h2>🏊‍♂️ 爬虫池管理</h2>
      <div class="header-actions">
        <button class="btn btn-secondary" @click="refreshData">🔄 刷新</button>
        <button class="btn btn-primary" @click="openCleanupModal">🧹 清理数据</button>
      </div>
    </div>

    <!-- 爬虫池状态概览 -->
    <div class="status-overview">
      <div class="status-card">
        <div class="status-icon">🎯</div>
        <div class="status-info">
          <div class="status-title">总爬虫数</div>
          <div class="status-value">{{ poolStatus.total_crawlers || 0 }}</div>
        </div>
      </div>
      
      <div class="status-card available">
        <div class="status-icon">✅</div>
        <div class="status-info">
          <div class="status-title">可用</div>
          <div class="status-value">{{ poolStatus.available || 0 }}</div>
        </div>
      </div>
      
      <div class="status-card busy">
        <div class="status-icon">⚡</div>
        <div class="status-info">
          <div class="status-title">忙碌</div>
          <div class="status-value">{{ poolStatus.busy || 0 }}</div>
        </div>
      </div>
      
      <div class="status-card blocked">
        <div class="status-icon">🚫</div>
        <div class="status-info">
          <div class="status-title">阻塞</div>
          <div class="status-value">{{ poolStatus.blocked || 0 }}</div>
        </div>
      </div>
      
      <div class="status-card health">
        <div class="status-icon">💚</div>
        <div class="status-info">
          <div class="status-title">平均健康度</div>
          <div class="status-value">{{ (poolStatus.avg_health_score || 0).toFixed(1) }}%</div>
        </div>
      </div>
      
      <div class="status-card sessions">
        <div class="status-icon">👥</div>
        <div class="status-info">
          <div class="status-title">活跃会话</div>
          <div class="status-value">{{ poolStatus.total_sessions || 0 }}</div>
        </div>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="tabs-container">
      <div class="tabs">
        <button 
          class="tab-button" 
          :class="{ active: activeTab === 'schedules' }"
          @click="activeTab = 'schedules'"
        >
          📋 调度状态
        </button>
        <button 
          class="tab-button" 
          :class="{ active: activeTab === 'sessions' }"
          @click="activeTab = 'sessions'"
        >
          👥 活跃会话
        </button>
        <button 
          class="tab-button" 
          :class="{ active: activeTab === 'rate-limits' }"
          @click="activeTab = 'rate-limits'"
        >
          🚦 频率限制
        </button>
        <button 
          class="tab-button" 
          :class="{ active: activeTab === 'detections' }"
          @click="activeTab = 'detections'"
        >
          🛡️ 反爬检测
        </button>
      </div>

      <!-- 调度状态标签页 -->
      <div v-if="activeTab === 'schedules'" class="tab-content">
        <div class="table-container">
          <div class="table-header">
            <div class="col">账号ID</div>
            <div class="col">平台</div>
            <div class="col">状态</div>
            <div class="col">优先级</div>
            <div class="col">负载</div>
            <div class="col">会话数</div>
            <div class="col">请求/分钟</div>
            <div class="col">健康度</div>
            <div class="col">最后使用</div>
            <div class="col">操作</div>
          </div>
          
          <div v-for="schedule in schedules" :key="schedule.account_id" class="table-row">
            <div class="col">{{ schedule.account_id }}</div>
            <div class="col">{{ schedule.platform_code }}</div>
            <div class="col">
              <span class="status-badge" :class="schedule.status">
                {{ getStatusText(schedule.status) }}
              </span>
            </div>
            <div class="col">{{ schedule.priority_score }}</div>
            <div class="col">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: (schedule.load_factor * 100) + '%' }"></div>
                <span class="progress-text">{{ (schedule.load_factor * 100).toFixed(1) }}%</span>
              </div>
            </div>
            <div class="col">{{ schedule.current_sessions }}/{{ schedule.max_concurrent_sessions }}</div>
            <div class="col">{{ schedule.requests_per_minute }}/{{ schedule.max_requests_per_minute }}</div>
            <div class="col">
              <span class="health-score" :class="getHealthClass(schedule.health_score)">
                {{ schedule.health_score.toFixed(1) }}%
              </span>
            </div>
            <div class="col">{{ formatDate(schedule.last_used_at) }}</div>
            <div class="col">
              <div class="action-buttons">
                <button 
                  class="btn-action btn-reset" 
                  @click="resetCrawler(schedule.account_id)"
                  title="重置状态"
                >
                  🔄
                </button>
                <select 
                  class="status-select" 
                  :value="schedule.status"
                  @change="updateCrawlerStatus(schedule.account_id, $event.target.value)"
                >
                  <option value="available">可用</option>
                  <option value="busy">忙碌</option>
                  <option value="cooling">冷却</option>
                  <option value="blocked">阻塞</option>
                  <option value="maintenance">维护</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 活跃会话标签页 -->
      <div v-if="activeTab === 'sessions'" class="tab-content">
        <div class="table-container">
          <div class="table-header">
            <div class="col">会话ID</div>
            <div class="col">客户端IP</div>
            <div class="col">平台</div>
            <div class="col">状态</div>
            <div class="col">请求数</div>
            <div class="col">成功数</div>
            <div class="col">错误数</div>
            <div class="col">风险评分</div>
            <div class="col">创建时间</div>
            <div class="col">过期时间</div>
          </div>
          
          <div v-for="session in sessions" :key="session.session_id" class="table-row">
            <div class="col">{{ session.session_id.substring(0, 8) }}...</div>
            <div class="col">{{ session.client_ip }}</div>
            <div class="col">{{ session.platform_code }}</div>
            <div class="col">
              <span class="status-badge" :class="session.status">
                {{ session.status }}
              </span>
            </div>
            <div class="col">{{ session.request_count }}</div>
            <div class="col">{{ session.success_count }}</div>
            <div class="col">{{ session.error_count }}</div>
            <div class="col">
              <span class="risk-score" :class="getRiskClass(session.risk_score)">
                {{ session.risk_score.toFixed(1) }}
              </span>
            </div>
            <div class="col">{{ formatDate(session.created_at) }}</div>
            <div class="col">{{ formatDate(session.expires_at) }}</div>
          </div>
        </div>
      </div>

      <!-- 频率限制标签页 -->
      <div v-if="activeTab === 'rate-limits'" class="tab-content">
        <div class="table-container">
          <div class="table-header">
            <div class="col">标识符</div>
            <div class="col">类型</div>
            <div class="col">平台</div>
            <div class="col">当前请求</div>
            <div class="col">最大请求</div>
            <div class="col">违规次数</div>
            <div class="col">阻塞到期</div>
            <div class="col">操作</div>
          </div>
          
          <div v-for="limit in rateLimits" :key="limit.id" class="table-row">
            <div class="col">{{ limit.identifier }}</div>
            <div class="col">{{ limit.identifier_type }}</div>
            <div class="col">{{ limit.platform_code }}</div>
            <div class="col">{{ limit.current_requests }}</div>
            <div class="col">{{ limit.max_requests }}</div>
            <div class="col">{{ limit.violation_count }}</div>
            <div class="col">{{ formatDate(limit.blocked_until) }}</div>
            <div class="col">
              <button 
                class="btn-action btn-remove" 
                @click="removeRateLimit(limit.id)"
                title="移除限制"
              >
                🗑️
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 反爬检测标签页 -->
      <div v-if="activeTab === 'detections'" class="tab-content">
        <div class="table-container">
          <div class="table-header">
            <div class="col">检测类型</div>
            <div class="col">严重程度</div>
            <div class="col">客户端IP</div>
            <div class="col">平台</div>
            <div class="col">置信度</div>
            <div class="col">状态</div>
            <div class="col">检测时间</div>
            <div class="col">详情</div>
          </div>
          
          <div v-for="detection in detections" :key="detection.id" class="table-row">
            <div class="col">{{ detection.detection_type }}</div>
            <div class="col">
              <span class="severity-badge" :class="detection.severity">
                {{ detection.severity }}
              </span>
            </div>
            <div class="col">{{ detection.client_ip }}</div>
            <div class="col">{{ detection.platform_code }}</div>
            <div class="col">{{ detection.confidence.toFixed(1) }}%</div>
            <div class="col">
              <span class="status-badge" :class="detection.status">
                {{ detection.status }}
              </span>
            </div>
            <div class="col">{{ formatDate(detection.created_at) }}</div>
            <div class="col">
              <button 
                class="btn-action btn-view" 
                @click="viewDetectionDetails(detection)"
                title="查看详情"
              >
                👁️
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import api from '@/api'

// 响应式数据
const activeTab = ref('schedules')
const poolStatus = reactive({})
const schedules = ref([])
const sessions = ref([])
const rateLimits = ref([])
const detections = ref([])

// 加载数据
const loadPoolStatus = async () => {
  try {
    const response = await api.crawlerPool.getStatus()
    if (response.code === 200) {
      Object.assign(poolStatus, response.data)
    }
  } catch (error) {
    console.error('加载爬虫池状态失败:', error)
  }
}

const loadSchedules = async () => {
  try {
    const response = await api.crawlerPool.getSchedules()
    if (response.code === 200) {
      schedules.value = response.data
    }
  } catch (error) {
    console.error('加载调度信息失败:', error)
  }
}

const loadSessions = async () => {
  try {
    const response = await api.crawlerPool.getSessions()
    if (response.code === 200) {
      sessions.value = response.data.items || []
    }
  } catch (error) {
    console.error('加载会话信息失败:', error)
  }
}

const loadRateLimits = async () => {
  try {
    const response = await api.crawlerPool.getRateLimits()
    if (response.code === 200) {
      rateLimits.value = response.data
    }
  } catch (error) {
    console.error('加载频率限制失败:', error)
  }
}

const loadDetections = async () => {
  try {
    const response = await api.crawlerPool.getDetections()
    if (response.code === 200) {
      detections.value = response.data.items || []
    }
  } catch (error) {
    console.error('加载检测记录失败:', error)
  }
}

// 刷新所有数据
const refreshData = async () => {
  await Promise.all([
    loadPoolStatus(),
    loadSchedules(),
    loadSessions(),
    loadRateLimits(),
    loadDetections()
  ])
}

// 工具函数
const getStatusText = (status) => {
  const statusMap = {
    'available': '可用',
    'busy': '忙碌',
    'cooling': '冷却',
    'blocked': '阻塞',
    'maintenance': '维护'
  }
  return statusMap[status] || status
}

const getHealthClass = (score) => {
  if (score >= 90) return 'excellent'
  if (score >= 70) return 'good'
  if (score >= 50) return 'fair'
  return 'poor'
}

const getRiskClass = (score) => {
  if (score >= 80) return 'high'
  if (score >= 50) return 'medium'
  return 'low'
}

const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString()
}

// 操作函数
const updateCrawlerStatus = async (accountId, status) => {
  try {
    const response = await api.crawlerPool.updateCrawlerStatus(accountId, status)
    if (response.code === 200) {
      alert('状态更新成功')
      loadSchedules()
    } else {
      alert('状态更新失败: ' + response.message)
    }
  } catch (error) {
    console.error('更新状态失败:', error)
    alert('状态更新失败')
  }
}

const resetCrawler = async (accountId) => {
  if (!confirm('确定要重置此爬虫的状态吗？')) return

  try {
    const response = await api.crawlerPool.resetCrawler(accountId)
    if (response.code === 200) {
      alert('爬虫状态已重置')
      refreshData()
    } else {
      alert('重置失败: ' + response.message)
    }
  } catch (error) {
    console.error('重置爬虫失败:', error)
    alert('重置失败')
  }
}

const removeRateLimit = async (limitId) => {
  if (!confirm('确定要移除此频率限制吗？')) return

  try {
    const response = await api.crawlerPool.removeRateLimit(limitId)
    if (response.code === 200) {
      alert('频率限制已移除')
      loadRateLimits()
    } else {
      alert('移除失败: ' + response.message)
    }
  } catch (error) {
    console.error('移除频率限制失败:', error)
    alert('移除失败')
  }
}

const viewDetectionDetails = (detection) => {
  const details = JSON.stringify(detection.detection_details, null, 2)
  alert(`检测详情:\n${details}`)
}

const openCleanupModal = () => {
  if (confirm('确定要清理过期数据吗？这将清理过期会话和旧记录。')) {
    cleanupData()
  }
}

const cleanupData = async () => {
  try {
    const response = await api.crawlerPool.cleanup({
      cleanup_sessions: true,
      cleanup_rate_limits: false,
      cleanup_detections: false,
      days: 7
    })
    if (response.code === 200) {
      alert('数据清理完成')
      refreshData()
    } else {
      alert('清理失败: ' + response.message)
    }
  } catch (error) {
    console.error('数据清理失败:', error)
    alert('清理失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.crawler-pool-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.page-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f1f5f9;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e2e8f0;
}

/* 状态概览 */
.status-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.status-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #e5e7eb;
  transition: all 0.2s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.status-card.available {
  border-left-color: #10b981;
}

.status-card.busy {
  border-left-color: #f59e0b;
}

.status-card.blocked {
  border-left-color: #ef4444;
}

.status-card.health {
  border-left-color: #8b5cf6;
}

.status-card.sessions {
  border-left-color: #06b6d4;
}

.status-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 12px;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.status-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

/* 标签页 */
.tabs-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.tab-button {
  flex: 1;
  padding: 16px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: #f8fafc;
  color: #374151;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #f8fafc;
}

.tab-content {
  padding: 20px;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.table-header {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
  min-height: 50px;
  border-radius: 8px 8px 0 0;
}

.table-row {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  min-height: 60px;
  align-items: center;
}

.table-row:hover {
  background: linear-gradient(135deg, #f8f9ff 0%, #f5f7ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.col {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  font-size: 14px;
  text-align: center;
}

/* 状态标签 */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.available {
  background: #f0fdf4;
  color: #16a34a;
}

.status-badge.busy {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.cooling {
  background: #dbeafe;
  color: #2563eb;
}

.status-badge.blocked {
  background: #fef2f2;
  color: #dc2626;
}

.status-badge.maintenance {
  background: #f3f4f6;
  color: #6b7280;
}

.status-badge.active {
  background: #f0fdf4;
  color: #16a34a;
}

.status-badge.expired {
  background: #fef2f2;
  color: #dc2626;
}

/* 进度条 */
.progress-bar {
  position: relative;
  width: 100%;
  height: 20px;
  background: #f3f4f6;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 11px;
  font-weight: 500;
  color: #374151;
}

/* 健康度评分 */
.health-score.excellent {
  color: #16a34a;
  font-weight: 600;
}

.health-score.good {
  color: #65a30d;
  font-weight: 600;
}

.health-score.fair {
  color: #d97706;
  font-weight: 600;
}

.health-score.poor {
  color: #dc2626;
  font-weight: 600;
}

/* 风险评分 */
.risk-score.low {
  color: #16a34a;
}

.risk-score.medium {
  color: #d97706;
}

.risk-score.high {
  color: #dc2626;
}

/* 严重程度标签 */
.severity-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.severity-badge.low {
  background: #f0fdf4;
  color: #16a34a;
}

.severity-badge.medium {
  background: #fef3c7;
  color: #d97706;
}

.severity-badge.high {
  background: #fef2f2;
  color: #dc2626;
}

.severity-badge.critical {
  background: #7f1d1d;
  color: white;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.btn-action {
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.btn-action:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-action.btn-reset {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-action.btn-remove {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.btn-action.btn-view {
  background: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
}

.status-select {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  cursor: pointer;
}

.status-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}
</style>
