"""
密码加密解密工具类
使用AES加密算法对密码进行加密存储
"""

import os
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class PasswordCrypto:
    """密码加密解密类"""
    
    def __init__(self, secret_key: str = None):
        """
        初始化加密器
        
        Args:
            secret_key: 加密密钥，如果不提供则从环境变量获取
        """
        if secret_key is None:
            secret_key = os.getenv('PASSWORD_SECRET_KEY', 'AqentCrawler2024DefaultSecretKey')
        
        # 使用PBKDF2从密钥生成Fernet密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=b'AqentCrawlerSalt',  # 固定盐值，实际项目中应该使用随机盐
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(secret_key.encode()))
        self.fernet = Fernet(key)
    
    def encrypt_password(self, password: str) -> str:
        """
        加密密码
        
        Args:
            password: 明文密码
            
        Returns:
            加密后的密码（base64编码）
        """
        if not password:
            return ""
        
        try:
            encrypted = self.fernet.encrypt(password.encode('utf-8'))
            return base64.urlsafe_b64encode(encrypted).decode('utf-8')
        except Exception as e:
            print(f"密码加密失败: {str(e)}")
            return ""
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """
        解密密码

        Args:
            encrypted_password: 加密的密码（base64编码）

        Returns:
            明文密码
        """
        if not encrypted_password:
            return ""

        try:
            # 添加padding处理
            encrypted_password = encrypted_password.strip()

            # 先尝试解密，如果失败再判断是否为明文
            try:
                # 尝试解码base64
                encrypted_data = base64.urlsafe_b64decode(encrypted_password.encode('utf-8'))
                # 尝试解密
                decrypted = self.fernet.decrypt(encrypted_data)
                return decrypted.decode('utf-8')
            except Exception as decrypt_error:
                # 解密失败，检查是否看起来像加密格式
                if self._looks_like_encrypted(encrypted_password):
                    print(f"密码看起来是加密格式但解密失败: {decrypt_error}")
                    return ""  # 返回空字符串表示解密失败
                else:
                    # 不像加密格式，可能是明文密码
                    print(f"密码似乎不是加密格式，直接返回")
                    return encrypted_password

        except Exception as e:
            print(f"密码解密过程异常: {str(e)}")
            return ""

    def _looks_like_encrypted(self, password: str) -> bool:
        """
        检查密码是否看起来像加密的

        Args:
            password: 密码字符串

        Returns:
            True if looks encrypted, False otherwise
        """
        if not password:
            return False

        # 加密的密码通常是base64编码，长度较长且包含特定字符
        if len(password) < 30:  # 加密后的密码通常很长
            return False

        # 检查是否以我们的加密前缀开始（Fernet加密后base64编码的特征）
        if password.startswith('Z0FBQUFBQm'):  # 这是Fernet加密的典型开头
            return True

        # 检查是否包含base64字符且长度足够
        import re
        base64_pattern = re.compile(r'^[A-Za-z0-9+/=_-]+$')
        return bool(base64_pattern.match(password)) and len(password) > 50
    
    def is_encrypted(self, password: str) -> bool:
        """
        检查密码是否已加密
        
        Args:
            password: 密码字符串
            
        Returns:
            True if encrypted, False otherwise
        """
        if not password:
            return False
        
        try:
            # 尝试解密，如果成功说明是加密的
            self.decrypt_password(password)
            return True
        except:
            return False


# 全局加密器实例
password_crypto = PasswordCrypto()


def encrypt_password(password: str) -> str:
    """加密密码的便捷函数"""
    return password_crypto.encrypt_password(password)


def decrypt_password(encrypted_password: str) -> str:
    """解密密码的便捷函数"""
    return password_crypto.decrypt_password(encrypted_password)


def is_password_encrypted(password: str) -> bool:
    """检查密码是否已加密的便捷函数"""
    return password_crypto.is_encrypted(password)
