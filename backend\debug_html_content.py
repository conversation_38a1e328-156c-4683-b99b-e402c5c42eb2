#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import re
import json

def debug_html_content():
    """调试HTML内容，查找数据存储方式"""

    url = "https://detail.1688.com/offer/591940079968.html"

    # 使用爬虫的cookies
    cookies = {
        'leftMenuLastMode': 'offer',
        'leftMenuModeTip': 'offer',
        'keywordsHistory': '%5B%22%E5%8D%B0%E5%BA%A6%E5%B0%8F%E5%8F%B6%E7%B4%AB%E6%AA%80%22%5D',
        'plugin_home_downLoad_cookie': '1',
        'JSESSIONID': 'F8E8F8E8F8E8F8E8F8E8F8E8F8E8F8E8',
        'EGG_SESS': 'test_session'
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://www.1688.com/',
    }
    
    print(f"🔍 请求URL: {url}")
    
    try:
        response = requests.get(url, headers=headers, cookies=cookies, timeout=30)
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📏 响应内容长度: {len(response.text)} 字符")
        
        if response.status_code == 200:
            html_content = response.text
            
            # 查找各种可能的数据存储方式
            patterns = [
                (r'window\.__INIT_DATA\s*=\s*', 'window.__INIT_DATA'),
                (r'window\.contextPath\s*,\s*', 'window.contextPath'),
                (r'window\.detailData\s*=\s*', 'window.detailData'),
                (r'window\.pageData\s*=\s*', 'window.pageData'),
                (r'window\.globalData\s*=\s*', 'window.globalData'),
                (r'window\.offerDetailData\s*=\s*', 'window.offerDetailData'),
                (r'var\s+detailData\s*=\s*', 'var detailData'),
                (r'var\s+pageData\s*=\s*', 'var pageData'),
                (r'var\s+globalData\s*=\s*', 'var globalData'),
            ]
            
            print("\n🔍 查找数据存储模式:")
            found_patterns = []
            
            for pattern, name in patterns:
                matches = re.findall(pattern, html_content)
                if matches:
                    print(f"✅ 找到 {name}: {len(matches)} 个匹配")
                    found_patterns.append(name)
                else:
                    print(f"❌ 未找到 {name}")
            
            # 查找关键字段
            print("\n🔍 查找关键字段:")
            keywords = ['skuModel', 'skuInfoMap', 'componentType', 'offerImgList', 'tempModel', 'sellerModel']

            for keyword in keywords:
                count = len(re.findall(keyword, html_content))
                if count > 0:
                    print(f"✅ 找到 {keyword}: {count} 次")
                else:
                    print(f"❌ 未找到 {keyword}")

            # 查找价格相关字段
            print("\n🔍 查找价格相关字段:")
            price_keywords = ['price', 'Price', 'finalPriceModel', 'skuMapOriginal', 'offerPriceRanges', 'tradeWithoutPromotion']

            for keyword in price_keywords:
                count = len(re.findall(keyword, html_content))
                if count > 0:
                    print(f"✅ 找到 {keyword}: {count} 次")
                    # 如果找到价格相关字段，提取一些上下文
                    if count > 0 and count < 10:  # 避免太多输出
                        matches = list(re.finditer(keyword, html_content))
                        for i, match in enumerate(matches[:3]):  # 只显示前3个匹配
                            start = max(0, match.start() - 50)
                            end = min(len(html_content), match.end() + 50)
                            context = html_content[start:end].replace('\n', ' ').replace('\r', ' ')
                            print(f"    匹配 {i+1}: ...{context}...")
                else:
                    print(f"❌ 未找到 {keyword}")
            
            # 如果找到了数据模式，尝试提取一小段内容
            if found_patterns:
                print(f"\n🔍 尝试提取第一个找到的模式: {found_patterns[0]}")
                
                if 'window.__INIT_DATA' in found_patterns:
                    pattern = r'window\.__INIT_DATA\s*=\s*'
                    match = re.search(pattern, html_content)
                    if match:
                        start = match.end()
                        # 提取前500个字符看看结构
                        sample = html_content[start:start+500]
                        print(f"📝 数据开始部分:\n{sample}")
                
                elif 'window.contextPath' in found_patterns:
                    pattern = r'window\.contextPath\s*,\s*'
                    match = re.search(pattern, html_content)
                    if match:
                        start = match.end()
                        # 提取前500个字符看看结构
                        sample = html_content[start:start+500]
                        print(f"📝 数据开始部分:\n{sample}")
            
            # 保存HTML内容到文件以便进一步分析
            with open('temp/debug_html_content.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"\n💾 HTML内容已保存到: temp/debug_html_content.html")
            
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

if __name__ == "__main__":
    debug_html_content()
