# 🕷️ AqentCrawler - 智能代购爬虫系统 (简化版)

## 📖 项目概述

AqentCrawler 是一个为海外代购平台设计的智能爬虫系统，支持多平台商品抓取、多语言翻译等功能。

**设计理念**: 简单优先，快速迭代，专注核心功能实现。

## 🏗️ 技术架构

```
海外用户 → 代购站前端 → 代购站后端 → [HTTPS请求] → 国内爬虫系统
                                                    ├── FastAPI后端 (单体应用)
                                                    ├── MySQL数据库
                                                    ├── Redis缓存
                                                    └── Vue3管理后台
```

## 🛠️ 技术栈

- **后端**: FastAPI + Python 3.9+
- **前端**: Vue 3 + Naive UI + Vite
- **数据库**: MySQL 8.0 + Redis
- **爬虫**: Playwright + BeautifulSoup
- **部署**: 传统部署 (无Docker/K8s)

## 🚀 快速开始

### 1. 环境准备

```bash
# 必需环境
- Python 3.9+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+
```

### 2. 项目初始化

```bash
# 1. 克隆项目
git clone <repository-url>
cd AqentCrawler

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库等信息

# 3. 安装后端依赖
cd backend
pip install -r requirements.txt

# 4. 安装前端依赖
cd ../frontend
npm install

# 5. 返回项目根目录
cd ..
```

### 3. 启动服务

#### 方式一: 一键启动 (推荐)

```bash
# 使用快速启动脚本
python start.py

# 选择启动模式:
# 1 - 开发环境 (自动重载，调试模式)
# 2 - 生产环境 (性能优化，多进程)
# 5 - 安装依赖 (首次使用必选)
```

#### 方式二: 使用启动脚本

```bash
# 开发环境
python scripts/start_dev.py

# 生产环境
python scripts/start_prod.py
```

#### 方式三: 手动启动

```bash
# 终端1: 启动后端
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 终端2: 启动前端
cd frontend
npm run dev
```

### 4. 访问系统

- **前端管理界面**: http://localhost:3000
- **后端API服务**: http://localhost:8000
- **API交互文档**: http://localhost:8000/docs
- **系统健康检查**: http://localhost:8000/health

### 5. 默认账号

- **用户名**: `admin`
- **密码**: `admin123`

## 📁 项目结构

```
AqentCrawler/
├── backend/                    # 后端API服务
│   ├── app/                   # 应用核心代码
│   │   ├── crawler/          # 爬虫引擎
│   │   ├── routers/          # API路由
│   │   ├── models/           # 数据模型
│   │   ├── services/         # 业务服务
│   │   └── utils/            # 工具函数
│   ├── requirements.txt      # Python依赖
│   └── .env                  # 环境配置
├── frontend/                   # 前端管理界面
│   ├── src/                  # 源代码
│   │   ├── views/           # 页面组件
│   │   ├── api/             # API接口
│   │   └── router/          # 路由配置
│   ├── package.json         # Node.js依赖
│   └── vite.config.js       # 构建配置
├── scripts/                   # 启动和部署脚本
│   ├── start_dev.py         # 开发环境启动
│   └── start_prod.py        # 生产环境启动
├── chrome-extension/          # Chrome扩展 (Cookie提取)
├── docs/                      # 项目文档
├── start.py                   # 快速启动脚本
├── DEPLOYMENT_GUIDE.md       # 部署指南
└── README.md                 # 项目说明
```

## 🛠️ 核心功能

### 🕷️ 爬虫功能
- **多平台支持**: 淘宝、天猫、京东等主流电商
- **智能反爬**: 自动处理验证码、滑块等反爬机制
- **数据提取**: 商品搜索、详情、价格、图片等信息
- **实时抓取**: 支持实时商品信息获取

### 🔐 认证系统
- **JWT认证**: 安全的用户认证机制
- **预置Token**: 4个长期有效的API Token
- **权限控制**: 基于角色的访问控制
- **会话管理**: 自动登录状态维护

### 📊 管理功能
- **平台管理**: 动态添加和配置爬虫平台
- **账号管理**: 爬虫账号池管理和轮换
- **代理管理**: 代理IP池配置和监控
- **日志统计**: 详细的API调用日志和统计

### 🌐 API服务
- **RESTful API**: 标准的REST接口设计
- **异步处理**: 高性能异步请求处理
- **错误容错**: 完善的错误处理和重试机制
- **文档完整**: 自动生成的API交互文档

## 📚 文档资源

### 📖 使用文档
- **[部署指南](DEPLOYMENT_GUIDE.md)** - 详细的部署和配置说明
- **[API文档](backend/API_DOCUMENTATION.md)** - 完整的API接口文档
- **[架构文档](SIMPLIFIED_ARCHITECTURE.md)** - 系统架构设计说明
- **[SQL调试指南](backend/docs/SQL_DEBUG_GUIDE.md)** - SQL语句打印和调试功能说明

### 🔑 预置Token
系统预置了4个长期有效的API Token，供上游系统直接使用：

| Token ID | 描述 | 有效期 | 权限 |
|----------|------|--------|------|
| `upstream_system_1` | 上游系统1专用 | 365天 | 全权限 |
| `upstream_system_2` | 上游系统2专用 | 90天 | 搜索+详情 |
| `test_system` | 测试系统专用 | 30天 | 全权限 |
| `dev_system` | 开发环境专用 | 180天 | 全权限 |

获取Token：
```bash
# 管理员登录后访问
GET /api/v1/auth/preset-tokens
```

### 🚀 快速体验
```bash
# 使用预置Token直接调用API
curl -X POST http://localhost:8000/api/v1/search \
  -H "Authorization: Bearer <preset_token>" \
  -H "Content-Type: application/json" \
  -d '{"query": "手机", "platform": "taobao"}'
```

## 🎯 系统特性

### 📊 性能指标
- **高并发**: 支持多进程并发处理
- **快速响应**: 平均API响应时间 < 3秒
- **高成功率**: 爬取成功率 > 90%
- **稳定可靠**: 7x24小时稳定运行

### 🔒 安全特性
- **JWT认证**: 安全的用户身份验证
- **Token管理**: 预置长期有效Token
- **权限控制**: 基于角色的访问控制
- **审计日志**: 完整的API调用日志

### 🛠️ 技术优势
- **简化架构**: 去除复杂的微服务架构
- **快速部署**: 一键启动脚本，简化部署流程
- **易于维护**: 清晰的代码结构和文档
- **扩展性强**: 支持动态添加新的爬虫平台
- **SQL调试**: 智能SQL语句打印，支持开发/生产环境切换

## 🤝 技术支持

### 常见问题
- 查看 [部署指南](DEPLOYMENT_GUIDE.md) 解决部署问题
- 查看 [API文档](backend/API_DOCUMENTATION.md) 了解接口使用
- 检查系统日志: `backend/logs/app.log`

### 联系方式
- 项目维护者: [dingjiliang](mailto:<EMAIL>)
- 技术交流: 欢迎提交Issue和PR

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

---

**AqentCrawler** - 让代购爬虫变得简单高效 🚀
