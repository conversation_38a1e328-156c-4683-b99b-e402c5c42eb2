"""
1688平台爬虫实现
基于测试文件 test_1688.py 的完整实现
"""
import requests
import hashlib
import json
import time
import re
import urllib.parse
from typing import Dict, List, Optional, Any
from datetime import datetime
import asyncio
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.database import get_db
from app.models import CrawlerAccount, Platform


class Alibaba1688Crawler:
    """1688平台爬虫类"""
    
    def __init__(self):
        self.platform_code = "1688"
        self.base_url = "https://h5api.m.1688.com/h5/mtop.relationrecommend.wirelessrecommend.recommend/2.0/"
        self.app_key = "********"
        self.app_id = 32517
        self.default_headers = {
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
        self._last_init_data = None  # 用于调试
        
    def _get_sign(self, em_token: str, timestamp: int, keyword: str) -> tuple:
        """
        生成1688 API签名
        
        Args:
            em_token: 从cookie中提取的token
            timestamp: 当前时间戳
            keyword: 搜索关键词（已URL编码）
            
        Returns:
            tuple: (sign, ep_data)
        """
        params = {
            "verticalProductFlag": "pcmarket",
            "searchScene": "pcOfferSearch",
            "charset": "UTF-8",
            "beginPage": "1",
            "pageSize": 60,
            "keywords": keyword,
            "spm": "a260k.home2025.searchbox.0",
            "method": "getOfferList"
        }
        
        ep = {
            "appId": self.app_id,
            "params": json.dumps(params, separators=(',', ':'), ensure_ascii=False)
        }
        
        ep_data = json.dumps(ep, separators=(',', ':'), ensure_ascii=False)
        string = f"{em_token}&{timestamp}&{self.app_key}&{ep_data}"
        
        md5_hash = hashlib.md5()
        md5_hash.update(string.encode('utf-8'))
        sign = md5_hash.hexdigest()
        
        return sign, ep_data
    
    def _extract_token_from_cookie(self, cookie: str) -> Optional[str]:
        """
        从cookie中提取_m_h5_tk token
        
        Args:
            cookie: 完整的cookie字符串
            
        Returns:
            str: 提取的token，如果未找到返回None
        """
        if not cookie:
            return None
            
        # 查找_m_h5_tk字段
        match = re.search(r'_m_h5_tk=([^;]+)', cookie)
        if match:
            token_with_timestamp = match.group(1)
            # 提取token部分（下划线前的部分）
            token = token_with_timestamp.split('_')[0]
            return token
        return None
    
    def _parse_location(self, procity: str) -> tuple:
        """
        解析地区信息
        
        Args:
            procity: 地区字符串，如"广东 深圳"
            
        Returns:
            tuple: (province, city)
        """
        if not procity:
            return "", ""
            
        parts = procity.split(' ')
        if len(parts) == 2:
            return parts[0], parts[1]
        elif len(parts) > 2:
            return parts[0], parts[-1]
        else:
            return parts[0], ""
    
    def _clean_title(self, title: str) -> str:
        """
        清理商品标题，移除HTML标签
        
        Args:
            title: 原始标题
            
        Returns:
            str: 清理后的标题
        """
        if not title:
            return ""
        return title.replace('<span class=H>', '').replace('</span>', '')
    
    async def search_products(self, query: str, account_username: Optional[str] = None) -> Dict[str, Any]:
        """
        搜索1688商品
        
        Args:
            query: 搜索关键词
            account_username: 指定使用的账号用户名
            
        Returns:
            Dict: 搜索结果
        """
        start_time = time.time()
        
        try:
            print(f"🔍 开始1688搜索: 关键词='{query}', 账号={account_username}")
            
            # 获取数据库会话
            db = next(get_db())
            
            try:
                # 获取平台信息
                platform = db.query(Platform).filter(Platform.code == self.platform_code).first()
                if not platform:
                    raise Exception(f"未找到{self.platform_code}平台配置")
                
                # 获取可用账号
                account = None
                if account_username:
                    # 使用指定账号
                    account = db.query(CrawlerAccount).filter(
                        and_(
                            CrawlerAccount.platform_id == platform.id,
                            CrawlerAccount.username == account_username,
                            CrawlerAccount.status == 'active',
                            CrawlerAccount.is_enabled == True  # 只使用已启用的爬虫
                        )
                    ).first()
                else:
                    # 自动选择可用账号
                    accounts = db.query(CrawlerAccount).filter(
                        and_(
                            CrawlerAccount.platform_id == platform.id,
                            CrawlerAccount.status == 'active',
                            CrawlerAccount.is_enabled == True  # 只使用已启用的爬虫
                        )
                    ).all()
                    
                    # 筛选有完整配置的账号
                    for acc in accounts:
                        if acc.cookie and len(acc.cookie) > 100:
                            account = acc
                            print(f"✅ 找到有效1688账号: {acc.username}")
                            break
                
                if not account:
                    print("❌ 未找到有完整配置的1688账号")
                    return {
                        "platform": self.platform_code,
                        "query": query,
                        "total": 0,
                        "products": [],
                        "response_time": 0,
                        "account_used": account_username,
                        "crawl_method": "无完整配置",
                        "error": "未找到有完整配置的1688账号"
                    }
                
                # 打印账号Cookie详情供调试
                print("=" * 80)
                print(f"🔍 使用账号: {account.username}")
                print(f"🔍 Cookie长度: {len(account.cookie)}")
                print("🔍 Cookie详细内容:")
                print("=" * 80)

                # 解析并打印Cookie
                cookie_dict = {}
                if account.cookie:
                    for cookie_pair in account.cookie.split(';'):
                        cookie_pair = cookie_pair.strip()
                        if '=' in cookie_pair:
                            name, value = cookie_pair.split('=', 1)
                            cookie_dict[name.strip()] = value.strip()

                # 按重要性排序打印Cookie
                important_cookies = [
                    'leftMenuLastMode', 'mtop_partitioned_detect', '_m_h5_tk', '_m_h5_tk_enc',
                    'leftMenuModeTip', 'xlly_s', 'plugin_home_downLoad_cookie', 'keywordsHistory',
                    'cna', 'cookie1', 'cookie2', 'cookie17', 'sgcookie', 't', '_tb_token_',
                    'sg', 'csg', 'lid', 'unb', 'uc4', '_nk_', '__cn_logon__', '__cn_logon_id__',
                    'ali_apache_track', 'ali_apache_tracktmp', '_user_vitals_session_data_',
                    'isg', 'tfstk', 'last_mid', '_csrf_token'
                ]

                for cookie_name in important_cookies:
                    if cookie_name in cookie_dict:
                        value = cookie_dict[cookie_name]
                        display_value = value[:50] + "..." if len(value) > 50 else value
                        print(f"  ✓ {cookie_name}: {display_value}")
                    else:
                        print(f"  ✗ {cookie_name}: 缺失")

                # 打印其他Cookie
                other_cookies = set(cookie_dict.keys()) - set(important_cookies)
                if other_cookies:
                    print("🔍 其他Cookie:")
                    for cookie_name in sorted(other_cookies):
                        value = cookie_dict[cookie_name]
                        display_value = value[:50] + "..." if len(value) > 50 else value
                        print(f"  + {cookie_name}: {display_value}")

                print("=" * 80)

                # 提取token
                em_token = self._extract_token_from_cookie(account.cookie)
                if not em_token:
                    print("❌ 无法从cookie中提取token")
                    return {
                        "platform": self.platform_code,
                        "query": query,
                        "total": 0,
                        "products": [],
                        "response_time": 0,
                        "account_used": account.username,
                        "crawl_method": "token提取失败",
                        "error": "无法从cookie中提取有效token"
                    }

                print(f"✅ 成功提取token: {em_token[:10]}...")

                # 特别检查关键的t参数
                if 't=' in account.cookie:
                    t_match = re.search(r't=([^;]+)', account.cookie)
                    if t_match:
                        t_value = t_match.group(1)
                        print(f"🔑 关键t参数: {t_value}")
                        print(f"🔑 t参数长度: {len(t_value)}")
                    else:
                        print("⚠️ 无法提取t参数")
                else:
                    print("❌ Cookie中缺少t参数！")
                
                # 执行搜索
                products = await self._do_search(query, account.cookie, em_token, account.user_agent)
                
                response_time = int((time.time() - start_time) * 1000)
                
                return {
                    "platform": self.platform_code,
                    "query": query,
                    "total": len(products),
                    "products": products,
                    "response_time": response_time,
                    "account_used": account.username,
                    "crawl_method": "1688 API",
                    "success": True
                }
                
            finally:
                db.close()
                
        except Exception as e:
            response_time = int((time.time() - start_time) * 1000)
            print(f"❌ 1688搜索失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
            
            return {
                "platform": self.platform_code,
                "query": query,
                "total": 0,
                "products": [],
                "response_time": response_time,
                "account_used": account_username,
                "crawl_method": "API请求失败",
                "error": str(e)
            }
    
    async def _do_search(self, query: str, cookie: str, em_token: str, user_agent: Optional[str] = None) -> List[Dict]:
        """
        执行实际的搜索请求
        
        Args:
            query: 搜索关键词
            cookie: 完整cookie
            em_token: 提取的token
            user_agent: 用户代理
            
        Returns:
            List[Dict]: 商品列表
        """
        # URL编码关键词
        keyword_encoded = urllib.parse.quote(query)
        
        # 获取当前时间戳
        timestamp = int(time.time() * 1000)
        
        # 生成签名
        sign, ep_data = self._get_sign(em_token, timestamp, keyword_encoded)
        
        # 构建请求头
        headers = self.default_headers.copy()
        headers['cookie'] = cookie
        if user_agent:
            headers['user-agent'] = user_agent
        
        # 构建请求参数
        params = {
            'jsv': '2.7.4',
            'appKey': self.app_key,
            't': timestamp,
            'sign': sign,
            'api': 'mtop.relationrecommend.WirelessRecommend.recommend',
            'ignoreLogin': 'true',
            'prefix': 'h5api',
            'v': '2.0',
            'dataType': 'jsonp',
            'jsonpIncPrefix': 'fetchTpp_32517_getOfferList',
            'timeout': '20000',
            'type': 'jsonp',
            'callback': 'mtopjsonpfetchTpp_32517_getOfferList6',
            'data': ep_data
        }
        
        print(f"🔍 发送1688搜索请求: {query}")
        print(f"🌐 请求URL: {self.base_url}")
        print(f"📋 请求参数: {params}")
        print(f"📋 请求头: {headers}")

        # 发送请求
        response = requests.get(self.base_url, headers=headers, params=params, timeout=30)
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📡 响应内容长度: {len(response.text)}")
        print(f"📡 响应前200字符: {response.text[:200]}")

        response.raise_for_status()

        # 解析响应
        return self._parse_response(response.text, query)

    def _parse_response(self, response_text: str, query: str) -> List[Dict]:
        """
        解析1688 API响应

        Args:
            response_text: API响应文本
            query: 搜索关键词

        Returns:
            List[Dict]: 解析后的商品列表
        """
        try:
            print(f"🔍 开始解析1688响应，响应长度: {len(response_text)}")

            # 提取JSON字符串 - 使用更灵活的正则表达式
            json_match = re.findall(r'mtopjsonpfetchTpp_32517_getOfferList\d+ \d*\((.*)', response_text)
            if not json_match:
                print("❌ 无法从响应中提取JSON数据")
                print(f"🔍 响应内容样本: {response_text[:500]}")

                # 尝试其他可能的JSON模式
                alternative_patterns = [
                    r'mtopjsonpfetchTpp_\d+_getOfferList\d+ \d*\((.*)\)',  # 更通用的mtop模式
                    r'mtopjsonp\w+\((.*)\)',
                    r'jsonp\w*\((.*)\)',
                    r'callback\((.*)\)',
                    r'\{.*\}'
                ]

                for pattern in alternative_patterns:
                    alt_match = re.findall(pattern, response_text)
                    if alt_match:
                        print(f"🔍 找到替代JSON模式: {pattern}")
                        json_str = alt_match[0]
                        if pattern == r'\{.*\}':
                            json_str = alt_match[0]
                        else:
                            json_str = alt_match[0][:-1] if alt_match[0].endswith(')') else alt_match[0]
                        break
                else:
                    return []
            else:
                json_str = json_match[0][:-1]  # 移除最后的括号

            print(f"🔍 提取的JSON字符串长度: {len(json_str)}")
            print(f"🔍 JSON字符串前100字符: {json_str[:100]}")

            json_data = json.loads(json_str)
            print(f"✅ JSON解析成功，数据结构: {list(json_data.keys()) if isinstance(json_data, dict) else type(json_data)}")

            # 检查响应状态
            if json_data.get('ret') and json_data['ret'][0] != 'SUCCESS::调用成功':
                print(f"❌ API调用失败: {json_data.get('ret')}")
                return []

            # 提取商品列表
            items_array = json_data.get('data', {}).get('data', {}).get('OFFER', {}).get('items', [])
            if not items_array:
                print("❌ 响应中没有找到商品数据")
                return []

            products = []
            for index, item in enumerate(items_array):
                try:
                    # 获取商品数据（数据在item['data']中）
                    item_data = item.get('data', {})

                    # 解析地区信息
                    province = item_data.get('province', '')
                    city = item_data.get('city', '')

                    # 清理标题
                    title = self._clean_title(item_data.get('title', ''))

                    # 提取价格信息
                    price_info = item_data.get('priceInfo', {})
                    price = price_info.get('price', '0')

                    # 提取销量信息
                    after_price = item_data.get('afterPrice', {})
                    sales_text = after_price.get('text', '')

                    # 构建地区信息
                    location = f"{province} {city}".strip() if province or city else ""

                    # 构建商品数据（按照上游系统格式）
                    offer_id = item_data.get('offerId', index)

                    # 解析价格（转换为分）
                    price_value = 0
                    market_price_value = 0
                    try:
                        if price and price != '0':
                            # 提取数字部分
                            price_numbers = re.findall(r'[\d.]+', str(price))
                            if price_numbers:
                                price_value = int(float(price_numbers[0]) * 100)
                                market_price_value = int(float(price_numbers[-1]) * 100) if len(price_numbers) > 1 else price_value
                    except:
                        price_value = market_price_value = 0

                    # 解析销量
                    sales_count = 0
                    try:
                        if sales_text:
                            sales_numbers = re.findall(r'\d+', sales_text)
                            if sales_numbers:
                                sales_count = int(sales_numbers[0])
                    except:
                        sales_count = 0

                    # 构建商品链接 - 直接使用offer_id构建标准链接
                    # linkUrl是跳转链接，不是直接的商品详情链接，所以我们直接构建标准链接
                    product_link = f"https://detail.1688.com/offer/{offer_id}.html"

                    # 保存原始跳转链接作为备用
                    original_link = item_data.get('linkUrl', '')

                    # 构建店铺链接
                    login_id = item_data.get('loginId', '')
                    shop_link = f"https://shop{login_id}.1688.com/" if login_id else ""

                    # 生成关键词
                    keywords = self._generate_keywords_from_title(title)

                    # 构建上游系统格式的商品数据
                    product = {
                        'id': int(offer_id) if str(offer_id).isdigit() else index + 10000,
                        'name': title,
                        'introduction': title[:100] + "..." if len(title) > 100 else title,
                        'categoryId': 0,  # 默认分类ID
                        'picUrl': item_data.get('offerPicUrl', ''),
                        'sliderPicUrls': [item_data.get('offerPicUrl', '')] if item_data.get('offerPicUrl') else [],
                        'specType': False,  # 默认无规格
                        'price': price_value,
                        'marketPrice': market_price_value,
                        'stock': 999999,  # 默认库存
                        'salesCount': sales_count,
                        'scores': 4,  # 默认评分
                        'newest': False,
                        'sale': sales_count > 100,  # 销量大于100认为在售
                        'hot': sales_count > 1000,  # 销量大于1000认为热销

                        # 扩展字段
                        'productLink': product_link,
                        'shopLink': shop_link,
                        'shopName': login_id,
                        'location': location,
                        'province': province,
                        'city': city,
                        'platform': self.platform_code,
                        'keyword': keywords,
                        'crawlTime': datetime.now().isoformat(),
                        'searchKeyword': query,

                        # 兼容旧格式字段
                        'title': title,
                        'sales': sales_text,
                        'shop_name': login_id,
                        'link': product_link,
                        'detail_url': product_link,
                        'original_link': original_link,  # 原始跳转链接
                        'image': item_data.get('offerPicUrl', ''),
                        'image_url': item_data.get('offerPicUrl', '')
                    }

                    products.append(product)

                except Exception as e:
                    print(f"⚠️ 解析商品数据失败 (索引 {index}): {str(e)}")
                    continue

            print(f"✅ 成功解析 {len(products)} 个1688商品")
            return products

        except Exception as e:
            print(f"❌ 解析1688响应失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
            return []

    async def _get_valid_account(self, account_username: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        获取有效的1688账号信息

        Args:
            account_username: 指定使用的账号用户名，如果为None则自动选择

        Returns:
            Dict: 账号信息，包含username, cookies, user_agent等字段
        """
        try:
            # 使用与搜索功能相同的导入方式
            db = next(get_db())

            try:
                # 获取平台信息（与搜索功能保持一致）
                platform = db.query(Platform).filter(Platform.code == self.platform_code).first()
                if not platform:
                    print(f"❌ 未找到{self.platform_code}平台配置")
                    return None

                # 查询1688平台的账号（与搜索功能保持一致）
                if account_username:
                    # 如果指定了用户名，查找特定账号
                    account = db.query(CrawlerAccount).filter(
                        and_(
                            CrawlerAccount.platform_id == platform.id,
                            CrawlerAccount.username == account_username,
                            CrawlerAccount.status == 'active',
                            CrawlerAccount.is_enabled == True  # 只使用已启用的爬虫
                        )
                    ).first()
                    if not account:
                        print(f"❌ 未找到指定的1688账号: {account_username}")
                        return None
                else:
                    # 自动选择第一个可用账号（与搜索功能保持一致）
                    accounts = db.query(CrawlerAccount).filter(
                        and_(
                            CrawlerAccount.platform_id == platform.id,
                            CrawlerAccount.status == 'active',
                            CrawlerAccount.is_enabled == True  # 只使用已启用的爬虫
                        )
                    ).all()

                    # 筛选有完整配置的账号
                    account = None
                    for acc in accounts:
                        if acc.cookie and len(acc.cookie) > 100:
                            account = acc
                            print(f"✅ 找到有效1688账号: {acc.username}")
                            break

                    if not account:
                        print("❌ 没有可用的1688账号")
                        return None

                # 检查账号必要信息
                if not account.cookie:
                    print(f"❌ 账号 {account.username} 缺少cookie信息")
                    return None

                print(f"✅ 使用1688账号: {account.username}")

                return {
                    'username': account.username,
                    'cookies': account.cookie,
                    'user_agent': account.user_agent or self.default_headers['user-agent'],
                    'token': account.token
                }

            finally:
                db.close()

        except Exception as e:
            print(f"❌ 获取1688账号失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
            return None

    async def get_product_detail(self, product_url: str, account_username: Optional[str] = None) -> Dict[str, Any]:
        """
        获取1688商品详情

        Args:
            product_url: 商品详情页URL
            account_username: 指定使用的账号用户名

        Returns:
            Dict: 商品详情数据，按照上游系统格式
        """
        try:
            print(f"🔍 开始获取1688商品详情: {product_url}")

            # 获取有效账号
            account_info = await self._get_valid_account(account_username)
            if not account_info:
                return {
                    "error": "没有可用的1688账号",
                    "success": False
                }

            # 构建请求头
            headers = {
                'User-Agent': account_info.get('user_agent', self.default_headers['user-agent']),
                'Cookie': account_info['cookies'],
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            print(f"🌐 发送1688详情页请求...")

            # 发送GET请求获取商品详情页
            response = requests.get(product_url, headers=headers, timeout=30)

            print(f"📊 响应状态码: {response.status_code}")
            print(f"📏 响应内容长度: {len(response.text)} 字符")

            if response.status_code == 200:
                # 解析商品详情
                product_detail = self._parse_1688_product_detail(response.text, product_url)

                if product_detail:
                    print(f"✅ 1688商品详情获取成功: {product_detail.get('name', '')[:50]}...")

                    # 打印返回给上游的详细信息（用于调试）
                    print(f"\n📤 返回给上游的商品详情数据:")
                    print(f"   📦 商品ID: {product_detail.get('id')}")
                    print(f"   📦 商品名称: {product_detail.get('name', '')[:100]}")
                    print(f"   💰 价格: {product_detail.get('price')} 分")
                    print(f"   💰 市场价: {product_detail.get('marketPrice')} 分")
                    print(f"   📋 SKU数量: {len(product_detail.get('skus', []))}")

                    # 详细打印前几个SKU的属性信息
                    skus = product_detail.get('skus', [])
                    if skus:
                        print(f"   📋 前3个SKU的属性详情:")
                        for i, sku in enumerate(skus[:3]):
                            print(f"     SKU {i+1}:")
                            print(f"       - ID: {sku.get('id')}")
                            print(f"       - 价格: {sku.get('price')} 分")
                            print(f"       - 库存: {sku.get('stock')}")
                            properties = sku.get('properties', [])
                            print(f"       - 属性数量: {len(properties)}")
                            for j, prop in enumerate(properties):
                                print(f"         属性{j+1}: propertyId={prop.get('propertyId')}, propertyName={prop.get('propertyName')}, valueId={prop.get('valueId')}, valueName={prop.get('valueName')}")

                    return {
                        "success": True,
                        "data": product_detail,
                        "account_used": account_info.get('username'),
                        "platform": self.platform_code
                    }
                else:
                    return {
                        "error": "商品详情解析失败",
                        "success": False
                    }
            else:
                return {
                    "error": f"请求失败，状态码: {response.status_code}",
                    "success": False
                }

        except Exception as e:
            print(f"❌ 1688商品详情获取失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
            return {
                "error": f"获取商品详情失败: {str(e)}",
                "success": False
            }

    def _parse_1688_product_detail(self, html_content: str, source_url: str) -> Optional[Dict[str, Any]]:
        """
        解析1688商品详情，按照上游系统数据结构

        Args:
            html_content: HTML页面内容
            source_url: 商品URL

        Returns:
            Dict: 按照上游系统格式的商品详情数据
        """
        try:
            print("🔍 开始解析1688商品详情数据...")

            # 1. 提取初始化数据
            init_data = self._extract_init_data_from_html(html_content)
            if not init_data:
                print("❌ 无法提取页面初始化数据")
                return None

            # 保存最后的初始化数据用于调试
            self._last_init_data = init_data

            # 2. 从初始化数据中提取各种信息
            offer_id = self._extract_offer_id_from_url(source_url)
            title = self._extract_title_from_init_data(init_data)
            company_name = self._extract_company_name_from_init_data(init_data)
            images = self._extract_images_from_init_data(init_data)
            skus = self._extract_skus_from_init_data(init_data)
            sales_count = self._extract_sales_count_from_init_data(init_data)
            freight_cost = self._extract_freight_cost_from_init_data(init_data)

            # 3. 获取商品描述
            description = self._get_product_description_from_init_data(init_data)

            # 4. 计算价格范围
            price_info = self._extract_price_from_skus(skus, html_content)

            # 5. 构建上游系统格式的数据
            product_detail = {
                "id": str(offer_id) if offer_id else "",
                "name": title or "",
                "introduction": title or "",
                "description": description,
                "keyword": title or "",
                "categoryId": 0,
                "picUrl": images[0] if images else "",
                "sliderPicUrls": images[:10],
                "specType": len(skus) > 0,
                "price": price_info.get('min_price', 0),
                "marketPrice": price_info.get('max_price', 0),
                "stock": sum(sku.get('stock', 0) for sku in skus) if skus else 999,
                "type": 0,
                "freight": int(freight_cost * 100) if freight_cost else 0,
                "shopName": company_name or "",
                "source": "1688",
                "sourceLink": source_url,
                "scores": 4,
                "newest": False,
                "sale": True,
                "hot": sales_count > 1000 if sales_count else False,
                "skus": skus,
                "salesCount": sales_count or 0,
                "props": []  # 添加props字段，1688暂时返回空数组
            }

            # 打印解析结果摘要
            print(f"✅ 1688商品详情解析完成:")
            print(f"   📦 商品名称: {title}")
            print(f"   🏪 店铺名称: {company_name}")
            print(f"   💰 价格范围: {price_info.get('min_price', 0)/100:.2f} - {price_info.get('max_price', 0)/100:.2f} 元")
            print(f"   📋 SKU数量: {len(skus)}")
            print(f"   🖼️ 图片数量: {len(images)}")
            print(f"   📊 销量: {sales_count or 0}")
            print(f"   📝 描述长度: {len(description)}")

            return product_detail

        except Exception as e:
            print(f"❌ 解析1688商品详情失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
            return None

    def _extract_init_data_from_html(self, html_content: str) -> Optional[Dict]:
        """从HTML中提取初始化数据"""
        try:
            print("🔍 开始查找初始化数据...")

            # 查找初始化数据的模式 - 更全面的匹配
            patterns = [
                # 1688特有的window.context模式（最重要）- 使用精确提取方法
                'window_context_extract',

                # 常见的变量名模式
                r'window\.detailData\s*=\s*({.+?});',
                r'var\s+detailData\s*=\s*({.+?});',
                r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
                r'window\.__INITIAL_DATA__\s*=\s*({.+?});',
                r'window\.pageData\s*=\s*({.+?});',
                r'var\s+pageData\s*=\s*({.+?});',

                # 1688其他模式
                r'window\.offerDetailData\s*=\s*({.+?});',
                r'var\s+offerDetailData\s*=\s*({.+?});',
                r'window\.globalData\s*=\s*({.+?});',
                r'var\s+globalData\s*=\s*({.+?});',

                # 更宽泛的模式
                r'window\.\w+\s*=\s*(\{[^}]*"data"\s*:\s*\{[^}]*"skuModel"[^}]*\}[^}]*\});',
                r'var\s+\w+\s*=\s*(\{[^}]*"data"\s*:\s*\{[^}]*"skuModel"[^}]*\}[^}]*\});',

                # 直接查找包含关键组件的JSON
                r'(\{[^}]*"componentType"\s*:\s*"@ali/tdmod-od-pc-offer-title"[^}]*\})',
                r'(\{[^}]*"skuModel"\s*:\s*\{[^}]*"skuInfoMap"[^}]*\}[^}]*\})'
            ]

            for i, pattern in enumerate(patterns):
                if pattern == 'window_context_extract':
                    print(f"🎯 尝试模式 {i+1}: 精确提取window.context...")
                    try:
                        # 使用精确提取方法
                        data = self._extract_window_context_precise(html_content)
                        if data:
                            print(f"✅ 模式 {i+1} 精确提取成功")
                            return data
                        else:
                            print(f"❌ 模式 {i+1} 精确提取失败")
                            continue
                    except Exception as e:
                        print(f"❌ 模式 {i+1} 处理出错: {str(e)}")
                        continue
                else:
                    print(f"🔍 尝试模式 {i+1}: {pattern[:50]}...")
                    try:
                        match = re.search(pattern, html_content, re.DOTALL)
                        if match:
                            json_str = match.group(1)
                            print(f"✅ 找到匹配数据，长度: {len(json_str)} 字符")

                            try:
                                import json
                                data = json.loads(json_str)
                                print(f"✅ 成功提取初始化数据，使用模式 {i+1}")
                                print(f"📋 数据结构预览: {list(data.keys())[:5]}")
                                return data
                            except json.JSONDecodeError as e:
                                print(f"⚠️ JSON解析失败: {str(e)[:100]}...")
                                # 尝试修复常见的JSON问题
                                fixed_json = self._try_fix_json(json_str)
                                if fixed_json:
                                    try:
                                        data = json.loads(fixed_json)
                                        print(f"✅ 修复JSON后成功解析")
                                        return data
                                    except:
                                        continue
                                continue
                    except Exception as e:
                        print(f"⚠️ 模式 {i+1} 匹配失败: {str(e)}")
                        continue

            # 如果所有模式都失败，尝试查找关键字段
            print("🔍 尝试查找关键字段...")
            if self._find_key_data_in_html(html_content):
                return self._extract_data_by_keywords(html_content)

            print("❌ 未找到初始化数据")
            return None

        except Exception as e:
            print(f"❌ 提取初始化数据失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
            return None

    def _extract_window_context_precise(self, html_content: str) -> Optional[Dict]:
        """精确提取window.__INIT_DATA中的JSON数据 - 基于真实网络响应"""
        try:
            print("🔍 开始精确提取window.__INIT_DATA中的JSON数据...")

            # 首先尝试提取 window.__INIT_DATA
            init_data_pattern = r'window\.__INIT_DATA\s*=\s*'
            init_data_match = re.search(init_data_pattern, html_content)

            if init_data_match:
                print("✅ 找到window.__INIT_DATA")

                # 从匹配位置开始提取平衡的JSON
                json_start = init_data_match.end()

                # 跳过空白字符
                while json_start < len(html_content) and html_content[json_start].isspace():
                    json_start += 1

                if json_start >= len(html_content) or html_content[json_start] != '{':
                    print(f"❌ 未找到JSON开始位置，当前字符: '{html_content[json_start] if json_start < len(html_content) else 'EOF'}'")
                else:
                    # 提取平衡的JSON对象
                    json_str = self._extract_balanced_json(html_content, json_start)

                    if json_str:
                        print(f"✅ 提取JSON成功，长度: {len(json_str)} 字符")

                        try:
                            import json
                            init_data = json.loads(json_str)
                            print("✅ 成功解析window.__INIT_DATA")

                            # 检查数据结构
                            if 'data' in init_data:
                                print("✅ 找到data字段")
                                data_section = init_data['data']

                                # 查找价格组件
                                price_component_found = False
                                logistics_component_found = False

                                for component_id, component_data in data_section.items():
                                    component_type = component_data.get('componentType', '')
                                    if component_type == '@ali/tdmod-od-pc-offer-price':
                                        print(f"✅ 找到价格组件: {component_id}")
                                        price_component_found = True
                                    elif component_type == '@ali/tdmod-od-pc-offer-logistics':
                                        print(f"✅ 找到物流组件: {component_id}")
                                        logistics_component_found = True

                                if price_component_found and logistics_component_found:
                                    print("✅ 所有关键组件都已找到")
                                else:
                                    print(f"⚠️ 部分组件缺失 - 价格组件: {price_component_found}, 物流组件: {logistics_component_found}")

                            return init_data

                        except json.JSONDecodeError as e:
                            print(f"❌ 解析window.__INIT_DATA失败: {str(e)}")
                            # 尝试修复JSON
                            fixed_json = re.sub(r'(\{|,)\s*(\d+)\s*:', r'\1"\2":', json_str)
                            try:
                                init_data = json.loads(fixed_json)
                                print("✅ 修复JSON后解析成功")
                                return init_data
                            except json.JSONDecodeError:
                                print("❌ 修复后仍无法解析JSON")
                    else:
                        print("❌ 无法提取完整的JSON")
            else:
                print("❌ 未找到window.__INIT_DATA，尝试其他模式...")

            # 如果没有找到window.__INIT_DATA，尝试原来的window.contextPath方法
            print("🔍 尝试提取window.contextPath后的JSON数据...")

            # 直接查找 )(window.contextPath, { 后面的JSON数据
            # 这是函数调用的第二个参数，包含我们需要的所有数据
            context_pattern = r'\)\s*\(\s*window\.contextPath\s*,\s*'
            context_match = re.search(context_pattern, html_content)

            if not context_match:
                print("❌ 未找到window.contextPath模式")
                return None

            print("✅ 找到window.contextPath位置")

            # 从匹配位置开始提取平衡的JSON
            json_start = context_match.end()

            # 跳过空白字符
            while json_start < len(html_content) and html_content[json_start].isspace():
                json_start += 1

            if json_start >= len(html_content) or html_content[json_start] != '{':
                print(f"❌ 未找到JSON开始位置，当前字符: '{html_content[json_start] if json_start < len(html_content) else 'EOF'}'")
                return None

            print("✅ 找到JSON开始位置")

            # 提取平衡的JSON对象
            json_str = self._extract_balanced_json(html_content, json_start)

            if not json_str:
                print("❌ 无法提取完整的JSON")
                return None

            print(f"✅ 提取JSON成功，长度: {len(json_str)} 字符")

            try:
                import json
                context_data = json.loads(json_str)
                print("✅ 成功解析JSON数据")

                # 检查数据结构
                if 'result' in context_data:
                    print("✅ 找到result字段")
                    if 'data' in context_data.get('result', {}):
                        print("✅ 找到data字段")
                        data_section = context_data['result']['data']
                        if 'Root' in data_section:
                            print("✅ 找到Root组件")
                            root_fields = data_section['Root'].get('fields', {})
                            if 'dataJson' in root_fields:
                                print("✅ 找到dataJson字段")
                                data_json = root_fields['dataJson']
                                if 'skuModel' in data_json:
                                    print("✅ 找到skuModel字段")
                                if 'tempModel' in data_json:
                                    print("✅ 找到tempModel字段")
                            return context_data

                # 如果没有找到预期结构，仍然返回数据
                print("⚠️ 数据结构与预期不同，但仍返回解析结果")
                return context_data

            except json.JSONDecodeError as e:
                print(f"❌ 解析JSON失败: {str(e)}")

                # 尝试修复常见的JSON问题
                print("🔧 尝试修复JSON格式...")

                # 修复数字键没有引号的问题
                fixed_json = re.sub(r'(\{|,)\s*(\d+)\s*:', r'\1"\2":', json_str)

                try:
                    context_data = json.loads(fixed_json)
                    print("✅ 修复JSON后解析成功")
                    return context_data
                except json.JSONDecodeError:
                    print("❌ 修复后仍无法解析JSON")
                    return None

        except Exception as e:
            print(f"❌ 精确提取JSON数据失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
            return None

    def _extract_balanced_json(self, text: str, start_pos: int) -> Optional[str]:
        """从指定位置开始提取平衡的JSON对象"""
        if text[start_pos] != '{':
            return None

        bracket_count = 0
        in_string = False
        escape_next = False

        for i in range(start_pos, len(text)):
            char = text[i]

            if escape_next:
                escape_next = False
                continue

            if char == '\\':
                escape_next = True
                continue

            if char == '"' and not escape_next:
                in_string = not in_string
                continue

            if not in_string:
                if char == '{':
                    bracket_count += 1
                elif char == '}':
                    bracket_count -= 1
                    if bracket_count == 0:
                        return text[start_pos:i+1]

        return None

    def _try_fix_json(self, json_str: str) -> Optional[str]:
        """尝试修复常见的JSON格式问题"""
        try:
            # 移除末尾的分号
            json_str = json_str.rstrip(';')

            # 移除JavaScript注释
            json_str = re.sub(r'//.*?\n', '\n', json_str)
            json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)

            # 修复未引用的键名
            json_str = re.sub(r'(\w+):', r'"\1":', json_str)

            # 修复单引号
            json_str = json_str.replace("'", '"')

            return json_str
        except Exception as e:
            print(f"⚠️ JSON修复失败: {str(e)}")
            return None

    def _find_key_data_in_html(self, html_content: str) -> bool:
        """检查HTML中是否包含关键数据"""
        key_indicators = [
            'skuModel',
            'skuInfoMap',
            '@ali/tdmod-od-pc-offer-title',
            '@ali/tdmod-od-pc-offer-description',
            'offerImgList',
            'componentType'
        ]

        found_count = 0
        for indicator in key_indicators:
            if indicator in html_content:
                found_count += 1
                print(f"✅ 找到关键字段: {indicator}")

        print(f"📊 找到 {found_count}/{len(key_indicators)} 个关键字段")
        return found_count >= 3

    def _extract_data_by_keywords(self, html_content: str) -> Optional[Dict]:
        """通过关键字段提取数据"""
        try:
            print("🔍 尝试通过关键字段提取数据...")

            # 构建基本数据结构
            extracted_data = {
                "data": {}
            }

            # 提取SKU数据
            sku_data = self._extract_sku_data_by_regex(html_content)
            if sku_data:
                extracted_data["data"]["skuModel"] = sku_data

            # 提取标题数据
            title_data = self._extract_title_data_by_regex(html_content)
            if title_data:
                extracted_data["data"]["title_component"] = {
                    "componentType": "@ali/tdmod-od-pc-offer-title",
                    "data": title_data
                }

            # 提取图片数据
            image_data = self._extract_image_data_by_regex(html_content)
            if image_data:
                extracted_data["data"]["image_component"] = {
                    "componentType": "@ali/tdmod-pc-od-main-pic",
                    "data": image_data
                }

            # 提取描述URL
            desc_url = self._extract_description_url_by_regex(html_content)
            if desc_url:
                extracted_data["data"]["desc_component"] = {
                    "componentType": "@ali/tdmod-od-pc-offer-description",
                    "data": {"detailUrl": desc_url}
                }

            # 提取基本信息
            base_info = self._extract_base_info_by_regex(html_content)
            if base_info:
                extracted_data["data"]["offerBaseInfo"] = base_info

            if extracted_data["data"]:
                print(f"✅ 通过关键字段成功提取数据")
                return extracted_data

            return None

        except Exception as e:
            print(f"❌ 关键字段提取失败: {str(e)}")
            return None

    def _extract_sku_data_by_regex(self, html_content: str) -> Optional[Dict]:
        """通过正则表达式提取SKU数据"""
        try:
            # 查找skuInfoMap
            sku_info_patterns = [
                r'"skuInfoMap"\s*:\s*(\{[^}]+\})',
                r'"skuInfoMap"\s*:\s*(\{.*?\})',
                r'skuInfoMap["\']?\s*:\s*(\{.*?\})'
            ]

            sku_info_map = {}
            for pattern in sku_info_patterns:
                match = re.search(pattern, html_content, re.DOTALL)
                if match:
                    try:
                        import json
                        sku_info_map = json.loads(match.group(1))
                        print(f"✅ 提取到SKU信息映射: {len(sku_info_map)} 个")
                        break
                    except:
                        continue

            # 查找skuProps
            sku_props = []
            props_patterns = [
                r'"skuProps"\s*:\s*(\[.*?\])',
                r'skuProps["\']?\s*:\s*(\[.*?\])'
            ]

            for pattern in props_patterns:
                match = re.search(pattern, html_content, re.DOTALL)
                if match:
                    try:
                        import json
                        sku_props = json.loads(match.group(1))
                        print(f"✅ 提取到SKU属性: {len(sku_props)} 个")
                        break
                    except:
                        continue

            if sku_info_map or sku_props:
                return {
                    "skuInfoMap": sku_info_map,
                    "skuProps": sku_props
                }

            return None

        except Exception as e:
            print(f"❌ 正则提取SKU数据失败: {str(e)}")
            return None

    def _extract_title_data_by_regex(self, html_content: str) -> Optional[Dict]:
        """通过正则表达式提取标题数据"""
        try:
            title_patterns = [
                r'"title"\s*:\s*"([^"]+)"',
                r'"subject"\s*:\s*"([^"]+)"',
                r'"offerTitle"\s*:\s*"([^"]+)"',
                r'<title[^>]*>([^<]+)</title>'
            ]

            for pattern in title_patterns:
                match = re.search(pattern, html_content)
                if match:
                    title = match.group(1).strip()
                    if title and len(title) > 5:  # 确保是有效标题
                        print(f"✅ 提取到商品标题: {title[:50]}...")
                        return {"title": title}

            return None

        except Exception as e:
            print(f"❌ 正则提取标题失败: {str(e)}")
            return None

    def _extract_image_data_by_regex(self, html_content: str) -> Optional[Dict]:
        """通过正则表达式提取图片数据"""
        try:
            # 查找offerImgList
            img_patterns = [
                r'"offerImgList"\s*:\s*(\[.*?\])',
                r'offerImgList["\']?\s*:\s*(\[.*?\])'
            ]

            for pattern in img_patterns:
                match = re.search(pattern, html_content, re.DOTALL)
                if match:
                    try:
                        import json
                        img_list = json.loads(match.group(1))
                        if img_list:
                            print(f"✅ 提取到商品图片: {len(img_list)} 张")
                            return {"offerImgList": img_list}
                    except:
                        continue

            # 备选方案：直接搜索图片URL
            img_urls = re.findall(r'"(https://cbu01\.alicdn\.com/img/ibank/[^"]*\.jpg)"', html_content)
            if img_urls:
                unique_imgs = list(dict.fromkeys(img_urls))  # 去重
                print(f"✅ 通过URL模式提取到图片: {len(unique_imgs)} 张")
                return {"offerImgList": unique_imgs}

            return None

        except Exception as e:
            print(f"❌ 正则提取图片失败: {str(e)}")
            return None

    def _extract_description_url_by_regex(self, html_content: str) -> Optional[str]:
        """通过正则表达式提取描述URL"""
        try:
            desc_patterns = [
                r'"detailUrl"\s*:\s*"([^"]+)"',
                r'detailUrl["\']?\s*:\s*["\']([^"\']+)["\']'
            ]

            for pattern in desc_patterns:
                match = re.search(pattern, html_content)
                if match:
                    url = match.group(1)
                    if 'itemcdn.tmall.com' in url or '1688offer' in url:
                        print(f"✅ 提取到描述URL: {url}")
                        return url

            return None

        except Exception as e:
            print(f"❌ 正则提取描述URL失败: {str(e)}")
            return None

    def _extract_base_info_by_regex(self, html_content: str) -> Optional[Dict]:
        """通过正则表达式提取基本信息"""
        try:
            base_info = {}

            # 提取offerId
            offer_id_patterns = [
                r'"offerId"\s*:\s*(\d+)',
                r'offerId["\']?\s*:\s*(\d+)',
                r'offer/(\d+)\.html'
            ]

            for pattern in offer_id_patterns:
                match = re.search(pattern, html_content)
                if match:
                    base_info["offerId"] = int(match.group(1))
                    break

            # 提取卖家信息
            seller_patterns = [
                r'"sellerLoginId"\s*:\s*"([^"]+)"',
                r'"companyName"\s*:\s*"([^"]+)"',
                r'sellerLoginId["\']?\s*:\s*["\']([^"\']+)["\']'
            ]

            for pattern in seller_patterns:
                match = re.search(pattern, html_content)
                if match:
                    base_info["sellerLoginId"] = match.group(1)
                    break

            return base_info if base_info else None

        except Exception as e:
            print(f"❌ 正则提取基本信息失败: {str(e)}")
            return None

    def _extract_offer_id_from_url(self, url: str) -> Optional[str]:
        """从URL中提取商品ID"""
        try:
            import re
            match = re.search(r'offer/(\d+)\.html', url)
            if match:
                offer_id = match.group(1)
                print(f"✅ 提取商品ID: {offer_id}")
                return offer_id

            print("⚠️ 未找到商品ID")
            return None

        except Exception as e:
            print(f"❌ 提取商品ID失败: {str(e)}")
            return None

    def _extract_title_from_init_data(self, init_data: Dict) -> str:
        """从初始化数据中提取商品标题"""
        try:
            # 方法1: 从result.global.globalData.model.tempModel中提取（新的数据结构）
            global_data = init_data.get('result', {}).get('global', {}).get('globalData', {}).get('model', {})
            temp_model = global_data.get('tempModel', {})

            title = temp_model.get('offerTitle', '')
            if title:
                print(f"✅ 提取商品标题(globalData.tempModel): {title}")
                return title

            # 方法2: 从result.data中查找标题组件
            result_data = init_data.get('result', {}).get('data', {})
            for component_key, component in result_data.items():
                if isinstance(component, dict):
                    fields = component.get('fields', {})
                    if 'subject' in fields:
                        title = fields['subject']
                        if title:
                            print(f"✅ 提取商品标题(组件subject): {title}")
                            return title

            # 方法2: 查找@ali/tdmod-od-pc-offer-title组件
            data_section = init_data.get('data', {})
            for component in data_section.values():
                if isinstance(component, dict):
                    if component.get('componentType') == '@ali/tdmod-od-pc-offer-title':
                        component_data = component.get('data', {})
                        title = component_data.get('title', '') or component_data.get('offerTitle', '')
                        if title:
                            print(f"✅ 提取商品标题(组件): {title}")
                            return title

            # 方法3: 查找包含title字段的组件
            for component_key, component in data_section.items():
                if isinstance(component, dict):
                    component_data = component.get('data', {})
                    if isinstance(component_data, dict) and 'title' in component_data:
                        title = component_data['title']
                        if title and len(title) > 10:  # 确保是商品标题而不是页面标题
                            print(f"✅ 提取商品标题(数据): {title}")
                            return title

            # 方法4: 从pageInfo中查找
            page_info = init_data.get('pageInfo', {})
            if 'title' in page_info:
                title = page_info['title']
                if title and len(title) > 10:  # 确保是商品标题
                    print(f"✅ 提取商品标题(页面): {title}")
                    return title

            # 方法5: 检查是否有通过正则提取的标题组件
            if 'title_component' in data_section:
                title = data_section['title_component'].get('data', {}).get('title', '')
                if title:
                    print(f"✅ 提取商品标题(正则): {title}")
                    return title

            print("⚠️ 未找到商品标题")
            return ""

        except Exception as e:
            print(f"❌ 提取商品标题失败: {str(e)}")
            return ""

    def _extract_company_name_from_init_data(self, init_data: Dict) -> str:
        """从初始化数据中提取公司名称"""
        try:
            # 方法1: 从result.global.globalData.model.sellerModel中提取（新的数据结构）
            global_data = init_data.get('result', {}).get('global', {}).get('globalData', {}).get('model', {})
            seller_model = global_data.get('sellerModel', {})

            company_name = seller_model.get('companyName', '') or seller_model.get('sellerLoginId', '')
            if company_name:
                print(f"✅ 提取公司名称(sellerModel): {company_name}")
                return company_name

            # 方法2: 从tempModel中提取
            temp_model = global_data.get('tempModel', {})
            company_name = temp_model.get('companyName', '') or temp_model.get('sellerLoginId', '')
            if company_name:
                print(f"✅ 提取公司名称(tempModel): {company_name}")
                return company_name

            # 方法2: 从offerBaseInfo中提取
            offer_base_info = init_data.get('data', {}).get('offerBaseInfo', {})
            company_name = offer_base_info.get('sellerLoginId', '') or offer_base_info.get('companyName', '')

            if company_name:
                print(f"✅ 提取公司名称(offerBaseInfo): {company_name}")
                return company_name

            print("⚠️ 未找到公司名称")
            return ""

        except Exception as e:
            print(f"❌ 提取公司名称失败: {str(e)}")
            return ""

    def _extract_images_from_init_data(self, init_data: Dict) -> List[str]:
        """从初始化数据中提取商品图片"""
        try:
            images = []

            # 方法1: 从result.data.Root.fields.dataJson.images中提取（根据实际数据结构）
            result_section = init_data.get('result', {})
            data_section = result_section.get('data', {})
            root_component = data_section.get('Root', {})
            if root_component:
                fields = root_component.get('fields', {})
                data_json = fields.get('dataJson', {})

                # 尝试从多个可能的图片字段提取
                for img_field in ['images', 'offerImgList', 'imageList', 'picList']:
                    img_data = data_json.get(img_field, [])
                    if img_data:
                        print(f"✅ 在dataJson.{img_field}中找到图片数据: {len(img_data)} 张")
                        for img_info in img_data:
                            if isinstance(img_info, str):
                                images.append(img_info)
                            elif isinstance(img_info, dict):
                                img_url = (img_info.get('fullPathImageURI') or
                                          img_info.get('imageURI') or
                                          img_info.get('url') or
                                          img_info.get('src'))
                                if img_url:
                                    if not img_url.startswith('http'):
                                        img_url = f"https://cbu01.alicdn.com/{img_url}"
                                    images.append(img_url)
                        break

            # 方法2: 从result.global.globalData.model.images中提取（备用）
            if not images:
                global_data = init_data.get('result', {}).get('global', {}).get('globalData', {}).get('model', {})
                global_images = global_data.get('images', [])

                if global_images:
                    # 提取fullPathImageURI字段
                    for img_info in global_images:
                        if isinstance(img_info, dict):
                            img_url = img_info.get('fullPathImageURI', '')
                            if not img_url and img_info.get('imageURI'):
                                img_url = f"https://cbu01.alicdn.com/{img_info['imageURI']}"
                            if img_url:
                                images.append(img_url)

                    if images:
                        print(f"✅ 从globalData.model提取商品图片: {len(images)} 张")

            # 方法3: 查找主图组件
            if not images:
                for component in data_section.values():
                    if isinstance(component, dict):
                        if component.get('componentType') == '@ali/tdmod-pc-od-main-pic':
                            offer_img_list = component.get('data', {}).get('offerImgList', [])
                            images.extend(offer_img_list)
                            if images:
                                print(f"✅ 从主图组件提取图片: {len(images)} 张")
                            break

            # 方法4: 检查是否有通过正则提取的图片组件
            if not images and 'image_component' in data_section:
                offer_img_list = data_section['image_component'].get('data', {}).get('offerImgList', [])
                images.extend(offer_img_list)
                if images:
                    print(f"✅ 从正则提取的图片组件获取: {len(images)} 张")

            # 方法5: 如果还没有图片，尝试从其他地方提取
            if not images:
                for component in data_section.values():
                    if isinstance(component, dict) and 'offerImgList' in str(component):
                        img_data = component.get('data', {})
                        if 'offerImgList' in img_data:
                            images.extend(img_data['offerImgList'])
                            if images:
                                print(f"✅ 从其他组件提取图片: {len(images)} 张")
                            break

            # 去重并限制数量
            unique_images = []
            seen = set()
            for img in images:
                if img and img not in seen:
                    unique_images.append(img)
                    seen.add(img)

            print(f"✅ 最终提取商品图片: {len(unique_images)} 张")
            return unique_images[:20]  # 限制图片数量

        except Exception as e:
            print(f"❌ 提取商品图片失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
            return []

    def _extract_skus_from_init_data(self, init_data: Dict) -> List[Dict[str, Any]]:
        """从初始化数据中提取SKU信息 - 根据新的数据结构分析"""
        try:
            skus = []

            # 第一步：从data中查找SKU模型数据（支持新旧两种数据结构）
            # 尝试新结构：直接从data获取
            data_section = init_data.get('data', {})
            # 尝试旧结构：从result.data获取
            if not data_section:
                data_section = init_data.get('result', {}).get('data', {})

            sku_model = None
            print(f"🔍 开始查找skuModel，数据结构包含 {len(data_section)} 个组件")

            # 方法1: 查找包含skuModel的组件的data字段
            for component_key, component in data_section.items():
                if isinstance(component, dict) and 'data' in component:
                    component_data = component['data']
                    if isinstance(component_data, dict) and 'skuModel' in component_data:
                        sku_model = component_data['skuModel']
                        print(f"✅ 在组件 {component_key}.data 中找到skuModel")
                        break

            # 方法2: 查找Root组件的fields.dataJson中的skuModel（新发现的位置）
            if not sku_model:
                root_component = data_section.get('Root', {})
                if isinstance(root_component, dict):
                    fields = root_component.get('fields', {})
                    if isinstance(fields, dict):
                        data_json = fields.get('dataJson', {})
                        if isinstance(data_json, dict) and 'skuModel' in data_json:
                            sku_model = data_json['skuModel']
                            print(f"✅ 在Root.fields.dataJson中找到skuModel")

            # 方法3: 查找任何组件的fields.dataJson中的skuModel
            if not sku_model:
                for component_key, component in data_section.items():
                    if isinstance(component, dict) and 'fields' in component:
                        fields = component['fields']
                        if isinstance(fields, dict) and 'dataJson' in fields:
                            data_json = fields['dataJson']
                            if isinstance(data_json, dict) and 'skuModel' in data_json:
                                sku_model = data_json['skuModel']
                                print(f"✅ 在组件 {component_key}.fields.dataJson 中找到skuModel")
                                break

            # 方法4: 直接从根级别查找skuModel（新结构）
            if not sku_model:
                # 检查是否直接在根级别有skuModel
                root_sku_model = init_data.get('skuModel')
                if root_sku_model:
                    sku_model = root_sku_model
                    print("✅ 在根级别找到skuModel")
                else:
                    # 检查data根级别
                    sku_model = data_section.get('skuModel', {})
                    if sku_model:
                        print("✅ 在data根级别找到skuModel")

            # 方法5: 查找任何包含skuModel的位置（最广泛的搜索）
            if not sku_model:
                print("🔍 进行深度搜索skuModel...")
                def find_sku_model_recursive(obj, path=""):
                    if isinstance(obj, dict):
                        if 'skuModel' in obj:
                            return obj['skuModel'], path
                        for key, value in obj.items():
                            result, found_path = find_sku_model_recursive(value, f"{path}.{key}" if path else key)
                            if result:
                                return result, found_path
                    elif isinstance(obj, list):
                        for i, item in enumerate(obj):
                            result, found_path = find_sku_model_recursive(item, f"{path}[{i}]" if path else f"[{i}]")
                            if result:
                                return result, found_path
                    return None, ""

                sku_model, found_path = find_sku_model_recursive(init_data)
                if sku_model:
                    print(f"✅ 在 {found_path} 找到skuModel")

            if not sku_model:
                print("⚠️ 未找到skuModel数据")
                # 尝试查找globalData.skuModel（备用方案）
                global_data = init_data.get('result', {}).get('global', {}).get('globalData', {})
                sku_model = global_data.get('skuModel', {})
                if sku_model:
                    print("✅ 在globalData中找到skuModel（备用方案）")
                else:
                    # 保存原始数据用于调试
                    import json
                    debug_file = f"temp/debug_sku_data_{init_data.get('result', {}).get('data', {}).get('Root', {}).get('fields', {}).get('dataJson', {}).get('tempModel', {}).get('offerId', 'unknown')}.json"
                    try:
                        with open(debug_file, 'w', encoding='utf-8') as f:
                            json.dump(init_data, f, ensure_ascii=False, indent=2)
                        print(f"🔍 已保存调试数据到: {debug_file}")
                    except Exception as e:
                        print(f"⚠️ 保存调试数据失败: {str(e)}")
                    return []

            sku_props = sku_model.get('skuProps', [])
            sku_info_map = sku_model.get('skuInfoMap', {})

            print(f"📋 找到 {len(sku_info_map)} 个SKU组合")
            print(f"📋 找到 {len(sku_props)} 个SKU属性组")

            if not sku_info_map:
                print("⚠️ SKU信息映射为空")
                return []

            # 第二步：获取价格信息
            price_data = self._extract_price_data_from_init_data(init_data)

            # 第三步：获取重量信息
            weight_data = self._extract_weight_data_from_init_data(init_data)

            # 第四步：构建SKU列表
            sku_id = 1
            for sku_name, sku_info in sku_info_map.items():
                try:
                    # 解析SKU属性
                    properties = self._parse_sku_properties_from_name(sku_name, sku_props)

                    # 获取SKU图片
                    pic_url = self._get_sku_image_from_props(sku_name, sku_props)

                    # 获取SKU价格
                    sku_id_value = sku_info.get('skuId')
                    price_info = self._get_sku_price(sku_id_value, price_data)

                    # 获取SKU重量
                    weight_grams = self._get_sku_weight(sku_id_value, weight_data)

                    # 构建SKU对象
                    sku = {
                        'id': sku_id,
                        'skuId': sku_id_value,  # 1688的真实SKU ID
                        'price': price_info['price'],  # 价格（分）
                        'marketPrice': price_info['marketPrice'],  # 市场价（分）
                        'stock': int(sku_info.get('canBookCount', 0)),
                        'picUrl': pic_url,
                        'properties': properties,
                        'weight': weight_grams  # 重量（克）
                    }

                    skus.append(sku)
                    sku_id += 1

                except Exception as e:
                    print(f"⚠️ 解析SKU失败: {sku_name} - {str(e)}")
                    continue

            print(f"✅ 成功解析 {len(skus)} 个SKU")
            return skus

        except Exception as e:
            print(f"❌ 提取SKU信息失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
            return []

    def _search_component_recursive(self, data: Any, target_component_type: str, path: str = "", max_depth: int = 3, current_depth: int = 0) -> Optional[Dict]:
        """递归搜索指定类型的组件"""
        if current_depth > max_depth:
            return None

        if isinstance(data, dict):
            # 检查当前字典是否是目标组件
            component_type = data.get('componentType')
            if component_type == target_component_type:
                print(f"    ✅ 在{path}中找到组件: {target_component_type}")
                return data

            # 递归搜索子字典
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    result = self._search_component_recursive(value, target_component_type, f"{path}.{key}", max_depth, current_depth + 1)
                    if result:
                        return result

        elif isinstance(data, list):
            # 递归搜索列表中的元素
            for i, item in enumerate(data):
                if isinstance(item, (dict, list)):
                    result = self._search_component_recursive(item, target_component_type, f"{path}[{i}]", max_depth, current_depth + 1)
                    if result:
                        return result

        return None

    def _extract_price_data_from_init_data(self, init_data: Dict) -> Dict[str, Any]:
        """从初始化数据中提取价格信息"""
        try:
            # 尝试两种数据结构路径
            # 路径1: result.data (旧格式)
            result_data = init_data.get('result', {}).get('data', {})
            # 路径2: data (新格式)
            direct_data = init_data.get('data', {})

            # 选择有数据的路径
            print(f"🔍 调试路径选择:")
            print(f"   - init_data顶层键: {list(init_data.keys())}")
            print(f"   - direct_data长度: {len(direct_data)}")
            print(f"   - result_data长度: {len(result_data)}")

            if len(direct_data) > 0:
                search_data = direct_data
                data_path = 'data'
            elif len(result_data) > 0:
                search_data = result_data
                data_path = 'result.data'
            else:
                search_data = {}
                data_path = 'none'

            print(f"🔍 开始查找价格组件，共有 {len(search_data)} 个组件")
            print(f"🔍 使用数据路径: {data_path}")

            for component_key, component in search_data.items():
                if isinstance(component, dict):
                    component_type = component.get('componentType')
                    print(f"  - 组件 {component_key}: {component_type}")

                    # 如果没有直接的componentType，递归搜索fields中的数据
                    if component_type is None:
                        found_component = self._search_component_recursive(component, '@ali/tdmod-od-pc-offer-price', component_key)
                        if found_component:
                            component_type = '@ali/tdmod-od-pc-offer-price'
                            component = found_component

                    if component_type == '@ali/tdmod-od-pc-offer-price':
                        print(f"✅ 找到价格组件: {component_key}")

                        # 获取价格数据
                        component_data = component.get('data', {})

                        # 尝试多种价格数据路径
                        final_price_model = component_data.get('finalPriceModel', {})
                        trade_without_promotion = final_price_model.get('tradeWithoutPromotion', {})
                        price_model = component_data.get('priceModel', {})

                        # 如果没有finalPriceModel，尝试直接从priceModel获取
                        if not final_price_model and price_model:
                            current_prices = price_model.get('currentPrices', [])
                            print(f"✅ 从priceModel获取价格数据: {len(current_prices)} 个价格")

                            return {
                                'skuMapOriginal': [],
                                'offerPriceRanges': current_prices,
                                'priceModel': price_model,
                                'originalPricesWithoutPromotion': component_data.get('originalPricesWithoutPromotion', [])
                            }

                        return {
                            'skuMapOriginal': trade_without_promotion.get('skuMapOriginal', []),
                            'offerPriceRanges': trade_without_promotion.get('offerPriceRanges', []),
                            'offerBeginAmount': trade_without_promotion.get('offerBeginAmount', 1),
                            'priceModel': price_model,
                            'originalPricesWithoutPromotion': component_data.get('originalPricesWithoutPromotion', [])
                        }

            print("⚠️ 未找到价格组件")
            return {}

        except Exception as e:
            print(f"❌ 提取价格数据失败: {str(e)}")
            return {}

    def _extract_weight_data_from_init_data(self, init_data: Dict) -> Dict[str, Any]:
        """从初始化数据中提取重量和物流信息"""
        try:
            # 尝试两种数据结构路径
            # 路径1: result.data (旧格式)
            result_data = init_data.get('result', {}).get('data', {})
            # 路径2: data (新格式)
            direct_data = init_data.get('data', {})

            # 选择有数据的路径
            if len(direct_data) > 0:
                search_data = direct_data
                data_path = 'data'
            elif len(result_data) > 0:
                search_data = result_data
                data_path = 'result.data'
            else:
                search_data = {}
                data_path = 'none'

            print(f"🔍 开始查找物流组件，共有 {len(search_data)} 个组件")
            print(f"🔍 使用数据路径: {data_path}")

            for component_key, component in search_data.items():
                if isinstance(component, dict):
                    component_type = component.get('componentType')
                    print(f"  - 组件 {component_key}: {component_type}")

                    # 如果没有直接的componentType，递归搜索fields中的数据
                    if component_type is None:
                        found_component = self._search_component_recursive(component, '@ali/tdmod-od-pc-offer-logistics', component_key)
                        if found_component:
                            component_type = '@ali/tdmod-od-pc-offer-logistics'
                            component = found_component

                    if component_type == '@ali/tdmod-od-pc-offer-logistics':
                        print(f"✅ 找到物流组件: {component_key}")

                        # 获取物流数据
                        component_data = component.get('data', {})

                        return {
                            'totalCost': component_data.get('totalCost', 0),  # 运费（元）
                            'unitWeight': component_data.get('unitWeight', 0),  # 统一重量（千克）
                            'skuWeight': component_data.get('skuWeight', {}),  # SKU重量映射（千克）
                            'minWeight': component_data.get('minWeight', 0),  # 最小重量
                            'freightInfo': component_data.get('freightInfo', {})  # 运费信息
                        }

            print("⚠️ 未找到物流组件")
            return {}

        except Exception as e:
            print(f"❌ 提取重量数据失败: {str(e)}")
            return {}

    def _get_sku_price(self, sku_id: int, price_data: Dict[str, Any]) -> Dict[str, int]:
        """获取SKU价格信息（返回分为单位）"""
        try:
            sku_map_original = price_data.get('skuMapOriginal', [])
            offer_price_ranges = price_data.get('offerPriceRanges', [])

            # 方法1：查找SKU独立价格
            for sku_item in sku_map_original:
                if sku_item.get('skuId') == sku_id:
                    price = sku_item.get('price')
                    discount_price = sku_item.get('discountPrice')

                    if price and discount_price:
                        # 有独立价格，取较低的价格
                        final_price = min(float(price), float(discount_price))
                        return {
                            'price': int(final_price * 100),  # 转换为分
                            'marketPrice': int(float(price) * 100)
                        }

            # 方法2：使用统一价格范围
            if offer_price_ranges:
                # 取最低价格作为默认价格
                min_price_range = min(offer_price_ranges, key=lambda x: float(x.get('price', 0)))
                price = float(min_price_range.get('price', 0))
                discount_price = float(min_price_range.get('discountPrice', price))

                final_price = min(price, discount_price)
                return {
                    'price': int(final_price * 100),  # 转换为分
                    'marketPrice': int(price * 100)
                }

            # 默认价格
            return {'price': 0, 'marketPrice': 0}

        except Exception as e:
            print(f"⚠️ 获取SKU价格失败: {str(e)}")
            return {'price': 0, 'marketPrice': 0}

    def _get_sku_weight(self, sku_id: int, weight_data: Dict[str, Any]) -> int:
        """获取SKU重量信息（返回克为单位）"""
        try:
            sku_weight_map = weight_data.get('skuWeight', {})
            unit_weight = weight_data.get('unitWeight', 0)

            # 方法1：查找SKU独立重量
            if sku_weight_map and str(sku_id) in sku_weight_map:
                weight_kg = float(sku_weight_map[str(sku_id)])
                return int(weight_kg * 1000)  # 转换为克

            # 方法2：使用统一重量
            if unit_weight > 0:
                return int(float(unit_weight) * 1000)  # 转换为克

            # 默认重量
            return 0

        except Exception as e:
            print(f"⚠️ 获取SKU重量失败: {str(e)}")
            return 0

    def _parse_sku_properties_from_name(self, sku_name: str, sku_props: List[Dict]) -> List[Dict[str, Any]]:
        """从SKU名称解析属性信息 - 根据1688数据结构优化"""
        try:
            properties = []

            # 1688使用的分隔符（根据分析，主要是>和&gt;）
            possible_separators = ['&gt;', '>', ';', ',', '-', '|', '_', ' ']

            # 尝试不同的分隔符
            for separator in possible_separators:
                if separator in sku_name:
                    parts = sku_name.split(separator)
                    parts = [part.strip() for part in parts if part.strip()]

                    if len(parts) == len(sku_props):
                        # 找到正确的分隔符，按顺序匹配属性
                        for i, part in enumerate(parts):
                            if i < len(sku_props):
                                prop_info = sku_props[i]

                                # 查找对应的值ID
                                value_id = None
                                for value in prop_info.get('value', []):
                                    if value.get('name') == part:
                                        value_id = value.get('vid')
                                        # 如果没有vid，基于名称生成一个ID
                                        if value_id is None:
                                            value_id = abs(hash(part)) % 100000
                                        break

                                # 如果没有找到匹配的值，生成一个默认ID
                                if value_id is None:
                                    value_id = abs(hash(part)) % 100000

                                properties.append({
                                    'propertyId': prop_info.get('fid'),
                                    'propertyName': prop_info.get('prop'),  # 添加propertyName字段
                                    'valueId': value_id,
                                    'valueName': part,  # 添加valueName字段
                                    'name': prop_info.get('prop'),  # 保留原有字段
                                    'value': part  # 保留原有字段
                                })

                        print(f"✅ 使用分隔符 '{separator}' 成功解析SKU属性: {sku_name} -> {len(properties)}个属性")
                        return properties

            # 如果没有找到合适的分隔符，尝试作为单一属性处理
            if len(sku_props) == 1:
                prop_info = sku_props[0]

                # 查找对应的值ID
                value_id = None
                for value in prop_info.get('value', []):
                    if value.get('name') == sku_name:
                        value_id = value.get('vid')
                        # 如果没有vid，基于名称生成一个ID
                        if value_id is None:
                            value_id = abs(hash(sku_name)) % 100000
                        break

                # 如果没有找到匹配的值，生成一个默认ID
                if value_id is None:
                    value_id = abs(hash(sku_name)) % 100000

                properties.append({
                    'propertyId': prop_info.get('fid'),
                    'propertyName': prop_info.get('prop'),  # 添加propertyName字段
                    'valueId': value_id,
                    'valueName': sku_name,  # 添加valueName字段
                    'name': prop_info.get('prop'),  # 保留原有字段
                    'value': sku_name  # 保留原有字段
                })

                print(f"✅ 作为单一属性处理: {sku_name}")
                return properties

            print(f"⚠️ 无法解析SKU属性: {sku_name} (期望{len(sku_props)}个属性)")
            return []

        except Exception as e:
            print(f"❌ 解析SKU属性失败: {sku_name} - {str(e)}")
            return []

    def _infer_property_type(self, value: str) -> Dict[str, Any]:
        """根据值推断属性类型"""
        try:
            value_lower = value.lower()

            # 尺寸相关关键词
            size_keywords = ['xs', 's', 'm', 'l', 'xl', 'xxl', '小', '中', '大', '特大',
                           '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40',
                           'cm', 'mm', '寸', '码']

            # 颜色相关关键词
            color_keywords = ['红', '蓝', '绿', '黄', '黑', '白', '灰', '紫', '粉', '橙', '棕', '银', '金',
                            'red', 'blue', 'green', 'yellow', 'black', 'white', 'gray', 'purple',
                            'pink', 'orange', 'brown', 'silver', 'gold']

            # 材质相关关键词
            material_keywords = ['棉', '丝', '毛', '麻', '皮', '金属', '塑料', '木', '竹', '玻璃',
                               'cotton', 'silk', 'wool', 'leather', 'metal', 'plastic', 'wood']

            # 判断属性类型
            if any(keyword in value_lower for keyword in size_keywords):
                return {
                    'propertyId': 20509,  # 尺寸属性ID
                    'propertyName': '尺寸',
                    'valueId': abs(hash(value)) % 10000,
                    'valueName': value
                }
            elif any(keyword in value_lower for keyword in color_keywords):
                return {
                    'propertyId': 3216,  # 颜色属性ID
                    'propertyName': '颜色',
                    'valueId': abs(hash(value)) % 10000,
                    'valueName': value
                }
            elif any(keyword in value_lower for keyword in material_keywords):
                return {
                    'propertyId': 122216,  # 材质属性ID
                    'propertyName': '材质',
                    'valueId': abs(hash(value)) % 10000,
                    'valueName': value
                }
            else:
                # 默认作为规格
                return {
                    'propertyId': 1627207,  # 规格属性ID
                    'propertyName': '规格',
                    'valueId': abs(hash(value)) % 10000,
                    'valueName': value
                }

        except Exception as e:
            print(f"⚠️ 推断属性类型失败: {value} - {str(e)}")
            return None

    def _get_sku_image_from_props(self, sku_name: str, sku_props: List[Dict]) -> str:
        """获取SKU对应的图片"""
        try:
            for prop_def in sku_props:
                prop_values = prop_def.get('value', [])
                for value_info in prop_values:
                    if value_info.get('name') == sku_name:
                        return value_info.get('imageUrl', '')
            return ""
        except Exception as e:
            print(f"⚠️ 获取SKU图片失败: {sku_name} - {str(e)}")
            return ""

    def _extract_sales_count_from_init_data(self, init_data: Dict) -> int:
        """从初始化数据中提取销量"""
        try:
            # 方法1: 从result.data.Root.fields.dataJson.tempModel中提取（根据您提供的信息）
            result_section = init_data.get('result', {})
            data_section = result_section.get('data', {})
            root_component = data_section.get('Root', {})
            if root_component:
                fields = root_component.get('fields', {})
                data_json = fields.get('dataJson', {})
                temp_model = data_json.get('tempModel', {})

                if temp_model:
                    print(f"🔍 tempModel包含的键: {list(temp_model.keys())}")
                    # 尝试多个可能的销量字段
                    for sales_field in ['saledCount', 'mixAmount', 'totalSoldQuantity', 'soldCount', 'saleCount']:
                        sales_count = temp_model.get(sales_field, 0)
                        if sales_count:
                            print(f"✅ 提取销量(result.data.Root.fields.dataJson.tempModel.{sales_field}): {sales_count}")
                            return int(sales_count)
                else:
                    print("⚠️ 未找到tempModel字段")

            # 方法2: 从result.global.globalData.model.tempModel中提取（备用）
            global_data = init_data.get('result', {}).get('global', {}).get('globalData', {}).get('model', {})
            temp_model = global_data.get('tempModel', {})

            sales_count = temp_model.get('saledCount', 0)
            if sales_count:
                print(f"✅ 提取销量(globalData.tempModel): {sales_count}")
                return int(sales_count)

            # 方法2: 查找标题组件中的销量信息
            data_section = init_data.get('data', {})
            for component in data_section.values():
                if isinstance(component, dict) and component.get('componentType') == '@ali/tdmod-od-pc-offer-title':
                    sale_num = component.get('data', {}).get('saleNum', '')
                    if sale_num:
                        # 提取数字，如"2200+"
                        import re
                        match = re.search(r'(\d+)', sale_num)
                        if match:
                            sales_count = int(match.group(1))
                            print(f"✅ 提取销量(组件): {sales_count}")
                            return sales_count

            print("⚠️ 未找到销量信息")
            return 0
        except Exception as e:
            print(f"❌ 提取销量失败: {str(e)}")
            return 0

    def _extract_freight_cost_from_init_data(self, init_data: Dict) -> float:
        """从初始化数据中提取运费"""
        try:
            # 查找物流组件
            data_section = init_data.get('data', {})
            for component in data_section.values():
                if component.get('componentType') == '@ali/tdmod-od-pc-offer-logistics':
                    total_cost = component.get('data', {}).get('totalCost', 0)
                    return float(total_cost)
            return 0.0
        except Exception as e:
            print(f"❌ 提取运费失败: {str(e)}")
            return 0.0

    def _get_product_description_from_init_data(self, init_data: Dict) -> str:
        """从初始化数据中获取商品描述"""
        try:
            # 方法1: 从result.data.Root.fields.dataJson.offerDetail.detailUrl中查找（根据您提供的信息）
            result_section = init_data.get('result', {})
            data_section = result_section.get('data', {})
            root_component = data_section.get('Root', {})
            if root_component:
                fields = root_component.get('fields', {})
                data_json = fields.get('dataJson', {})
                offer_detail = data_json.get('offerDetail', {})
                desc_url = offer_detail.get('detailUrl', '')
                if desc_url:
                    print(f"🔍 找到商品描述URL(result.data.Root.fields.dataJson.offerDetail): {desc_url}")
                    description = self._fetch_product_description(desc_url)
                    print(f"📝 获取到的商品描述内容:")
                    print(f"   长度: {len(description)} 字符")
                    print(f"   内容预览: {description[:200]}...")
                    return description

            # 方法2: 从result.global.globalData.model.offerDetail.detailUrl中查找（备用）
            global_data = init_data.get('result', {}).get('global', {}).get('globalData', {}).get('model', {})
            offer_detail = global_data.get('offerDetail', {})
            desc_url = offer_detail.get('detailUrl', '')
            if desc_url:
                print(f"🔍 找到商品描述URL(globalData.offerDetail): {desc_url}")
                description = self._fetch_product_description(desc_url)
                print(f"📝 获取到的商品描述内容:")
                print(f"   长度: {len(description)} 字符")
                print(f"   内容预览: {description[:200]}...")
                return description

            # 方法2: 从result.data中查找描述组件（备用方法）
            result_data = init_data.get('result', {}).get('data', {})
            for component_key, component in result_data.items():
                if isinstance(component, dict):
                    fields = component.get('fields', {})
                    if 'detailUrl' in fields:
                        desc_url = fields['detailUrl']
                        if desc_url:
                            print(f"🔍 找到商品描述URL(result.data): {desc_url}")
                            return self._fetch_product_description(desc_url)

            # 方法4: 查找描述组件
            data_section = init_data.get('data', {})
            for component in data_section.values():
                if isinstance(component, dict):
                    if component.get('componentType') == '@ali/tdmod-od-pc-offer-description':
                        detail_url = component.get('data', {}).get('detailUrl', '')
                        if detail_url:
                            print(f"🔍 找到商品描述URL(组件): {detail_url}")
                            return self._fetch_product_description(detail_url)

            # 方法5: 检查是否有通过正则提取的描述组件
            if 'desc_component' in data_section:
                detail_url = data_section['desc_component'].get('data', {}).get('detailUrl', '')
                if detail_url:
                    print(f"🔍 找到商品描述URL(正则): {detail_url}")
                    return self._fetch_product_description(detail_url)

            print("⚠️ 未找到商品描述URL")
            return "优质商品，欢迎选购！"

        except Exception as e:
            print(f"❌ 获取商品描述失败: {str(e)}")
            return "优质商品，欢迎选购！"

    def _fetch_product_description(self, detail_url: str) -> str:
        """获取商品详细描述"""
        try:
            print(f"🌐 请求商品描述: {detail_url}")

            # 首先尝试从临时文件读取（用于测试）
            try:
                with open('temp/1688detail_tdmod-od-pc-offer-description.text', 'r', encoding='utf-8') as f:
                    content = f.read()
                    print("✅ 从临时文件读取商品描述")
            except FileNotFoundError:
                # 如果临时文件不存在，则发送网络请求
                headers = {
                    'User-Agent': self.default_headers['user-agent'],
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                }

                response = requests.get(detail_url, headers=headers, timeout=30)

                if response.status_code == 200:
                    content = response.text
                    print("✅ 从网络获取商品描述")
                else:
                    print(f"⚠️ 请求商品描述失败，状态码: {response.status_code}")
                    return "优质商品，欢迎选购！"

            # 特殊处理1688商品描述的两种返回格式
            import re

            # 格式1: var desc='<div>...</div>';
            desc_match = re.search(r"var\s+desc\s*=\s*'([^']+)';", content)
            if desc_match:
                html_content = desc_match.group(1)
                # 处理转义字符
                html_content = html_content.replace('\\', '')

                if html_content and len(html_content) > 10:
                    print(f"✅ 获取商品描述(var desc格式)成功，长度: {len(html_content)}")
                    return html_content

            # 格式2: 直接HTML内容
            if content.strip().startswith('<div') and content.strip().endswith('</div>'):
                html_content = content.strip()

                if html_content and len(html_content) > 10:
                    print(f"✅ 获取商品描述(直接HTML格式)成功，长度: {len(html_content)}")
                    return html_content

            # 格式3: 原有的JSON格式（备用）
            offer_details_match = re.search(r'var\s+offer_details\s*=\s*({.+?});', content)
            if offer_details_match:
                import json
                try:
                    detail_data = json.loads(offer_details_match.group(1))
                    html_content = detail_data.get('content', '')

                    if html_content:
                        # 优先返回完整的HTML内容（保留格式和图片）
                        clean_html = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL)
                        clean_html = re.sub(r'<style[^>]*>.*?</style>', '', clean_html, flags=re.DOTALL)

                        if clean_html and len(clean_html) > 10:
                            print(f"✅ 获取商品描述(offer_details格式)成功，长度: {len(clean_html)}")
                            return clean_html

                except json.JSONDecodeError as e:
                    print(f"⚠️ 解析描述JSON失败: {str(e)}")

            print("⚠️ 未找到有效的商品描述内容")
            return "优质商品，欢迎选购！"

        except Exception as e:
            print(f"❌ 获取商品描述失败: {str(e)}")
            return "优质商品，欢迎选购！"

    def _extract_field(self, html_content: str, patterns: List[str], source_url: str = "") -> Optional[str]:
        """提取字符串字段"""
        for pattern in patterns:
            match = re.search(pattern, html_content) or (re.search(pattern, source_url) if source_url else None)
            if match:
                return match.group(1).strip()
        return None

    def _extract_number_field(self, html_content: str, patterns: List[str], is_float: bool = False) -> Optional[int]:
        """提取数字字段"""
        for pattern in patterns:
            match = re.search(pattern, html_content)
            if match:
                try:
                    if is_float:
                        return float(match.group(1))
                    else:
                        return int(match.group(1))
                except ValueError:
                    continue
        return None

    def _extract_images_from_html(self, html_content: str) -> List[str]:
        """提取商品图片"""
        images = []

        # 方法1: 从offerImgList提取
        img_list_pattern = r'"offerImgList"\s*:\s*\[([^\]]+)\]'
        img_list_match = re.search(img_list_pattern, html_content)
        if img_list_match:
            img_urls = re.findall(r'"(https://[^"]*\.jpg[^"]*)"', img_list_match.group(1))
            images.extend(img_urls)

        # 方法2: 直接搜索阿里云图片URL
        if not images:
            img_pattern = r'"(https://cbu01\.alicdn\.com/img/ibank/[^"]*\.jpg)"'
            img_matches = re.findall(img_pattern, html_content)
            images.extend(img_matches)

        # 去重
        unique_images = []
        seen = set()
        for img in images:
            if img not in seen:
                unique_images.append(img)
                seen.add(img)

        return unique_images[:20]

    def _extract_skus_from_html(self, html_content: str) -> List[Dict[str, Any]]:
        """提取SKU信息"""
        skus = []

        try:
            # 方法1: 尝试提取SKU价格映射 - 使用更宽泛的模式
            sku_info_patterns = [
                r'"skuInfoMap"\s*:\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}',  # 支持嵌套对象
                r'"skuMap"\s*:\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}',
                r'"skuPriceMap"\s*:\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}',
                r'"skuModel"\s*:\s*\{[^}]*"skuInfoMap"\s*:\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}',
                r'"tradeModel"\s*:\s*\{[^}]*"skuInfoMap"\s*:\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}'
            ]

            sku_info_data = None
            for pattern in sku_info_patterns:
                sku_info_match = re.search(pattern, html_content, re.DOTALL)
                if sku_info_match:
                    sku_info_data = sku_info_match.group(1)
                    print(f"✅ 找到SKU映射数据，使用模式: {pattern}")
                    break

            if not sku_info_data:
                print("⚠️ 未找到SKU价格映射，尝试提取价格范围")
                # 方法2: 尝试从价格区间创建SKU
                return self._create_skus_from_price_range(html_content)

            # 解析每个SKU组合 - 使用更灵活的正则表达式
            sku_patterns = [
                r'"([^"]+)"\s*:\s*\{[^}]*"price"\s*:\s*"?([\d.]+)"?[^}]*"canBookCount"\s*:\s*(\d+)[^}]*\}',
                r'"([^"]+)"\s*:\s*\{[^}]*"price"\s*:\s*([\d.]+)[^}]*"stock"\s*:\s*(\d+)[^}]*\}',
                r'"([^"]+)"\s*:\s*\{[^}]*[\'"]([\d.]+)[\'"][^}]*[\'"]([\d]+)[\'"][^}]*\}'
            ]

            sku_matches = []
            for pattern in sku_patterns:
                matches = re.findall(pattern, sku_info_data)
                if matches:
                    sku_matches = matches
                    print(f"✅ 找到SKU数据，使用模式: {pattern}")
                    break

            print(f"📋 找到 {len(sku_matches)} 个SKU组合")

            if not sku_matches:
                print("⚠️ 无法解析SKU数据，创建默认SKU")
                return self._create_skus_from_price_range(html_content)

            sku_id = 1
            for sku_key, price, stock in sku_matches:
                try:
                    # 解析SKU属性
                    properties = self._parse_sku_properties(sku_key)

                    # 构建SKU对象
                    sku = {
                        'id': sku_id,
                        'properties': properties,
                        'price': int(float(price) * 100),  # 转换为分
                        'marketPrice': int(float(price) * 100),  # 暂时使用相同价格
                        'vipPrice': None,
                        'picUrl': "",  # 可以后续优化
                        'stock': int(stock),
                        'weight': 0.5,
                        'volume': 0.3
                    }

                    skus.append(sku)
                    sku_id += 1

                except Exception as e:
                    print(f"⚠️ 解析SKU失败: {sku_key} - {str(e)}")
                    continue

            print(f"✅ 成功解析 {len(skus)} 个SKU")
            return skus

        except Exception as e:
            print(f"❌ SKU解析失败: {str(e)}")
            return self._create_skus_from_price_range(html_content)

    def _create_skus_from_price_range(self, html_content: str) -> List[Dict[str, Any]]:
        """从价格范围创建SKU"""
        try:
            skus = []

            # 尝试提取价格范围
            price_range_patterns = [
                r'"minPrice"\s*:\s*"?([\d.]+)"?[^}]*"maxPrice"\s*:\s*"?([\d.]+)"?',
                r'"price"\s*:\s*"?([\d.]+)"?[^}]*"maxPrice"\s*:\s*"?([\d.]+)"?',
                r'价格[：:]\s*([\d.]+)[-~]([\d.]+)',
                r'¥\s*([\d.]+)[-~]([\d.]+)',
                r'"unitPrice"\s*:\s*"?([\d.]+)"?'
            ]

            min_price = None
            max_price = None

            for pattern in price_range_patterns:
                match = re.search(pattern, html_content)
                if match:
                    try:
                        if len(match.groups()) == 2:  # 价格范围
                            min_price = float(match.group(1))
                            max_price = float(match.group(2))
                            print(f"✅ 找到价格范围: {min_price}-{max_price}元")
                            break
                        else:  # 单一价格
                            min_price = max_price = float(match.group(1))
                            print(f"✅ 找到单一价格: {min_price}元")
                            break
                    except ValueError:
                        continue

            if not min_price:
                # 如果没有找到价格，使用默认价格
                min_price = max_price = 9.0
                print("⚠️ 未找到价格信息，使用默认价格9.0元")

            # 创建SKU
            if min_price == max_price:
                # 单一价格，创建一个SKU
                sku = {
                    'id': 1,
                    'properties': [
                        {
                            'propertyId': 1,
                            'propertyName': '规格',
                            'valueId': 1,
                            'valueName': '默认规格'
                        }
                    ],
                    'price': int(min_price * 100),  # 转换为分
                    'marketPrice': int(min_price * 100),
                    'vipPrice': None,
                    'picUrl': "",
                    'stock': 999,
                    'weight': 0.5,
                    'volume': 0.3
                }
                skus.append(sku)
                print(f"✅ 创建单一SKU，价格: {min_price}元")
            else:
                # 价格范围，创建两个SKU
                sku1 = {
                    'id': 1,
                    'properties': [
                        {
                            'propertyId': 1,
                            'propertyName': '规格',
                            'valueId': 1,
                            'valueName': '基础版'
                        }
                    ],
                    'price': int(min_price * 100),
                    'marketPrice': int(min_price * 100),
                    'vipPrice': None,
                    'picUrl': "",
                    'stock': 500,
                    'weight': 0.5,
                    'volume': 0.3
                }

                sku2 = {
                    'id': 2,
                    'properties': [
                        {
                            'propertyId': 1,
                            'propertyName': '规格',
                            'valueId': 2,
                            'valueName': '高级版'
                        }
                    ],
                    'price': int(max_price * 100),
                    'marketPrice': int(max_price * 100),
                    'vipPrice': None,
                    'picUrl': "",
                    'stock': 500,
                    'weight': 0.5,
                    'volume': 0.3
                }

                skus.extend([sku1, sku2])
                print(f"✅ 创建价格范围SKU: {min_price}元 和 {max_price}元")

            return skus

        except Exception as e:
            print(f"❌ 从价格范围创建SKU失败: {str(e)}")
            # 返回一个默认SKU
            return [{
                'id': 1,
                'properties': [
                    {
                        'propertyId': 1,
                        'propertyName': '规格',
                        'valueId': 1,
                        'valueName': '默认规格'
                    }
                ],
                'price': 900,  # 9.00元
                'marketPrice': 950,  # 9.50元
                'vipPrice': None,
                'picUrl': "",
                'stock': 999,
                'weight': 0.5,
                'volume': 0.3
            }]

    def _create_default_sku_from_price(self, html_content: str) -> List[Dict[str, Any]]:
        """从价格信息创建默认SKU"""
        try:
            # 尝试提取价格信息
            price_patterns = [
                r'"price"\s*:\s*"?([\d.]+)"?',
                r'"minPrice"\s*:\s*"?([\d.]+)"?',
                r'"maxPrice"\s*:\s*"?([\d.]+)"?',
                r'价格[：:]\s*([\d.]+)',
                r'¥\s*([\d.]+)'
            ]

            price = None
            for pattern in price_patterns:
                match = re.search(pattern, html_content)
                if match:
                    try:
                        price = float(match.group(1))
                        break
                    except ValueError:
                        continue

            if not price:
                price = 0.0
                print("⚠️ 无法提取价格，使用默认价格0")

            # 创建默认SKU
            default_sku = {
                'id': 1,
                'properties': [
                    {
                        'propertyId': 1,
                        'propertyName': '规格',
                        'valueId': 1,
                        'valueName': '默认规格'
                    }
                ],
                'price': int(price * 100),  # 转换为分
                'marketPrice': int(price * 100),
                'vipPrice': None,
                'picUrl': "",
                'stock': 999,  # 默认库存
                'weight': 0.5,
                'volume': 0.3
            }

            print(f"✅ 创建默认SKU，价格: {price}元")
            return [default_sku]

        except Exception as e:
            print(f"❌ 创建默认SKU失败: {str(e)}")
            return []

    def _parse_sku_properties(self, sku_key: str) -> List[Dict[str, Any]]:
        """解析SKU属性"""
        properties = []

        try:
            # 格式: "8388【黑色】流汗狗>38/39【偏小一码】"
            if '>' in sku_key:
                parts = sku_key.split('>')
                if len(parts) >= 2:
                    # 第一部分包含颜色和图案
                    color_part = parts[0]
                    size_part = parts[1]

                    # 提取颜色
                    color_match = re.search(r'【([^】]+)】', color_part)
                    if color_match:
                        color_name = color_match.group(1)
                        properties.append({
                            'propertyId': 20,
                            'propertyName': '颜色',
                            'valueId': 40,
                            'valueName': color_name
                        })

                    # 提取尺码
                    size_match = re.search(r'(\d+/\d+)', size_part)
                    if size_match:
                        size_name = size_match.group(1)
                        properties.append({
                            'propertyId': 21,
                            'propertyName': '尺码',
                            'valueId': 41,
                            'valueName': size_name
                        })

            return properties

        except Exception as e:
            print(f"⚠️ 解析SKU属性失败: {sku_key} - {str(e)}")
            return properties

    def _extract_price_from_skus(self, skus: List[Dict[str, Any]], html_content: str) -> Dict[str, int]:
        """从SKU中提取价格范围"""
        if skus:
            prices = [sku.get('price', 0) for sku in skus if sku.get('price', 0) > 0]
            if prices:
                return {
                    'min_price': min(prices),
                    'max_price': max(prices)
                }

        # 备选方案：从HTML中直接提取价格
        price_patterns = [
            r'"skuPriceScale"\s*:\s*"([\d.]+)-([\d.]+)"',
            r'"minPrice"\s*:\s*"?([\d.]+)"?.*?"maxPrice"\s*:\s*"?([\d.]+)"?',
            r'"price"\s*:\s*"?([\d.]+)"?',
            r'"unitPrice"\s*:\s*"?([\d.]+)"?',
            r'价格[：:]\s*([\d.]+)[-~]([\d.]+)',
            r'价格[：:]\s*([\d.]+)',
            r'¥\s*([\d.]+)[-~]([\d.]+)',
            r'¥\s*([\d.]+)'
        ]

        for pattern in price_patterns:
            match = re.search(pattern, html_content)
            if match:
                try:
                    if len(match.groups()) == 2:  # 价格范围
                        min_price = int(float(match.group(1)) * 100)
                        max_price = int(float(match.group(2)) * 100)
                        return {'min_price': min_price, 'max_price': max_price}
                    else:  # 单一价格
                        price = int(float(match.group(1)) * 100)
                        return {'min_price': price, 'max_price': price}
                except ValueError:
                    continue

        print("⚠️ 无法提取价格信息，使用默认价格")
        return {'min_price': 0, 'max_price': 0}

    def _generate_product_description(self, html_content: str, title: str) -> str:
        """生成商品描述（不包含图片）"""
        try:
            # 尝试提取商品描述文本
            description_patterns = [
                r'"description"\s*:\s*"([^"]+)"',
                r'"productDesc"\s*:\s*"([^"]+)"',
                r'"offerDesc"\s*:\s*"([^"]+)"',
                r'<meta\s+name="description"\s+content="([^"]+)"'
            ]

            description = ""
            for pattern in description_patterns:
                match = re.search(pattern, html_content)
                if match:
                    description = match.group(1).strip()
                    break

            # 如果没有找到描述，使用标题作为描述
            if not description and title:
                description = f"【{title}】优质商品，欢迎选购！"

            # 清理HTML标签
            description = re.sub(r'<[^>]+>', '', description)

            return description or "优质商品，欢迎选购！"

        except Exception as e:
            print(f"⚠️ 生成商品描述失败: {str(e)}")
            return "优质商品，欢迎选购！"

    def _generate_description_from_images(self, images: List[str]) -> str:
        """生成包含图片的商品详情描述（用于详情展示）"""
        if not images:
            return ""

        description_parts = ['<div class="product-detail-images">']
        for img in images[:15]:
            description_parts.append(f'<img src="{img}" alt="商品详情" style="width: 100%; margin-bottom: 10px;"><br>')
        description_parts.append('</div>')

        return ''.join(description_parts)

    def _generate_keywords_from_title(self, title: str) -> str:
        """生成关键词"""
        if not title:
            return "1688,商品"

        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', title)
        keywords = [word for word in words if len(word) >= 2]

        return ','.join(keywords[:10]) if keywords else "1688,商品"

    def format_search_results_for_upstream(self, products: List[Dict], query: str, total: int) -> Dict[str, Any]:
        """格式化搜索结果为上游系统格式"""
        try:
            formatted_products = []

            for product in products:
                # 确保所有必需字段都存在
                formatted_product = {
                    'id': product.get('id', 0),
                    'name': product.get('name', ''),
                    'introduction': product.get('introduction', ''),
                    'categoryId': product.get('categoryId', 0),
                    'picUrl': product.get('picUrl', ''),
                    'sliderPicUrls': product.get('sliderPicUrls', []),
                    'specType': product.get('specType', False),
                    'price': product.get('price', 0),
                    'marketPrice': product.get('marketPrice', 0),
                    'stock': product.get('stock', 0),
                    'salesCount': product.get('salesCount', 0),
                    'scores': product.get('scores', 4),
                    'newest': product.get('newest', False),
                    'sale': product.get('sale', False),
                    'hot': product.get('hot', False),

                    # 扩展字段
                    'productLink': product.get('productLink', ''),
                    'shopLink': product.get('shopLink', ''),
                    'shopName': product.get('shopName', ''),
                    'location': product.get('location', ''),
                    'platform': product.get('platform', self.platform_code),
                    'keyword': product.get('keyword', ''),
                }

                formatted_products.append(formatted_product)

            return {
                'list': formatted_products,
                'total': total,
                'platform': self.platform_code,
                'query': query,
                'success': True
            }

        except Exception as e:
            print(f"❌ 格式化搜索结果失败: {str(e)}")
            return {
                'list': [],
                'total': 0,
                'platform': self.platform_code,
                'query': query,
                'success': False,
                'error': str(e)
            }


# 全局爬虫实例
_alibaba_1688_crawler = None

def get_alibaba_1688_crawler() -> Alibaba1688Crawler:
    """获取1688爬虫实例（单例模式）"""
    global _alibaba_1688_crawler
    if _alibaba_1688_crawler is None:
        _alibaba_1688_crawler = Alibaba1688Crawler()
    return _alibaba_1688_crawler
