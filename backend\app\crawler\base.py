"""
基础爬虫类

提供所有平台爬虫的基础功能和接口
"""

import asyncio
import random
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, Page

from .config import CrawlerConfig, get_platform_config
from .utils.stealth import apply_stealth
from .utils.proxy import ProxyManager


@dataclass
class SearchResult:
    """搜索结果数据结构"""
    title: str
    price: str
    image_url: str
    product_url: str
    platform: str
    shop_name: Optional[str] = None
    rating: Optional[float] = None
    sales: Optional[str] = None
    location: Optional[str] = None
    extra_info: Optional[Dict[str, Any]] = None


@dataclass
class CrawlerResult:
    """爬虫结果数据结构"""
    platform: str
    query: str
    total_found: int
    products: List[SearchResult]
    success: bool
    error_message: Optional[str] = None
    response_time: Optional[float] = None


class BaseCrawler(ABC):
    """基础爬虫类"""
    
    def __init__(self, platform: str, config: Optional[CrawlerConfig] = None):
        self.platform = platform.lower()
        self.config = config or CrawlerConfig()
        self.platform_config = get_platform_config(self.platform)
        
        if not self.platform_config:
            raise ValueError(f"不支持的平台: {platform}")
        
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.proxy_manager = ProxyManager() if self.config.use_proxy else None
        
        # 性能统计
        self.stats = {
            "requests_count": 0,
            "success_count": 0,
            "error_count": 0,
            "total_time": 0.0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self):
        """启动浏览器"""
        try:
            playwright = await async_playwright().start()
            
            # 选择浏览器类型
            if self.config.browser_type == "firefox":
                browser_type = playwright.firefox
            elif self.config.browser_type == "webkit":
                browser_type = playwright.webkit
            else:
                browser_type = playwright.chromium
            
            # 启动浏览器
            launch_options = {
                "headless": self.config.headless,
                "args": [
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-accelerated-2d-canvas",
                    "--no-first-run",
                    "--no-zygote",
                    "--disable-gpu"
                ]
            }
            
            # 添加代理配置
            if self.config.use_proxy and self.proxy_manager:
                proxy = await self.proxy_manager.get_proxy()
                if proxy:
                    launch_options["proxy"] = proxy
            
            self.browser = await browser_type.launch(**launch_options)
            
            # 创建浏览器上下文
            context_options = {
                "viewport": {
                    "width": self.config.viewport_width,
                    "height": self.config.viewport_height
                },
                "user_agent": random.choice(self.config.user_agents) if self.config.random_user_agent else None
            }
            
            self.context = await self.browser.new_context(**context_options)
            
            # 应用反检测措施
            if self.config.use_stealth:
                await apply_stealth(self.context)
            
        except Exception as e:
            raise Exception(f"启动浏览器失败: {str(e)}")
    
    async def close(self):
        """关闭浏览器"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
        except Exception as e:
            print(f"关闭浏览器时出错: {str(e)}")
    
    async def create_page(self) -> Page:
        """创建新页面"""
        if not self.context:
            raise Exception("浏览器上下文未初始化")
        
        page = await self.context.new_page()
        
        # 设置超时
        page.set_default_timeout(self.config.timeout)
        page.set_default_navigation_timeout(self.config.navigation_timeout)
        
        return page
    
    async def search(self, query: str, max_pages: int = 1) -> CrawlerResult:
        """搜索商品"""
        start_time = time.time()
        self.stats["requests_count"] += 1
        
        try:
            # 执行搜索
            products = await self._search_implementation(query, max_pages)
            
            # 统计
            response_time = time.time() - start_time
            self.stats["success_count"] += 1
            self.stats["total_time"] += response_time
            
            return CrawlerResult(
                platform=self.platform,
                query=query,
                total_found=len(products),
                products=products,
                success=True,
                response_time=response_time
            )
            
        except Exception as e:
            # 统计
            response_time = time.time() - start_time
            self.stats["error_count"] += 1
            self.stats["total_time"] += response_time
            
            return CrawlerResult(
                platform=self.platform,
                query=query,
                total_found=0,
                products=[],
                success=False,
                error_message=str(e),
                response_time=response_time
            )
    
    @abstractmethod
    async def _search_implementation(self, query: str, max_pages: int) -> List[SearchResult]:
        """具体的搜索实现，由子类实现"""
        pass
    
    async def retry_on_failure(self, func, *args, **kwargs):
        """失败重试装饰器"""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.config.max_retries:
                    wait_time = self.config.retry_delay * (2 ** attempt)  # 指数退避
                    await asyncio.sleep(wait_time)
                    continue
                break
        
        raise last_exception
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        avg_time = self.stats["total_time"] / max(self.stats["requests_count"], 1)
        success_rate = self.stats["success_count"] / max(self.stats["requests_count"], 1) * 100
        
        return {
            "platform": self.platform,
            "requests_count": self.stats["requests_count"],
            "success_count": self.stats["success_count"],
            "error_count": self.stats["error_count"],
            "success_rate": round(success_rate, 2),
            "average_response_time": round(avg_time, 2)
        }
