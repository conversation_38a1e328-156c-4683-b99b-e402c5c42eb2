"""
爬虫配置管理服务
负责账号选择、配置更新、健康监控等功能
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, func
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import random
import logging

from app.models import Platform, CrawlerAccount
from app.database import get_db

logger = logging.getLogger(__name__)


class ConfigService:
    """配置管理服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_best_account(self, platform_code: str) -> Optional[CrawlerAccount]:
        """获取最佳可用账号"""
        try:
            # 获取平台信息
            platform = self.db.query(Platform).filter(Platform.code == platform_code).first()
            if not platform:
                logger.error(f"平台不存在: {platform_code}")
                return None
            
            # 查询可用账号
            available_accounts = self.db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.platform_id == platform.id,
                    CrawlerAccount.status == 'active',
                    CrawlerAccount.error_count < 3,
                    or_(
                        CrawlerAccount.current_requests_count < CrawlerAccount.max_requests_per_hour,
                        CrawlerAccount.current_requests_count.is_(None)
                    )
                )
            ).all()
            
            if not available_accounts:
                logger.warning(f"没有可用的{platform_code}账号")
                return None
            
            # 选择最佳账号（基于健康度和使用频率）
            best_account = self._select_best_account(available_accounts)
            
            if best_account:
                logger.info(f"选择{platform_code}账号: {best_account.username}")
                # 更新使用统计
                self._update_account_usage(best_account)
            
            return best_account
            
        except Exception as e:
            logger.error(f"获取{platform_code}最佳账号失败: {str(e)}")
            return None
    
    def _select_best_account(self, accounts: List[CrawlerAccount]) -> Optional[CrawlerAccount]:
        """选择最佳账号的算法"""
        try:
            # 计算每个账号的得分
            scored_accounts = []
            
            for account in accounts:
                score = self._calculate_account_score(account)
                scored_accounts.append((account, score))
            
            # 按得分排序，选择最高分的账号
            scored_accounts.sort(key=lambda x: x[1], reverse=True)
            
            if scored_accounts:
                return scored_accounts[0][0]
            
            return None
            
        except Exception as e:
            logger.error(f"选择最佳账号失败: {str(e)}")
            return None
    
    def _calculate_account_score(self, account: CrawlerAccount) -> float:
        """计算账号得分"""
        try:
            score = 100.0  # 基础分数
            
            # 成功率权重 (40%)
            success_rate = float(account.success_rate or 100.0)
            score += (success_rate - 50) * 0.4
            
            # 错误次数权重 (20%)
            error_penalty = account.error_count * 10
            score -= error_penalty * 0.2
            
            # 使用频率权重 (20%) - 优先选择使用较少的账号
            usage_ratio = account.current_requests_count / max(account.max_requests_per_hour, 1)
            score -= usage_ratio * 20 * 0.2
            
            # 优先级权重 (10%)
            priority_bonus = account.priority * 5
            score += priority_bonus * 0.1
            
            # 最后使用时间权重 (10%) - 优先选择最近未使用的账号
            if account.last_used_at:
                hours_since_last_use = (datetime.now() - account.last_used_at).total_seconds() / 3600
                freshness_bonus = min(hours_since_last_use, 24) * 2  # 最多24小时的奖励
                score += freshness_bonus * 0.1
            else:
                score += 24 * 2 * 0.1  # 从未使用过的账号获得最高奖励
            
            return max(score, 0)  # 确保分数不为负数
            
        except Exception as e:
            logger.error(f"计算账号得分失败: {str(e)}")
            return 0.0
    
    def _update_account_usage(self, account: CrawlerAccount):
        """更新账号使用统计"""
        try:
            account.last_used_at = datetime.now()
            account.current_requests_count = (account.current_requests_count or 0) + 1
            account.total_requests = (account.total_requests or 0) + 1
            self.db.commit()
            
        except Exception as e:
            logger.error(f"更新账号使用统计失败: {str(e)}")
            self.db.rollback()
    
    def update_account_result(self, account: CrawlerAccount, success: bool, error_message: str = None):
        """更新账号使用结果"""
        try:
            if success:
                account.success_requests = (account.success_requests or 0) + 1
                account.error_count = 0  # 成功后重置错误计数
                account.last_error_message = None
            else:
                account.error_count = (account.error_count or 0) + 1
                account.last_error_message = error_message
                
                # 如果连续错误次数过多，暂时停用账号
                if account.error_count >= 5:
                    account.status = 'error'
                    logger.warning(f"账号 {account.username} 因连续错误过多被停用")
            
            # 重新计算成功率
            if account.total_requests > 0:
                account.success_rate = (account.success_requests / account.total_requests) * 100
            
            self.db.commit()
            
        except Exception as e:
            logger.error(f"更新账号结果失败: {str(e)}")
            self.db.rollback()
    
    def cleanup_cookie(self, cookie_string: str) -> str:
        """清理和去重Cookie字符串"""
        if not cookie_string:
            return ''

        try:
            # 分割Cookie字符串为键值对
            cookie_pairs = [pair.strip() for pair in cookie_string.split(';') if pair.strip()]

            # 使用字典去重，保留最后出现的值
            cookie_dict = {}

            for pair in cookie_pairs:
                if '=' in pair:
                    key, *value_parts = pair.split('=')
                    value = '='.join(value_parts)  # 处理值中包含=的情况
                    clean_key = key.strip()

                    if clean_key:
                        cookie_dict[clean_key] = value

            # 重新组装Cookie字符串
            cleaned_cookie = '; '.join([f"{key}={value}" for key, value in cookie_dict.items()])

            logger.info(f"Cookie清理: 原长度={len(cookie_string)}, 清理后长度={len(cleaned_cookie)}, 去重数量={len(cookie_dict)}")

            return cleaned_cookie

        except Exception as e:
            logger.error(f"Cookie清理失败: {str(e)}")
            return cookie_string  # 返回原始Cookie

    def create_or_update_account(self, platform_code: str, username: str,
                               cookie: str, token: str, user_agent: str = None,
                               extracted_from: str = None) -> CrawlerAccount:
        """创建或更新账号配置"""
        try:
            # 清理Cookie
            cleaned_cookie = self.cleanup_cookie(cookie)

            # 获取平台
            platform = self.db.query(Platform).filter(Platform.code == platform_code).first()
            if not platform:
                # 创建新平台
                platform = Platform(
                    code=platform_code,
                    name=platform_code.title(),
                    status='active'
                )
                self.db.add(platform)
                self.db.flush()
            
            # 查找现有账号
            account = self.db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.platform_id == platform.id,
                    CrawlerAccount.username == username
                )
            ).first()
            
            if account:
                # 更新现有账号
                account.cookie = cleaned_cookie
                account.token = token
                account.user_agent = user_agent or account.user_agent
                account.status = 'active'
                account.error_count = 0  # 重置错误计数
                account.updated_at = datetime.now()
                account.extracted_from = extracted_from
                
                logger.info(f"更新{platform_code}账号: {username}")
                
            else:
                # 创建新账号
                account = CrawlerAccount(
                    platform_id=platform.id,
                    username=username,
                    display_name=username,
                    cookie=cleaned_cookie,
                    token=token,
                    user_agent=user_agent,
                    status='active',
                    extracted_from=extracted_from,
                    notes=f"通过Chrome扩展创建于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
                self.db.add(account)
                
                logger.info(f"创建{platform_code}新账号: {username}")
            
            self.db.commit()
            return account
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建或更新账号失败: {str(e)}")
            raise
    
    def get_account_stats(self, platform_code: str = None) -> Dict[str, Any]:
        """获取账号统计信息"""
        try:
            query = self.db.query(CrawlerAccount)
            
            if platform_code:
                platform = self.db.query(Platform).filter(Platform.code == platform_code).first()
                if platform:
                    query = query.filter(CrawlerAccount.platform_id == platform.id)
            
            accounts = query.all()
            
            stats = {
                'total_accounts': len(accounts),
                'active_accounts': len([a for a in accounts if a.status == 'active']),
                'inactive_accounts': len([a for a in accounts if a.status == 'inactive']),
                'error_accounts': len([a for a in accounts if a.status == 'error']),
                'total_requests': sum(a.total_requests or 0 for a in accounts),
                'total_success': sum(a.success_requests or 0 for a in accounts),
                'avg_success_rate': 0
            }
            
            if stats['total_requests'] > 0:
                stats['avg_success_rate'] = (stats['total_success'] / stats['total_requests']) * 100
            
            return stats
            
        except Exception as e:
            logger.error(f"获取账号统计失败: {str(e)}")
            return {}
    
    def reset_hourly_limits(self):
        """重置每小时请求限制（定时任务调用）"""
        try:
            self.db.query(CrawlerAccount).update({
                CrawlerAccount.current_requests_count: 0
            })
            self.db.commit()
            logger.info("已重置所有账号的每小时请求计数")
            
        except Exception as e:
            logger.error(f"重置每小时限制失败: {str(e)}")
            self.db.rollback()
    
    def cleanup_expired_accounts(self):
        """清理过期账号（定时任务调用）"""
        try:
            # 将长时间未使用的账号标记为inactive
            cutoff_time = datetime.now() - timedelta(days=30)
            
            expired_count = self.db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.last_used_at < cutoff_time,
                    CrawlerAccount.status == 'active'
                )
            ).update({
                CrawlerAccount.status: 'inactive',
                CrawlerAccount.notes: func.concat(
                    CrawlerAccount.notes, 
                    f"\n自动停用于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} (30天未使用)"
                )
            })
            
            self.db.commit()
            
            if expired_count > 0:
                logger.info(f"已停用 {expired_count} 个长时间未使用的账号")
            
        except Exception as e:
            logger.error(f"清理过期账号失败: {str(e)}")
            self.db.rollback()


def get_config_service(db: Session = None) -> ConfigService:
    """获取配置服务实例"""
    if db is None:
        db = next(get_db())
    return ConfigService(db)
