{% extends "base.html" %}

{% block title %}爬虫状态通知 - AqentCrawler{% endblock %}

{% block header_title %}爬虫状态通知{% endblock %}
{% block header_subtitle %}爬虫账号状态变更通知{% endblock %}

{% block content %}
<div class="alert alert-{{ status_type or 'info' }}">
    <h3 style="margin: 0 0 10px 0;">
        {% if status_type == 'success' %}✅{% elif status_type == 'warning' %}⚠️{% elif status_type == 'danger' %}❌{% else %}ℹ️{% endif %}
        {{ notification_title or '爬虫状态变更' }}
    </h3>
    <p style="margin: 0;">{{ notification_message }}</p>
</div>

{% if crawler_accounts %}
<h4>爬虫账号状态</h4>
<table class="data-table">
    <thead>
        <tr>
            <th>平台</th>
            <th>用户名</th>
            <th>状态</th>
            <th>最后更新</th>
            <th>备注</th>
        </tr>
    </thead>
    <tbody>
        {% for account in crawler_accounts %}
        <tr>
            <td>{{ account.platform_name or account.platform }}</td>
            <td>{{ account.username }}</td>
            <td>
                <span class="status-badge status-{{ account.status_class or 'info' }}">
                    {{ account.status_text or account.status }}
                </span>
            </td>
            <td class="timestamp">{{ account.updated_at or account.last_update }}</td>
            <td>{{ account.remark or account.note or '-' }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}

{% if token_info %}
<h4>Token状态</h4>
<table class="data-table">
    <thead>
        <tr>
            <th>平台</th>
            <th>用户名</th>
            <th>Token状态</th>
            <th>过期时间</th>
            <th>剩余时间</th>
        </tr>
    </thead>
    <tbody>
        {% for token in token_info %}
        <tr>
            <td>{{ token.platform_name or token.platform }}</td>
            <td>{{ token.username }}</td>
            <td>
                <span class="status-badge status-{{ token.status_class or 'info' }}">
                    {{ token.status_text or token.status }}
                </span>
            </td>
            <td class="timestamp">{{ token.expires_at or token.expire_time }}</td>
            <td>
                {% if token.remaining_time %}
                <span style="color: {% if token.is_expiring %}#ff4d4f{% else %}#52c41a{% endif %};">
                    {{ token.remaining_time }}
                </span>
                {% else %}
                -
                {% endif %}
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}

{% if failed_operations %}
<h4>失败操作</h4>
<div class="alert alert-danger">
    <ul style="margin: 0; padding-left: 20px;">
        {% for operation in failed_operations %}
        <li>{{ operation.description or operation }} 
            {% if operation.error %}
            <br><small style="color: #666;">错误: {{ operation.error }}</small>
            {% endif %}
        </li>
        {% endfor %}
    </ul>
</div>
{% endif %}

{% if statistics %}
<h4>统计信息</h4>
<table class="data-table">
    {% for stat in statistics %}
    <tr>
        <td style="font-weight: 600; width: 40%;">{{ stat.name or stat.label }}</td>
        <td>
            {{ stat.value }}
            {% if stat.unit %}{{ stat.unit }}{% endif %}
            {% if stat.change %}
            <small style="color: {% if stat.change > 0 %}#52c41a{% else %}#ff4d4f{% endif %};">
                ({% if stat.change > 0 %}+{% endif %}{{ stat.change }}{% if stat.unit %}{{ stat.unit }}{% endif %})
            </small>
            {% endif %}
        </td>
    </tr>
    {% endfor %}
</table>
{% endif %}

{% if next_actions %}
<h4>建议操作</h4>
<ol>
    {% for action in next_actions %}
    <li>{{ action }}</li>
    {% endfor %}
</ol>
{% endif %}

{% if dashboard_url %}
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ dashboard_url }}" class="btn btn-primary">查看爬虫管理</a>
</div>
{% endif %}

<div class="divider"></div>

<p style="font-size: 14px; color: #666;">
    <strong>通知时间:</strong> {{ notification_time or now().strftime('%Y-%m-%d %H:%M:%S') }}<br>
    <strong>通知类型:</strong> 
    <span class="status-badge status-{{ status_type or 'info' }}">
        {% if status_type == 'success' %}正常{% elif status_type == 'warning' %}警告{% elif status_type == 'danger' %}异常{% else %}信息{% endif %}
    </span><br>
    {% if total_accounts %}
    <strong>总账号数:</strong> {{ total_accounts }}<br>
    {% endif %}
    {% if active_accounts %}
    <strong>可用账号数:</strong> {{ active_accounts }}
    {% endif %}
</p>
{% endblock %}
