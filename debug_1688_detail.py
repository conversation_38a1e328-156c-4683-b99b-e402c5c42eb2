#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试1688商品详情数据结构
"""

import requests
import json
import sys

def debug_1688_detail():
    """调试1688商品详情数据结构"""
    url = "http://localhost:8000/api/v1/upstream/detail"
    data = {
        "product_url": "https://detail.1688.com/offer/932222752479.html",  # 原来的商品
        "language": "zh"
    }
    
    try:
        print("🔍 获取1688商品详情...")
        response = requests.post(url, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            product_data = result.get('data', {})
            
            print("\n📦 商品基本信息:")
            print(f"  - ID: {product_data.get('id')}")
            print(f"  - 名称: {product_data.get('name', '')[:50]}...")
            print(f"  - 主图: {product_data.get('picUrl')}")
            print(f"  - 图片列表数量: {len(product_data.get('sliderPicUrls', []))}")
            
            # 详细分析图片信息
            slider_pics = product_data.get('sliderPicUrls', [])
            print(f"\n🖼️ 图片详情:")
            if slider_pics:
                for i, pic in enumerate(slider_pics[:5]):  # 只显示前5张
                    print(f"  {i+1}. {pic}")
            else:
                print("  ❌ 没有找到图片数据")

            # 检查原始响应数据
            print(f"\n🔍 原始响应检查:")
            print(f"  - 响应状态: {result.get('code')}")
            print(f"  - 响应消息: {result.get('message')}")
            if 'data' in result:
                data_keys = list(result['data'].keys())
                print(f"  - 数据字段: {data_keys}")
                if 'sliderPicUrls' in result['data']:
                    raw_pics = result['data']['sliderPicUrls']
                    print(f"  - 原始图片数据类型: {type(raw_pics)}")
                    print(f"  - 原始图片数据长度: {len(raw_pics) if isinstance(raw_pics, list) else 'N/A'}")
                    if isinstance(raw_pics, list) and raw_pics:
                        print(f"  - 原始图片示例: {raw_pics[0]}")
                else:
                    print("  - ❌ 响应数据中没有sliderPicUrls字段")
            
            # 详细分析SKU信息
            skus = product_data.get('skus', [])
            print(f"\n📋 SKU详情 (共{len(skus)}个):")
            
            # 分析前几个SKU的属性结构
            for i, sku in enumerate(skus[:5]):  # 只分析前5个
                print(f"\n  SKU {i+1}:")
                print(f"    - ID: {sku.get('id')}")
                print(f"    - 价格: {sku.get('price')}分")
                print(f"    - 库存: {sku.get('stock')}")
                print(f"    - 图片: {sku.get('picUrl', '无')}")
                
                properties = sku.get('properties', [])
                print(f"    - 属性数量: {len(properties)}")
                for j, prop in enumerate(properties):
                    print(f"      属性{j+1}: {prop.get('propertyName')}={prop.get('valueName')} (ID:{prop.get('propertyId')}, ValueID:{prop.get('valueId')})")
            
            # 分析属性分布
            print(f"\n📊 属性分析:")
            all_properties = {}
            for sku in skus:
                for prop in sku.get('properties', []):
                    prop_name = prop.get('propertyName')
                    if prop_name not in all_properties:
                        all_properties[prop_name] = set()
                    all_properties[prop_name].add(prop.get('valueName'))
            
            for prop_name, values in all_properties.items():
                print(f"  - {prop_name}: {len(values)}个值 ({', '.join(list(values)[:3])}{'...' if len(values) > 3 else ''})")
            
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = debug_1688_detail()
    sys.exit(0 if success else 1)
