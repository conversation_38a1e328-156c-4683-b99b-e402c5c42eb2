<template>
  <div class="logs-page">
    <div class="page-header">
      <h2>调用日志</h2>
      <div class="header-actions">
        <button @click="exportLogs" class="btn-secondary" :disabled="exporting">
          {{ exporting ? '导出中...' : '📥 导出CSV' }}
        </button>
        <button @click="cleanupLogs" class="btn-secondary">
          🗑️ 清理日志
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <h3>总请求数</h3>
        <div class="stat-value">{{ stats.total_requests }}</div>
      </div>
      <div class="stat-card">
        <h3>成功请求</h3>
        <div class="stat-value">{{ stats.success_requests }}</div>
      </div>
      <div class="stat-card">
        <h3>成功率</h3>
        <div class="stat-value">{{ stats.success_rate }}%</div>
      </div>
      <div class="stat-card">
        <h3>平均响应时间</h3>
        <div class="stat-value">{{ stats.avg_response_time }}ms</div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-item">
        <label>开始日期:</label>
        <input type="date" v-model="filters.start_date" @change="loadLogs">
      </div>
      <div class="filter-item">
        <label>结束日期:</label>
        <input type="date" v-model="filters.end_date" @change="loadLogs">
      </div>
      <div class="filter-item">
        <label>平台:</label>
        <select v-model="filters.platform" @change="loadLogs">
          <option value="">全部平台</option>
          <option value="taobao">淘宝</option>
          <option value="1688">1688</option>
          <option value="pinduoduo">拼多多</option>
          <option value="jingdong">京东</option>
        </select>
      </div>
      <div class="filter-item">
        <label>响应码:</label>
        <select v-model="filters.response_code" @change="loadLogs">
          <option value="">全部</option>
          <option value="200">200 成功</option>
          <option value="400">400 错误请求</option>
          <option value="500">500 服务器错误</option>
        </select>
      </div>
      <div class="filter-item">
        <label>IP地址:</label>
        <input type="text" v-model="filters.client_ip" @input="debounceSearch" placeholder="筛选IP">
      </div>
      <div class="filter-item">
        <button @click="refreshLogs" class="btn-secondary">🔄 刷新</button>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="logs-table">
      <div class="table-header">
        <div class="col">ID</div>
        <div class="col">时间</div>
        <div class="col">IP地址</div>
        <div class="col">方法</div>
        <div class="col">路径</div>
        <div class="col">平台</div>
        <div class="col">搜索关键词</div>
        <div class="col">响应码</div>
        <div class="col">响应时间</div>
        <div class="col">代理</div>
        <div class="col">账号</div>
        <div class="col">错误信息</div>
      </div>

      <div v-if="loading" class="loading">加载中...</div>

      <div v-else-if="logs.length === 0" class="empty">
        暂无日志数据
      </div>

      <div v-else>
        <div v-for="log in logs" :key="log.id" class="table-row" @click="showLogDetail(log)">
          <div class="col">{{ log.id }}</div>
          <div class="col">{{ formatDate(log.created_at) }}</div>
          <div class="col">{{ log.client_ip }}</div>
          <div class="col">
            <span class="method-tag" :class="log.request_method.toLowerCase()">
              {{ log.request_method }}
            </span>
          </div>
          <div class="col">{{ log.request_path }}</div>
          <div class="col">
            <span v-if="log.platform" class="platform-tag" :class="'platform-' + log.platform">
              {{ getPlatformName(log.platform) }}
            </span>
            <span v-else>-</span>
          </div>
          <div class="col">{{ log.search_query || '-' }}</div>
          <div class="col">
            <span class="status-tag" :class="getStatusClass(log.response_code)">
              {{ log.response_code }}
            </span>
          </div>
          <div class="col">{{ log.response_time_ms }}ms</div>
          <div class="col">{{ log.proxy_used || '-' }}</div>
          <div class="col">{{ log.account_used || '-' }}</div>
          <div class="col error-cell">
            <span v-if="log.error_message" class="error-text" :title="log.error_message">
              {{ truncateText(log.error_message, 30) }}
            </span>
            <span v-else>-</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="totalPages > 1">
      <button @click="changePage(currentPage - 1)" :disabled="currentPage === 1">上一页</button>
      <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页 (总计 {{ totalItems }} 条记录)</span>
      <button @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">下一页</button>
    </div>

    <!-- 日志详情模态框 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal detail-modal" @click.stop>
        <div class="modal-header">
          <h3>日志详情 #{{ selectedLog?.id }}</h3>
          <button @click="closeDetailModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div class="detail-grid" v-if="selectedLog">
            <div class="detail-item">
              <label>请求ID:</label>
              <span>{{ selectedLog.request_id }}</span>
            </div>
            <div class="detail-item">
              <label>时间:</label>
              <span>{{ formatDate(selectedLog.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>客户端IP:</label>
              <span>{{ selectedLog.client_ip }}</span>
            </div>
            <div class="detail-item">
              <label>User Agent:</label>
              <span>{{ selectedLog.user_agent || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>请求方法:</label>
              <span>{{ selectedLog.request_method }}</span>
            </div>
            <div class="detail-item">
              <label>请求路径:</label>
              <span>{{ selectedLog.request_path }}</span>
            </div>
            <div class="detail-item">
              <label>平台:</label>
              <span>{{ selectedLog.platform || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>搜索类型:</label>
              <span>{{ selectedLog.search_type || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>搜索关键词:</label>
              <span>{{ selectedLog.search_query || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>响应码:</label>
              <span>{{ selectedLog.response_code }}</span>
            </div>
            <div class="detail-item">
              <label>响应时间:</label>
              <span>{{ selectedLog.response_time_ms }}ms</span>
            </div>
            <div class="detail-item">
              <label>响应大小:</label>
              <span>{{ selectedLog.response_size || '-' }} bytes</span>
            </div>
            <div class="detail-item">
              <label>使用代理:</label>
              <span>{{ selectedLog.proxy_used || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>使用账号:</label>
              <span>{{ selectedLog.account_used || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>翻译服务:</label>
              <span>{{ selectedLog.translation_used || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>错误类型:</label>
              <span>{{ selectedLog.error_type || '-' }}</span>
            </div>
            <div class="detail-item full-width">
              <label>错误信息:</label>
              <div class="error-message">{{ selectedLog.error_message || '-' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import api from '../api'

// 响应式数据
const logs = ref([])
const stats = ref({
  total_requests: 0,
  success_requests: 0,
  success_rate: 0,
  avg_response_time: 0
})
const loading = ref(false)
const exporting = ref(false)
const showDetailModal = ref(false)
const selectedLog = ref(null)
const currentPage = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const pageSize = 50

// 筛选器
const filters = ref({
  start_date: '',
  end_date: '',
  platform: '',
  response_code: '',
  client_ip: '',
  search_query: ''
})

// 搜索防抖
let searchTimeout = null
const debounceSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    loadLogs()
  }, 500)
}

// 平台名称映射
const getPlatformName = (platform) => {
  const names = {
    'taobao': '淘宝',
    '1688': '1688',
    'pinduoduo': '拼多多',
    'jingdong': '京东'
  }
  return names[platform] || platform
}

// 获取状态样式类
const getStatusClass = (code) => {
  if (code >= 200 && code < 300) return 'success'
  if (code >= 400 && code < 500) return 'client-error'
  if (code >= 500) return 'server-error'
  return 'other'
}

// 截断文本
const truncateText = (text, length) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 加载日志列表
const loadLogs = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize
    }

    // 只添加非空的筛选参数
    if (filters.value.start_date) {
      params.start_date = filters.value.start_date
    }
    if (filters.value.end_date) {
      params.end_date = filters.value.end_date
    }
    if (filters.value.platform) {
      params.platform = filters.value.platform
    }
    if (filters.value.response_code) {
      params.response_code = filters.value.response_code
    }
    if (filters.value.client_ip) {
      params.client_ip = filters.value.client_ip
    }
    if (filters.value.search_query) {
      params.search_query = filters.value.search_query
    }

    const response = await api.logs.getAll(params)

    if (response.code === 200) {
      // 检查返回数据格式
      if (response.data.items) {
        // 新的分页格式
        logs.value = response.data.items
        totalItems.value = response.data.total
        totalPages.value = response.data.pages
      } else {
        // 兼容旧格式
        logs.value = response.data
        totalItems.value = response.data.length
        totalPages.value = Math.ceil(response.data.length / pageSize)
      }
    } else {
      logs.value = []
      totalItems.value = 0
      totalPages.value = 1
    }
  } catch (error) {
    console.error('加载日志失败:', error)
    showMessage('错误', '加载日志失败')
    logs.value = []
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await api.logs.getStats()
    if (response.code === 200) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 刷新日志列表
const refreshLogs = () => {
  currentPage.value = 1
  loadLogs()
  loadStats()
}

// 分页
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadLogs()
  }
}

// 显示日志详情
const showLogDetail = (log) => {
  selectedLog.value = log
  showDetailModal.value = true
}

// 关闭详情模态框
const closeDetailModal = () => {
  showDetailModal.value = false
  selectedLog.value = null
}

// 导出日志
const exportLogs = async () => {
  exporting.value = true
  try {
    const params = {
      ...filters.value
    }

    // 创建下载链接
    const queryString = new URLSearchParams(params).toString()
    const url = `${api.defaults.baseURL}/logs/export/csv?${queryString}`

    // 创建临时链接下载
    const link = document.createElement('a')
    link.href = url
    link.download = `api_logs_${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    showMessage('成功', '日志导出成功')
  } catch (error) {
    console.error('导出日志失败:', error)
    showMessage('错误', '导出日志失败')
  } finally {
    exporting.value = false
  }
}

// 清理日志
const cleanupLogs = async () => {
  const days = prompt('请输入要保留的天数（删除更早的日志）:', '30')
  if (!days || isNaN(days) || days < 1) {
    return
  }

  if (!confirm(`确定要删除 ${days} 天前的日志吗？此操作不可恢复。`)) {
    return
  }

  try {
    const response = await api.delete(`/logs/cleanup?days=${days}`)
    showMessage('成功', response.message)
    loadLogs()
    loadStats()
  } catch (error) {
    console.error('清理日志失败:', error)
    showMessage('错误', '清理日志失败')
  }
}

// 简单的消息提示
const showMessage = (type, text) => {
  alert(`${type}: ${text}`)
}

// 页面加载时获取数据
onMounted(() => {
  // 设置默认日期范围（最近7天）
  const today = new Date()
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

  filters.value.end_date = today.toISOString().split('T')[0]
  filters.value.start_date = weekAgo.toISOString().split('T')[0]

  loadLogs()
  loadStats()
})
</script>

<style scoped>
.logs-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  font-weight: normal;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.filter-item input,
.filter-item select {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.logs-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  overflow-x: auto;
}

.table-header {
  display: grid;
  grid-template-columns: 50px 120px 100px 50px 160px 70px 120px 70px 80px 80px 80px 160px;
  background: #f5f5f5;
  border-bottom: 1px solid #eee;
  font-weight: bold;
  color: #333;
  min-width: 1000px;
}

.table-row {
  display: grid;
  grid-template-columns: 50px 120px 100px 50px 160px 70px 120px 70px 80px 80px 80px 160px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  min-width: 1000px;
}

.table-row:hover {
  background: #f9f9f9;
}

.col {
  padding: 12px 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.method-tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.method-tag.get {
  background: #e6f7ff;
  color: #1890ff;
}

.method-tag.post {
  background: #f6ffed;
  color: #52c41a;
}

.method-tag.put {
  background: #fff7e6;
  color: #fa8c16;
}

.method-tag.delete {
  background: #fff2f0;
  color: #ff4d4f;
}

.platform-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.platform-tag.platform-taobao {
  background: #ff6900;
  color: white;
}

.platform-tag.platform-1688 {
  background: #ff6600;
  color: white;
}

.platform-tag.platform-pinduoduo {
  background: #e02e24;
  color: white;
}

.platform-tag.platform-jingdong {
  background: #e3101e;
  color: white;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.status-tag.success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-tag.client-error {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-tag.server-error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-tag.other {
  background: #f5f5f5;
  color: #999;
  border: 1px solid #d9d9d9;
}

.error-cell {
  color: #ff4d4f;
}

.error-text {
  cursor: help;
}

.btn-primary {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary:hover {
  background: #40a9ff;
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  padding: 8px 16px;
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-secondary:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

.btn-secondary:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

.loading, .empty {
  text-align: center;
  padding: 40px;
  color: #666;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.pagination button:hover {
  background: #f5f5f5;
}

.pagination button:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 800px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

.detail-modal {
  width: 900px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #999;
  padding: 4px;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.detail-item span {
  font-size: 14px;
  color: #333;
  word-break: break-all;
}

.error-message {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 12px;
  color: #ff4d4f;
  font-family: monospace;
  font-size: 13px;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
