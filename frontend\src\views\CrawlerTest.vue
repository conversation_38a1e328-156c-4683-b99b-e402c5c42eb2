<template>
  <div class="crawler-test-page">
    <h2>爬虫功能测试</h2>
    
    <div class="test-form">
      <div class="form-group">
        <label>搜索关键词：</label>
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="请输入搜索关键词"
          @keyup.enter="testCrawler"
        />
      </div>
      
      <div class="form-group">
        <label>选择平台：</label>
        <select v-model="selectedPlatform">
          <option value="taobao">淘宝</option>
          <option value="1688">1688</option>
          <option value="pinduoduo">拼多多</option>
          <option value="jingdong">京东</option>
        </select>
      </div>
      
      <button 
        @click="testCrawler" 
        :disabled="loading || !searchQuery"
        class="test-button"
      >
        {{ loading ? '搜索中...' : '开始搜索' }}
      </button>
    </div>
    
    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <p>正在搜索商品，请稍候...</p>
    </div>
    
    <div v-if="error" class="error">
      <h3>搜索失败</h3>
      <p>{{ error }}</p>
    </div>
    
    <div v-if="results" class="results">
      <h3>搜索结果</h3>
      <div class="result-info">
        <p>平台: {{ results.platform }}</p>
        <p>查询: {{ results.query }}</p>
        <p>找到商品: {{ results.total }} 个</p>
        <p v-if="results.response_time">响应时间: {{ results.response_time.toFixed(2) }}秒</p>
        <p v-if="results.error" class="error-text">错误: {{ results.error }}</p>
      </div>
      
      <div v-if="(results.products && results.products.length > 0) || (results.list && results.list.length > 0)" class="products">
        <div
          v-for="(product, index) in (results.list || results.products)"
          :key="product.id || index"
          class="product-card"
        >
          <div class="product-image">
            <img
              :src="product.picUrl || product.image || 'https://via.placeholder.com/150x150'"
              :alt="product.name || product.title"
              @error="handleImageError"
            />
          </div>
          <div class="product-info">
            <h4 class="product-title">{{ product.name || product.title }}</h4>
            <p class="product-price">
              <span v-if="product.price && typeof product.price === 'number'">
                ¥{{ (product.price / 100).toFixed(2) }}
                <span v-if="product.marketPrice && product.marketPrice !== product.price" class="market-price">
                  ¥{{ (product.marketPrice / 100).toFixed(2) }}
                </span>
              </span>
              <span v-else>{{ product.price }}</span>
            </p>
            <p v-if="product.shopName || product.shop_name" class="product-shop">
              店铺: {{ product.shopName || product.shop_name }}
            </p>
            <p v-if="product.salesCount || product.sales" class="product-sales">
              销量: {{ product.salesCount || product.sales }}
            </p>
            <p v-if="product.location" class="product-location">发货地: {{ product.location }}</p>

            <!-- 新增标签显示 -->
            <div v-if="product.hot || product.newest || product.sale || product.specType" class="product-tags">
              <span v-if="product.hot" class="tag hot">热销</span>
              <span v-if="product.newest" class="tag newest">新品</span>
              <span v-if="product.sale" class="tag sale">促销</span>
              <span v-if="product.specType" class="tag spec">多规格</span>
            </div>

            <div class="product-actions">
              <a
                :href="product.productLink || product.link"
                target="_blank"
                class="product-link"
              >
                直接打开
              </a>
              <button
                @click="viewProductDetail(product)"
                class="detail-button"
                :disabled="detailLoading"
              >
                {{ detailLoading ? '获取中...' : '查看详情' }}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else-if="results.total === 0" class="no-results">
        <p>未找到相关商品</p>
      </div>
    </div>

    <!-- 商品详情弹窗 -->
    <div v-if="showDetailModal" class="modal-overlay" @click="closeDetailModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>商品详情</h3>
          <button @click="closeDetailModal" class="close-button">&times;</button>
        </div>

        <div v-if="detailLoading" class="modal-loading">
          <div class="spinner"></div>
          <p>正在获取商品详情...</p>
        </div>

        <div v-if="detailError" class="modal-error">
          <p>{{ detailError }}</p>
          <button @click="retryGetDetail" class="retry-button">重试</button>
        </div>

        <div v-if="productDetail" class="modal-detail">
          <!-- 基本信息 -->
          <div class="detail-basic">
            <div class="detail-image">
              <img
                :src="productDetail.picUrl || productDetail.image"
                :alt="productDetail.name || productDetail.title"
                @error="handleImageError"
              />
            </div>
            <div class="detail-info">
              <h4>{{ productDetail.name || productDetail.title }}</h4>
              <p class="detail-price">
                价格: ¥{{ (productDetail.price / 100).toFixed(2) }}
                <span v-if="productDetail.marketPrice && productDetail.marketPrice !== productDetail.price">
                  - ¥{{ (productDetail.marketPrice / 100).toFixed(2) }}
                </span>
              </p>
              <p v-if="productDetail.shopName" class="detail-shop">店铺: {{ productDetail.shopName }}</p>
              <p v-if="productDetail.salesCount" class="detail-sales">销量: {{ productDetail.salesCount }}</p>
              <p v-if="productDetail.stock" class="detail-stock">库存: {{ productDetail.stock.toLocaleString() }}</p>
              <p v-if="productDetail.freight" class="detail-freight">运费: ¥{{ (productDetail.freight / 100).toFixed(2) }}</p>
            </div>
          </div>

          <!-- SKU信息 -->
          <div v-if="productDetail.skus && productDetail.skus.length > 0" class="detail-skus">
            <h5>商品规格 ({{ productDetail.skus.length }} 个)</h5>
            <div class="sku-list">
              <div
                v-for="(sku, index) in productDetail.skus.slice(0, 6)"
                :key="sku.id"
                class="sku-item"
              >
                <div class="sku-properties">
                  <span
                    v-for="prop in sku.properties"
                    :key="prop.propertyId"
                    class="sku-prop"
                  >
                    {{ prop.propertyName }}: {{ prop.valueName }}
                  </span>
                </div>
                <div class="sku-price">¥{{ (sku.price / 100).toFixed(2) }}</div>
                <div class="sku-stock">库存: {{ sku.stock.toLocaleString() }}</div>
              </div>
              <div v-if="productDetail.skus.length > 6" class="sku-more">
                还有 {{ productDetail.skus.length - 6 }} 个规格...
              </div>
            </div>
          </div>

          <!-- 商品图片 -->
          <div v-if="productDetail.sliderPicUrls && productDetail.sliderPicUrls.length > 1" class="detail-images">
            <h5>商品图片</h5>
            <div class="image-gallery">
              <img
                v-for="(image, index) in productDetail.sliderPicUrls.slice(0, 8)"
                :key="index"
                :src="image"
                :alt="`商品图片${index + 1}`"
                @error="handleImageError"
                class="gallery-image"
              />
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="detail-actions">
            <a
              :href="productDetail.sourceLink"
              target="_blank"
              class="view-original-button"
            >
              查看原商品
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import api from '../api'

const searchQuery = ref('手机')
const selectedPlatform = ref('taobao')
const loading = ref(false)
const results = ref(null)
const error = ref('')

// 商品详情相关状态
const showDetailModal = ref(false)
const detailLoading = ref(false)
const detailError = ref('')
const productDetail = ref(null)
const currentProduct = ref(null)

const testCrawler = async () => {
  if (!searchQuery.value.trim()) {
    error.value = '请输入搜索关键词'
    return
  }
  
  loading.value = true
  error.value = ''
  results.value = null
  
  try {
    const response = await api.search.products({
      query: searchQuery.value.trim(),
      platform: selectedPlatform.value
    })
    
    if (response.code === 200) {
      results.value = response.data
    } else {
      error.value = response.message || '搜索失败'
    }
    
  } catch (err) {
    console.error('搜索错误:', err)
    error.value = err.response?.data?.message || err.message || '网络错误'
  } finally {
    loading.value = false
  }
}

const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/150x150?text=图片加载失败'
}

// 查看商品详情
const viewProductDetail = async (product) => {
  try {
    console.log('查看商品详情:', product)

    currentProduct.value = product
    showDetailModal.value = true
    detailLoading.value = true
    detailError.value = ''
    productDetail.value = null

    // 构建请求数据
    const requestData = {
      url: product.productLink || product.link || product.detail_url,
      platform: selectedPlatform.value
    }

    console.log('请求商品详情:', requestData)

    // 根据平台选择API
    let response
    if (selectedPlatform.value === '1688') {
      response = await api.product.detail1688(requestData)
    } else {
      response = await api.product.detailByPlatform(requestData)
    }

    console.log('商品详情响应:', response)

    if (response.code === 200) {
      productDetail.value = response.data
      console.log('商品详情获取成功:', productDetail.value)
    } else {
      detailError.value = response.message || '获取商品详情失败'
      console.error('商品详情获取失败:', detailError.value)
    }

  } catch (err) {
    console.error('获取商品详情错误:', err)
    detailError.value = err.response?.data?.message || err.message || '网络错误'
  } finally {
    detailLoading.value = false
  }
}

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  productDetail.value = null
  detailError.value = ''
  currentProduct.value = null
}

// 重试获取详情
const retryGetDetail = () => {
  if (currentProduct.value) {
    viewProductDetail(currentProduct.value)
  }
}
</script>

<style scoped>
.crawler-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  width: 100%;
  max-width: 300px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.test-button {
  background: #1890ff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.test-button:hover:not(:disabled) {
  background: #40a9ff;
}

.test-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  padding: 15px;
  border-radius: 4px;
  color: #a8071a;
}

.error-text {
  color: #a8071a;
}

.results {
  margin-top: 20px;
}

.result-info {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.result-info p {
  margin: 5px 0;
}

.products {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.product-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.product-image {
  text-align: center;
  margin-bottom: 10px;
}

.product-image img {
  max-width: 150px;
  max-height: 150px;
  object-fit: cover;
  border-radius: 4px;
}

.product-title {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  font-size: 18px;
  color: #ff4d4f;
  font-weight: bold;
  margin: 8px 0;
}

.market-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
  margin-left: 8px;
}

.product-tags {
  margin: 8px 0;
}

.tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  margin-right: 4px;
  margin-bottom: 2px;
}

.tag.hot {
  background: #ff4d4f;
  color: white;
}

.tag.newest {
  background: #52c41a;
  color: white;
}

.tag.sale {
  background: #fa8c16;
  color: white;
}

.tag.spec {
  background: #1890ff;
  color: white;
}

.product-shop,
.product-sales,
.product-location {
  font-size: 12px;
  color: #666;
  margin: 4px 0;
}

.product-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.product-link {
  display: inline-block;
  background: #1890ff;
  color: white;
  padding: 6px 12px;
  text-decoration: none;
  border-radius: 4px;
  font-size: 12px;
  flex: 1;
  text-align: center;
}

.product-link:hover {
  background: #40a9ff;
}

.detail-button {
  padding: 6px 12px;
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  flex: 1;
}

.detail-button:hover {
  background: #73d13d;
}

.detail-button:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.no-results {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* 商品详情弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 800px;
  max-height: 90vh;
  width: 90%;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #666;
}

.modal-loading {
  text-align: center;
  padding: 40px;
}

.modal-error {
  text-align: center;
  padding: 40px;
  color: #ff4d4f;
}

.retry-button {
  margin-top: 16px;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.modal-detail {
  padding: 20px;
}

.detail-basic {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-image img {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.detail-info {
  flex: 1;
}

.detail-info h4 {
  margin: 0 0 12px 0;
  font-size: 18px;
  line-height: 1.4;
}

.detail-price {
  font-size: 20px;
  color: #ff4d4f;
  font-weight: bold;
  margin: 8px 0;
}

.detail-shop,
.detail-sales,
.detail-stock,
.detail-freight {
  margin: 6px 0;
  color: #666;
}

.detail-skus {
  margin-bottom: 20px;
}

.detail-skus h5 {
  margin: 0 0 12px 0;
  font-size: 16px;
}

.sku-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
}

.sku-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.sku-properties {
  margin-bottom: 8px;
}

.sku-prop {
  display: inline-block;
  background: #e6f7ff;
  color: #1890ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  margin-right: 6px;
  margin-bottom: 4px;
}

.sku-price {
  font-weight: bold;
  color: #ff4d4f;
  margin-bottom: 4px;
}

.sku-stock {
  font-size: 12px;
  color: #666;
}

.sku-more {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

.detail-images {
  margin-bottom: 20px;
}

.detail-images h5 {
  margin: 0 0 12px 0;
  font-size: 16px;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
}

.gallery-image {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.detail-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.view-original-button {
  display: inline-block;
  padding: 10px 20px;
  background: #1890ff;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  font-size: 14px;
}

.view-original-button:hover {
  background: #40a9ff;
}
</style>
