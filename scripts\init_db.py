#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "backend"))

def init_database():
    """初始化数据库"""
    try:
        print("🗄️ 开始初始化数据库...")
        
        # 导入数据库相关模块
        from app.database import init_database, test_database_connection
        
        # 测试数据库连接
        if not test_database_connection():
            print("❌ 数据库连接失败，请检查配置")
            return False
        
        # 创建所有表
        init_database()
        
        print("✅ 数据库表创建完成，跳过初始数据插入")
        
        print("✅ 数据库初始化完成!")
        print("\n📋 数据库信息:")
        print("- 默认管理员: admin/admin123")
        print("- 翻译服务: Google翻译(已启用), 百度翻译(已禁用)")
        print("- 系统配置: 已设置默认参数")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = init_database()
    if not success:
        sys.exit(1)
