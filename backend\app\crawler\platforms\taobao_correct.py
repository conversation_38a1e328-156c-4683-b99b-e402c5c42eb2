"""
基于参考代码的正确淘宝爬虫实现
使用正确的API地址和签名算法
"""
import requests
import hashlib
import json
import time
import re
import urllib.parse
from typing import Dict, List, Any
from datetime import datetime


class TaobaoCorrectCrawler:
    """基于参考代码的正确淘宝爬虫"""
    
    def __init__(self):
        self.default_headers = {
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        # 正确的API地址（从参考代码获取）
        self.api_url = 'https://h5api.m.taobao.com/h5/mtop.relationrecommend.wirelessrecommend.recommend/2.0/'
        
    def get_sign(self, eT, page, bc_offset, nt_offset, totalResults, sourceS, keywordCode, em_token):
        """获取加密sign参数（完全按照参考代码实现）"""
        eC = '12574478'
        params = {
            "device": "HMA-AL00",
            "isBeta": "false",
            "grayHair": "false",
            "from": "nt_history",
            "brand": "HUAWEI",
            "info": "wifi",
            "index": "4",
            "rainbow": "",
            "schemaType": "auction",
            "elderHome": "false",
            "isEnterSrpSearch": "true",
            "newSearch": "false",
            "network": "wifi",
            "subtype": "",
            "hasPreposeFilter": "false",
            "prepositionVersion": "v2",
            "client_os": "Android",
            "gpsEnabled": "false",
            "searchDoorFrom": "srp",
            "debug_rerankNewOpenCard": "false",
            "homePageVersion": "v7",
            "searchElderHomeOpen": "false",
            "search_action": "initiative",
            "sugg": "_4_1",
            "sversion": "13.6",
            "style": "list",
            "ttid": "600000@taobao_pc_10.7.0",
            "needTabs": "true",
            "areaCode": "CN",
            "vm": "nw",
            "countryNum": "156",
            "m": "pc",
            "page": page,
            "n": 48,
            "q": keywordCode,
            "qSource": "url",
            "pageSource": "a21bo.jianhua/a.search_manual.0",
            "channelSrp": "",
            "tab": "all",
            "pageSize": "48",
            "totalPage": "100",
            "totalResults": totalResults,
            "sourceS": sourceS,
            "sort": "_coefp",
            "bcoffset": bc_offset,
            "ntoffset": nt_offset,
            "filterTag": "",
            "service": "",
            "prop": "",
            "loc": "",
            "start_price": None,
            "end_price": None,
            "startPrice": None,
            "endPrice": None,
            "categoryp": "",
            "ha3Kvpairs": None,
            "couponFilter": 0,
            "myCNA": "SLjKIBh/WQwCAXBRjy6Kt+R5",  # 这个可以从cookie中提取
            "screenResolution": "2560x1440",
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "couponUnikey": "",
            "subTabId": "",
            "np": ""
        }
        
        ep = {
            "appId": "34385",
            "params": json.dumps(params, separators=(',', ':'), ensure_ascii=False)
        }

        ep_data = json.dumps(ep, separators=(',', ':'), ensure_ascii=False)
        string = em_token + "&" + str(eT) + "&" + eC + "&" + ep_data  
        MD5 = hashlib.md5()
        MD5.update(string.encode('utf-8'))
        sign = MD5.hexdigest()
        
        print(f"🔐 生成签名: {sign}")
        return sign, ep_data

    def is_valid_product(self, item):
        """验证是否为有效的商品数据"""
        if not isinstance(item, dict):
            return False

        # 检查是否为推荐查询等非商品数据
        if item.get('customCardType'):
            return False

        # 检查是否为推荐查询列表
        if 'recommendQueryItemList' in item:
            return False

        # 检查必要的商品字段
        required_fields = ['title', 'price', 'item_id']
        for field in required_fields:
            if field not in item:
                return False

        # 检查title是否为空
        if not item.get('title', '').strip():
            return False

        # 检查price是否有效
        price = item.get('price', '')
        if not price or price == '0':
            return False

        return True

    def get_content(self, page, bc_offset, nt_offset, totalResults, sourceS, keyword, cookie, em_token):
        """发送请求获取内容（完全按照参考代码实现）"""
        try:
            # 使用传入的cookie
            headers = {
                'cookie': cookie,
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            # 获取当前时间戳
            eT = int(time.time() * 1000)
            keywordCode = urllib.parse.quote(keyword)
            
            print(f"🔍 搜索关键词: {keyword}")
            print(f"🔍 编码后关键词: {keywordCode}")
            print(f"🔍 时间戳: {eT}")
            
            # 获取加密参数
            sign, ep_data = self.get_sign(eT, page, bc_offset, nt_offset, totalResults, sourceS, keywordCode, em_token)
            
            # 查询参数（完全按照参考代码）
            data = {
                'jsv': '2.7.4',
                'appKey': '12574478',
                't': eT,
                'sign': sign,
                'api': 'mtop.relationrecommend.wirelessrecommend.recommend',
                'v': '2.0',
                'timeout': '10000',
                'type': 'jsonp',
                'dataType': 'jsonp',
                'callback': 'mtopjsonp41',
                'data': ep_data
            }
            
            print(f"📡 请求URL: {self.api_url}")
            # print(f"📡 请求参数: {data}")
            
            response = requests.get(url=self.api_url, headers=headers, params=data, timeout=15)
            
            print(f"📡 响应状态码: {response.status_code}")
            print(f"📡 响应内容长度: {len(response.text)}")
            print(f"📡 响应前500字符: {response.text[:500]}")
            
            if response.status_code != 200:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                return []
            
            text = response.text
            
            # 提取json字符串数据（按照参考代码）
            json_matches = re.findall(r'mtopjsonp\d+\((.*)', text)
            if not json_matches:
                print("❌ 未找到JSON数据")
                return []
            
            json_str = json_matches[0][:-1]
            print(f"📡 提取的JSON字符串前200字符: {json_str[:200]}")
            
            # 把json字符串数据, 转成json字典
            json_data = json.loads(json_str)
            print(f"📡 JSON数据顶级键: {list(json_data.keys())}")
            
            # 检查是否有错误
            if 'ret' in json_data and json_data['ret']:
                ret_messages = json_data['ret']
                print(f"📡 API返回状态: {ret_messages}")
                # 只有在真正的错误时才返回空结果
                if any('FAIL' in msg or 'ERROR' in msg for msg in ret_messages):
                    print(f"❌ API返回错误: {ret_messages}")
                    return []
                else:
                    print(f"✅ API调用成功: {ret_messages}")
            
            # 字典取值: 提取商品信息所在列表（按照参考代码）
            if 'data' not in json_data:
                print("❌ 响应中没有data字段")
                return []
            
            data_section = json_data['data']
            print(f"📡 data字段类型: {type(data_section)}")
            if isinstance(data_section, dict):
                print(f"📡 data中的顶级字段: {list(data_section.keys())[:10]}...")  # 只显示前10个字段

                # 尝试查找商品数据的不同可能字段名
                possible_items_fields = ['itemsArray', 'items', 'mods', 'mainResults', 'auctions']
                items_field = None
                items_data = None

                for field in possible_items_fields:
                    if field in data_section:
                        items_field = field
                        items_data = data_section[field]
                        print(f"✅ 找到商品字段: {field}, 类型: {type(items_data)}")
                        break

                if not items_field:
                    print(f"❌ 未找到商品数据字段，尝试查找嵌套结构...")
                    # 尝试查找嵌套的商品数据
                    for key, value in data_section.items():
                        if isinstance(value, dict):
                            for sub_key in possible_items_fields:
                                if sub_key in value:
                                    items_field = f"{key}.{sub_key}"
                                    items_data = value[sub_key]
                                    print(f"✅ 找到嵌套商品字段: {items_field}, 类型: {type(items_data)}")
                                    break
                            if items_field:
                                break

                if not items_field:
                    print(f"❌ 完全未找到商品数据")
                    return []
            else:
                print(f"❌ data字段不是字典类型: {type(data_section)}")
                return []
            
            # 使用找到的商品数据
            if isinstance(items_data, list):
                itemsArray = items_data
            elif isinstance(items_data, dict) and 'data' in items_data:
                itemsArray = items_data['data']
            else:
                itemsArray = items_data

            print(f"✅ 找到商品数组，长度: {len(itemsArray) if isinstance(itemsArray, list) else '非列表类型'}")
            
            # 解析商品数据
            products = []
            for i, index in enumerate(itemsArray):
                try:
                    # 过滤非商品数据
                    if not self.is_valid_product(index):
                        print(f"⚠️ 跳过非商品数据 {i+1}: {index.get('customCardType', 'unknown')}")
                        continue

                    # 处理地区数据信息（按照参考代码）
                    procity = index.get('procity', '')
                    pro_city = procity.split(' ') if procity else ['', '']
                    
                    if len(pro_city) == 2:
                        province = pro_city[0]
                        city = pro_city[1]
                    else:
                        province = pro_city[0] if pro_city else ''
                        city = pro_city[-1] if len(pro_city) > 1 else ''
                    
                    # 提取每个商品具体的数据内容（按照上游系统格式）
                    title = index.get('title', '').replace('<span class=H>', '').replace('</span>', '')
                    price_text = index.get('price', '0')
                    sales_text = index.get('realSales', '').replace('人付款', '')
                    nick = index.get('nick', '')
                    auction_url = index.get('auctionURL', '')
                    pic_url = index.get('pic_path', '') or index.get('pic_url', '')

                    # 解析价格（转换为分）
                    price_value = 0
                    market_price_value = 0
                    try:
                        if price_text and price_text != '0':
                            price_numbers = re.findall(r'[\d.]+', str(price_text))
                            if price_numbers:
                                price_value = int(float(price_numbers[0]) * 100)
                                market_price_value = int(float(price_numbers[-1]) * 100) if len(price_numbers) > 1 else price_value
                    except:
                        price_value = market_price_value = 0

                    # 解析销量
                    sales_count = 0
                    try:
                        if sales_text:
                            sales_numbers = re.findall(r'\d+', sales_text)
                            if sales_numbers:
                                sales_count = int(sales_numbers[0])
                    except:
                        sales_count = 0

                    # 构建商品链接
                    product_link = 'https:' + auction_url if auction_url else ''

                    # 从商品链接中提取真实的商品ID
                    product_id = self._extract_product_id_from_url(product_link)

                    # 构建店铺链接
                    shop_link = f"https://shop.taobao.com/shop/view_shop.htm?user_number_id={nick}" if nick else ""

                    # 生成关键词
                    keywords = self._generate_keywords_from_title(title)

                    # 构建上游系统格式的商品数据
                    product = {
                        'id': product_id,  # 使用从链接提取的真实商品ID
                        'name': title,
                        'introduction': title[:100] + "..." if len(title) > 100 else title,
                        'categoryId': 0,  # 默认分类ID
                        'picUrl': pic_url,
                        'sliderPicUrls': [pic_url] if pic_url else [],
                        'specType': False,  # 默认无规格
                        'price': price_value,
                        'marketPrice': market_price_value,
                        'stock': 999999,  # 默认库存
                        'salesCount': sales_count,
                        'scores': 4,  # 默认评分
                        'newest': False,
                        'sale': sales_count > 100,  # 销量大于100认为在售
                        'hot': sales_count > 1000,  # 销量大于1000认为热销

                        # 扩展字段
                        'productLink': product_link,
                        'shopLink': shop_link,
                        'shopName': nick,
                        'location': f"{province} {city}".strip(),
                        'province': province,
                        'city': city,
                        'platform': 'taobao',
                        'keyword': keywords,
                        'crawlTime': datetime.now().isoformat(),
                        'searchKeyword': keyword,

                        # 兼容旧格式字段（不覆盖上游格式字段）
                        'title': title,
                        'priceText': f"¥{price_text}",  # 原始价格文本
                        'sales': sales_text,
                        'shop_name': nick,
                        'link': product_link,
                        'image': pic_url
                    }
                    
                    products.append(product)
                    print(f"✅ 解析商品 {i+1}: {product['title'][:50]}...")
                    
                except Exception as e:
                    print(f"❌ 解析商品 {i+1} 失败: {str(e)}")
                    continue
            
            return products
            
        except Exception as e:
            print(f"❌ 获取内容失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误: {traceback.format_exc()}")
            return []

    async def search_products(self, query: str, account_username: str = None) -> Dict[str, Any]:
        """搜索商品主方法"""
        try:
            print(f"🔍 淘宝正确爬虫开始搜索: {query}")
            
            # 从数据库获取账号配置
            from app.services.config_service import get_config_service
            from app.database import get_db
            from app.models import CrawlerAccount, Platform
            from sqlalchemy import and_

            db = next(get_db())

            # 直接查询有完整配置的账号
            platform = db.query(Platform).filter(Platform.code == 'taobao').first()
            if not platform:
                print("❌ 未找到淘宝平台配置")
                return {
                    "platform": "taobao",
                    "query": query,
                    "total": 0,
                    "products": [],
                    "response_time": 0,
                    "account_used": account_username,
                    "crawl_method": "无平台配置",
                    "error": "未找到淘宝平台配置"
                }

            # 查询有完整配置的账号（优先选择Cookie长度大于100的）
            accounts = db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.platform_id == platform.id,
                    CrawlerAccount.status == 'active'
                )
            ).all()

            # 筛选有完整配置的账号
            valid_accounts = []
            for acc in accounts:
                if acc.cookie and len(acc.cookie) > 100 and acc.token and len(acc.token) > 10:
                    valid_accounts.append(acc)
                    print(f"✅ 找到有效账号: {acc.username}, Cookie长度: {len(acc.cookie)}")

            if not valid_accounts:
                print("❌ 未找到有完整配置的淘宝账号")
                return {
                    "platform": "taobao",
                    "query": query,
                    "total": 0,
                    "products": [],
                    "response_time": 0,
                    "account_used": account_username,
                    "crawl_method": "无完整配置",
                    "error": "未找到有完整配置的淘宝账号"
                }

            # 选择第一个有效账号
            account = valid_accounts[0]

            print(f"✅ 使用账号: {account.username}")
            print(f"🔐 Token: {account.token}")
            print(f"🍪 Cookie长度: {len(account.cookie)}")
            print(f"🍪 Cookie前100字符: {account.cookie[:100]}...")

            # 更新账号使用统计（记录开始使用）
            from app.services.config_service import get_config_service
            config_service = get_config_service(db)
            config_service._update_account_usage(account)
            print(f"📊 已更新账号使用统计: {account.username}")

            # 检查账号配置的完整性
            if not account.cookie or len(account.cookie) < 100:
                print("❌ Cookie配置不完整，跳过此账号")
                return {
                    "platform": "taobao",
                    "query": query,
                    "total": 0,
                    "products": [],
                    "response_time": 0,
                    "account_used": account.username,
                    "crawl_method": "Cookie不完整",
                    "error": "Cookie配置不完整"
                }

            if not account.token or len(account.token) < 10:
                print("❌ Token配置不完整，跳过此账号")
                return {
                    "platform": "taobao",
                    "query": query,
                    "total": 0,
                    "products": [],
                    "response_time": 0,
                    "account_used": account.username,
                    "crawl_method": "Token不完整",
                    "error": "Token配置不完整"
                }
            
            # 使用参考代码的默认参数
            bc_offset = ''
            nt_offset = ''
            totalResults = 4800
            sourceS = '0'
            page = 1
            
            # 调用获取内容方法
            products = self.get_content(
                page=page,
                bc_offset=bc_offset,
                nt_offset=nt_offset,
                totalResults=totalResults,
                sourceS=sourceS,
                keyword=query,
                cookie=account.cookie,
                em_token=account.token
            )
            
            # 更新账号使用结果
            from app.services.config_service import get_config_service
            config_service = get_config_service(db)
            success = len(products) > 0
            error_msg = None if success else "未获取到商品数据"
            config_service.update_account_result(account, success, error_msg)
            
            return {
                "platform": "taobao",
                "query": query,
                "total": len(products),
                "products": products,
                "response_time": 2.0,
                "account_used": account.username,
                "crawl_method": "淘宝正确API"
            }
            
        except Exception as e:
            print(f"❌ 淘宝正确爬虫搜索失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误: {traceback.format_exc()}")
            
            return {
                "platform": "taobao",
                "query": query,
                "total": 0,
                "products": [],
                "response_time": 0,
                "account_used": account_username,
                "crawl_method": "失败",
                "error": str(e)
            }

    def _extract_product_id_from_url(self, product_url: str) -> int:
        """从商品链接中提取商品ID"""
        if not product_url:
            return 0

        try:
            # 匹配淘宝/天猫商品ID的正则表达式
            # 支持格式：id=********* 或 item.htm?id=*********
            id_match = re.search(r'[?&]id=(\d+)', product_url)
            if id_match:
                return int(id_match.group(1))

            # 如果没有找到ID，返回0
            print(f"⚠️ 无法从URL中提取商品ID: {product_url}")
            return 0

        except Exception as e:
            print(f"❌ 提取商品ID失败: {str(e)}")
            return 0

    def _generate_keywords_from_title(self, title: str) -> str:
        """从标题生成关键词"""
        if not title:
            return "淘宝,商品"

        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', title)
        keywords = [word for word in words if len(word) >= 2]

        return ','.join(keywords[:10]) if keywords else "淘宝,商品"

    def format_search_results_for_upstream(self, products: List[Dict], query: str, total: int) -> Dict[str, Any]:
        """格式化搜索结果为上游系统格式"""
        try:
            formatted_products = []

            for product in products:
                # 从商品数据中提取价格信息（确保为数值型，单位为分）
                price_value = product.get('price', 0)
                market_price_value = product.get('marketPrice', 0)

                # 确保价格为整数（分）
                if isinstance(price_value, str):
                    try:
                        # 如果是字符串，尝试解析为数值
                        price_numbers = re.findall(r'[\d.]+', price_value)
                        if price_numbers:
                            price_value = int(float(price_numbers[0]) * 100)
                        else:
                            price_value = 0
                    except:
                        price_value = 0
                elif isinstance(price_value, float):
                    price_value = int(price_value)
                elif not isinstance(price_value, int):
                    price_value = 0

                # 确保市场价格为整数（分）
                if isinstance(market_price_value, str):
                    try:
                        price_numbers = re.findall(r'[\d.]+', market_price_value)
                        if price_numbers:
                            market_price_value = int(float(price_numbers[0]) * 100)
                        else:
                            market_price_value = price_value
                    except:
                        market_price_value = price_value
                elif isinstance(market_price_value, float):
                    market_price_value = int(market_price_value)
                elif not isinstance(market_price_value, int):
                    market_price_value = price_value

                # 确保所有必需字段都存在
                formatted_product = {
                    'id': product.get('id', 0),
                    'name': product.get('name', ''),
                    'introduction': product.get('introduction', ''),
                    'categoryId': product.get('categoryId', 0),
                    'picUrl': product.get('picUrl', ''),
                    'sliderPicUrls': product.get('sliderPicUrls', []),
                    'specType': product.get('specType', False),
                    'price': price_value,  # 使用数值格式的价格（分）
                    'marketPrice': market_price_value,  # 使用数值格式的市场价格（分）
                    'stock': product.get('stock', 0),
                    'salesCount': product.get('salesCount', 0),
                    'scores': product.get('scores', 4),
                    'newest': product.get('newest', False),
                    'sale': product.get('sale', False),
                    'hot': product.get('hot', False),

                    # 扩展字段
                    'productLink': product.get('productLink', ''),
                    'shopLink': product.get('shopLink', ''),
                    'shopName': product.get('shopName', ''),
                    'location': product.get('location', ''),
                    'platform': product.get('platform', 'taobao'),
                    'keyword': product.get('keyword', ''),
                }

                formatted_products.append(formatted_product)

            return {
                'list': formatted_products,
                'total': total,
                'platform': 'taobao',
                'query': query,
                'success': True
            }

        except Exception as e:
            print(f"❌ 格式化搜索结果失败: {str(e)}")
            return {
                'list': [],
                'total': 0,
                'platform': 'taobao',
                'query': query,
                'success': False,
                'error': str(e)
            }


# 创建全局实例
_taobao_correct_crawler = None

def get_taobao_correct_crawler():
    """获取淘宝正确爬虫实例"""
    global _taobao_correct_crawler
    if _taobao_correct_crawler is None:
        _taobao_correct_crawler = TaobaoCorrectCrawler()
    return _taobao_correct_crawler


# 如果直接运行此文件，执行测试
if __name__ == "__main__":
    import asyncio
    
    async def test():
        crawler = TaobaoCorrectCrawler()
        # 需要提供真实的cookie和token进行测试
        result = await crawler.search_products("手机")
        print(f"测试结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    asyncio.run(test())
