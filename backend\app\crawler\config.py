"""
爬虫配置管理
"""

import os
import sys
import asyncio
from typing import Dict, List, Optional
from dataclasses import dataclass

# 修复Windows上的Playwright异步问题
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())


@dataclass
class CrawlerConfig:
    """爬虫配置类"""
    
    # 浏览器配置
    headless: bool = True
    browser_type: str = "chromium"  # chromium, firefox, webkit
    viewport_width: int = 1920
    viewport_height: int = 1080
    
    # 请求配置
    timeout: int = 120000  # 毫秒，改为120秒
    navigation_timeout: int = 120000  # 毫秒，改为120秒
    wait_timeout: int = 30000  # 毫秒，改为30秒
    
    # 反爬虫配置
    use_stealth: bool = True
    random_user_agent: bool = True
    random_viewport: bool = True
    
    # 代理配置
    use_proxy: bool = False
    proxy_rotation: bool = True
    
    # 重试配置
    max_retries: int = 3
    retry_delay: int = 2  # 秒
    
    # 并发配置
    max_concurrent: int = 5
    rate_limit: int = 10  # 每秒请求数
    
    # 缓存配置
    enable_cache: bool = True
    cache_ttl: int = 3600  # 秒
    
    # 用户代理列表
    user_agents: List[str] = None
    
    def __post_init__(self):
        if self.user_agents is None:
            self.user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
            ]
    
    @classmethod
    def from_env(cls) -> 'CrawlerConfig':
        """从环境变量创建配置"""
        return cls(
            headless=os.getenv('CRAWLER_HEADLESS', 'true').lower() == 'true',
            browser_type=os.getenv('CRAWLER_BROWSER_TYPE', 'chromium'),
            timeout=int(os.getenv('CRAWLER_TIMEOUT', '120000')),
            max_retries=int(os.getenv('CRAWLER_MAX_RETRIES', '3')),
            max_concurrent=int(os.getenv('CRAWLER_MAX_CONCURRENT', '5')),
            use_proxy=os.getenv('CRAWLER_USE_PROXY', 'false').lower() == 'true',
            enable_cache=os.getenv('CRAWLER_ENABLE_CACHE', 'true').lower() == 'true',
        )


# 平台特定配置
PLATFORM_CONFIGS: Dict[str, Dict] = {
    "taobao": {
        "base_url": "https://www.taobao.com",
        "search_url": "https://s.taobao.com/search",
        "selectors": {
            "search_input": "input[name='q']",
            "search_button": "button[type='submit']",
            "product_list": ".item",
            "product_title": ".title a",
            "product_price": ".price",
            "product_image": ".pic img",
            "product_link": ".title a"
        },
        "wait_selectors": [".item", ".items"],
        "rate_limit": 5,  # 每秒请求数
        "timeout": 120000
    },
    "1688": {
        "base_url": "https://www.1688.com",
        "search_url": "https://s.1688.com/selloffer/offer_search.htm",
        "api_url": "https://h5api.m.1688.com/h5/mtop.relationrecommend.wirelessrecommend.recommend/2.0/",
        "app_key": "12574478",
        "app_id": 32517,
        "selectors": {
            "search_input": "input[name='keywords']",
            "search_button": "button[type='submit']",
            "product_list": ".offer-item",
            "product_title": ".offer-title",
            "product_price": ".price",
            "product_image": ".offer-img img",
            "product_link": ".offer-title a"
        },
        "wait_selectors": [".offer-item", ".offer-list"],
        "rate_limit": 5,  # 每秒请求数
        "timeout": 120000
    },
    
    "1688": {
        "base_url": "https://www.1688.com",
        "search_url": "https://s.1688.com/selloffer/offer_search.htm",
        "selectors": {
            "search_input": "input[name='keywords']",
            "search_button": "input[type='submit']",
            "product_list": ".offer-item",
            "product_title": ".offer-title a",
            "product_price": ".price-num",
            "product_image": ".offer-img img",
            "product_link": ".offer-title a"
        },
        "wait_selectors": [".offer-item", ".offer-list"],
        "rate_limit": 5,
        "timeout": 120000
    },
    
    "pinduoduo": {
        "base_url": "https://www.pinduoduo.com",
        "search_url": "https://www.pinduoduo.com/search_result.html",
        "selectors": {
            "search_input": "input[placeholder*='搜索']",
            "search_button": "button[type='submit']",
            "product_list": "._2kHjx3PaL",
            "product_title": "._2kHjx3PaL ._2Hy8wqhU-",
            "product_price": "._2kHjx3PaL ._3ViZbH7X-",
            "product_image": "._2kHjx3PaL img",
            "product_link": "._2kHjx3PaL a"
        },
        "wait_selectors": ["._2kHjx3PaL", ".goods-list"],
        "rate_limit": 3,
        "timeout": 35000
    },
    
    "jingdong": {
        "base_url": "https://www.jd.com",
        "search_url": "https://search.jd.com/Search",
        "selectors": {
            "search_input": "#key",
            "search_button": ".button",
            "product_list": ".gl-item",
            "product_title": ".p-name a em",
            "product_price": ".p-price i",
            "product_image": ".p-img img",
            "product_link": ".p-name a"
        },
        "wait_selectors": [".gl-item", ".gl-warp"],
        "rate_limit": 5,
        "timeout": 120000
    }
}


def get_platform_config(platform: str) -> Optional[Dict]:
    """获取平台配置"""
    return PLATFORM_CONFIGS.get(platform.lower())


def get_default_config() -> CrawlerConfig:
    """获取默认配置"""
    return CrawlerConfig.from_env()
