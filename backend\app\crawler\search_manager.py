#!/usr/bin/env python3
"""
搜索管理器 - 统一管理各平台的商品搜索
"""

import logging
import time
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

from app.crawler.platforms.taobao_correct import get_taobao_correct_crawler
from app.crawler.platforms.alibaba_1688 import get_alibaba_1688_crawler
from app.services.crawler_pool_service import CrawlerPoolService
from app.database import get_db

logger = logging.getLogger(__name__)

class SearchManager:
    """搜索管理器 - 集成爬虫池管理"""

    def __init__(self, client_ip: str = None):
        self.crawlers = {
            "taobao": get_taobao_correct_crawler(),
            "tmall": get_taobao_correct_crawler(),  # 淘宝和天猫使用同一个爬虫
            "1688": get_alibaba_1688_crawler(),
            # TODO: 添加其他平台的爬虫
            # "jd": get_jd_search_crawler(),
            # "pdd": get_pdd_search_crawler(),
        }
        self.client_ip = client_ip or "127.0.0.1"
        self.db = next(get_db())
        self.pool_service = CrawlerPoolService(self.db)
    
    async def search_products(
        self,
        query: str = None,
        keyword: str = None,
        platform: str = "taobao",
        page: int = 1,
        page_size: int = 20,
        sort: str = "default",
        price_min: Optional[float] = None,
        price_max: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        搜索商品

        Args:
            query: 搜索关键词 (新参数名)
            keyword: 搜索关键词 (兼容旧参数名)
            platform: 平台名称
            page: 页码
            page_size: 每页数量
            sort: 排序方式
            price_min: 最低价格
            price_max: 最高价格

        Returns:
            搜索结果
        """
        try:
            # 兼容处理：支持query和keyword两种参数名
            search_keyword = query or keyword

            # 验证平台
            if platform not in self.crawlers:
                return {
                    "success": False,
                    "message": f"不支持的平台: {platform}",
                    "data": None
                }

            # 验证参数
            if not search_keyword or not search_keyword.strip():
                return {
                    "success": False,
                    "message": "搜索关键词不能为空",
                    "data": None
                }
            
            if page < 1 or page > 10:
                return {
                    "success": False,
                    "message": "页码必须在1-10之间",
                    "data": None
                }
            
            if page_size < 1 or page_size > 50:
                return {
                    "success": False,
                    "message": "每页数量必须在1-50之间",
                    "data": None
                }
            
            # 价格验证
            if price_min is not None and price_min < 0:
                return {
                    "success": False,
                    "message": "最低价格不能小于0",
                    "data": None
                }
            
            if price_max is not None and price_max < 0:
                return {
                    "success": False,
                    "message": "最高价格不能小于0",
                    "data": None
                }
            
            if price_min is not None and price_max is not None and price_min > price_max:
                return {
                    "success": False,
                    "message": "最低价格不能大于最高价格",
                    "data": None
                }
            
            logger.info(f"开始搜索: 平台={platform}, 关键词={search_keyword}, 页码={page}")

            # 1. 创建或获取会话
            session_id = self.pool_service.get_or_create_session(
                client_ip=self.client_ip,
                user_agent="SearchManager/1.0",
                platform_code=platform
            )

            if not session_id:
                logger.error("创建会话失败")
                return {
                    "success": False,
                    "message": "创建会话失败",
                    "data": None
                }

            # 2. 选择最佳爬虫账号
            selected_account = self.pool_service.select_best_crawler(
                platform_code=platform,
                session_id=session_id,
                client_ip=self.client_ip
            )

            if not selected_account:
                logger.warning(f"没有可用的{platform}爬虫账号")
                return {
                    "success": False,
                    "message": f"没有可用的{platform}爬虫账号",
                    "data": None
                }

            logger.info(f"选择爬虫账号: {selected_account.username}")

            # 3. 获取爬虫实例
            crawler = self.crawlers[platform]

            # 4. 执行搜索
            start_time = time.time()
            search_result = await self._execute_search(
                crawler=crawler,
                keyword=search_keyword,
                platform=platform,
                page=page,
                page_size=page_size,
                sort=sort,
                price_min=price_min,
                price_max=price_max,
                account_username=selected_account.username
            )

            # 5. 记录搜索结果到爬虫池
            response_time_ms = int((time.time() - start_time) * 1000)
            success = search_result.get("success", False)

            self.pool_service.record_request_result(
                session_id=session_id,
                account_id=selected_account.id,
                success=success,
                response_time_ms=response_time_ms,
                error_message=search_result.get("message") if not success else None
            )
            
            if search_result.get("success"):
                logger.info(f"搜索成功: 找到{len(search_result.get('data', {}).get('products', []))}个商品")
            else:
                logger.warning(f"搜索失败: {search_result.get('message')}")
            
            return search_result
            
        except Exception as e:
            logger.error(f"搜索商品失败: {str(e)}")
            return {
                "success": False,
                "message": f"搜索失败: {str(e)}",
                "data": None
            }
    
    async def _execute_search(
        self,
        crawler,
        keyword: str,
        platform: str,
        page: int,
        page_size: int,
        sort: str,
        price_min: Optional[float],
        price_max: Optional[float],
        account_username: str = None
    ) -> Dict[str, Any]:
        """执行具体的搜索操作"""
        try:
            # 根据平台调用不同的搜索方法
            if platform in ["taobao", "tmall"]:
                result = await self._search_taobao(
                    crawler, keyword, page, page_size, sort, price_min, price_max, account_username
                )
            elif platform == "1688":
                result = await self._search_1688(
                    crawler, keyword, page, page_size, sort, price_min, price_max, account_username
                )
            else:
                return {
                    "success": False,
                    "message": f"平台 {platform} 搜索功能暂未实现",
                    "data": None
                }

            logger.info(f"🔍 平台搜索结果: {result}")

            # 标准化返回格式
            if result and result.get("success"):
                # 直接返回爬虫处理后的结果，不再进行二次处理
                return result
            else:
                return result or {
                    "success": False,
                    "message": "搜索失败",
                    "data": None
                }
                
        except Exception as e:
            logger.error(f"执行搜索失败: {str(e)}")
            return {
                "success": False,
                "message": f"搜索执行失败: {str(e)}",
                "data": None
            }
    
    async def _search_taobao(
        self,
        crawler,
        keyword: str,
        page: int,
        page_size: int,
        sort: str,
        price_min: Optional[float],
        price_max: Optional[float],
        account_username: str = None
    ) -> Dict[str, Any]:
        """淘宝/天猫搜索"""
        try:
            # 调用淘宝搜索，传入指定的账号用户名
            result = await crawler.search_products(keyword, account_username)
            logger.info(f"🔍 淘宝爬虫原始结果: {result}")

            # 检查是否有错误
            if result.get('error'):
                logger.warning(f"⚠️ 淘宝搜索有错误: {result.get('error')}")
                return {
                    "success": False,
                    "message": result.get('error', '搜索失败'),
                    "data": None
                }

            # 检查是否有商品数据
            products = result.get('products', [])
            total = result.get('total', 0)

            logger.info(f"🔍 解析到商品数量: {len(products)}, 总数: {total}")

            if products and len(products) > 0:
                # 使用爬虫的格式化方法转换为上游格式
                if hasattr(crawler, 'format_search_results_for_upstream'):
                    logger.info(f"🔍 使用爬虫格式化方法")
                    formatted_result = crawler.format_search_results_for_upstream(
                        products, keyword, total
                    )
                    logger.info(f"🔍 格式化后结果: {formatted_result}")

                    return {
                        "success": True,
                        "message": "搜索成功",
                        "data": formatted_result
                    }
                else:
                    # 如果没有格式化方法，直接返回原始结果
                    logger.info(f"🔍 使用默认格式化")
                    return {
                        "success": True,
                        "message": "搜索成功",
                        "data": {
                            "products": products,
                            "total": total,
                            "platform": "taobao",
                            "keyword": keyword
                        }
                    }
            else:
                logger.info(f"🔍 没有找到商品")
                return {
                    "success": True,
                    "message": "搜索成功",
                    "data": {
                        "products": [],
                        "total": 0,
                        "platform": "taobao",
                        "keyword": keyword
                    }
                }

        except Exception as e:
            logger.error(f"淘宝搜索失败: {str(e)}")
            return {
                "success": False,
                "message": f"淘宝搜索失败: {str(e)}",
                "data": None
            }
    
    async def _search_1688(
        self,
        crawler,
        keyword: str,
        page: int,
        page_size: int,
        sort: str,
        price_min: Optional[float],
        price_max: Optional[float],
        account_username: str = None
    ) -> Dict[str, Any]:
        """1688搜索"""
        try:
            # 调用1688搜索，传入指定的账号用户名
            result = await crawler.search_products(keyword, account_username)

            if result and result.get('total', 0) > 0:
                products = result.get('products', [])

                # 使用爬虫的格式化方法转换为上游格式
                if hasattr(crawler, 'format_search_results_for_upstream'):
                    formatted_result = crawler.format_search_results_for_upstream(
                        products, keyword, result.get('total', 0)
                    )

                    return {
                        "success": True,
                        "message": "搜索成功",
                        "data": formatted_result
                    }
                else:
                    # 如果没有格式化方法，直接返回原始结果
                    return {
                        "success": True,
                        "message": "搜索成功",
                        "data": {
                            "products": products,
                            "total": result.get('total', 0),
                            "platform": "1688",
                            "keyword": keyword
                        }
                    }
            else:
                return {
                    "success": True,
                    "message": "搜索成功",
                    "data": {
                        "products": [],
                        "total": 0,
                        "platform": "1688",
                        "keyword": keyword
                    }
                }

        except Exception as e:
            logger.error(f"1688搜索失败: {str(e)}")
            return {
                "success": False,
                "message": f"1688搜索失败: {str(e)}",
                "data": None
            }
    
    def _filter_by_price(
        self,
        products: List[Dict],
        price_min: Optional[float],
        price_max: Optional[float]
    ) -> List[Dict]:
        """根据价格过滤商品"""
        if price_min is None and price_max is None:
            return products
        
        filtered_products = []
        
        for product in products:
            price = product.get("price", 0)
            # 价格单位是分，需要转换为元
            price_yuan = price / 100 if price else 0
            
            # 应用价格过滤
            if price_min is not None and price_yuan < price_min:
                continue
            
            if price_max is not None and price_yuan > price_max:
                continue
            
            filtered_products.append(product)
        
        return filtered_products
    
    def get_supported_platforms(self) -> List[str]:
        """获取支持的平台列表"""
        return list(self.crawlers.keys())
    
    def is_platform_supported(self, platform: str) -> bool:
        """检查平台是否支持"""
        return platform in self.crawlers
