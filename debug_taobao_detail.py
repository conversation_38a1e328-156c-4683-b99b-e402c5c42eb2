#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试淘宝商品详情API - 检查多规格属性解析
"""

import requests
import json

def test_taobao_detail():
    """测试淘宝商品详情"""
    print("🔍 测试淘宝商品详情...")
    
    # 使用一个有多规格的淘宝商品
    data = {
        "product_url": "https://item.taobao.com/item.htm?id=669208469838",  # 真实商品
        "language": "zh"
    }
    
    try:
        response = requests.post("http://localhost:8000/api/v1/upstream/product/detail", json=data)
        result = response.json()
        
        if result.get('code') == 200:
            product_data = result.get('data', {})
            
            print(f"\n📦 商品基本信息:")
            print(f"  - ID: {product_data.get('id')}")
            print(f"  - 名称: {product_data.get('name', '')[:50]}...")
            print(f"  - 主图: {product_data.get('picUrl')}")
            print(f"  - 图片列表数量: {len(product_data.get('sliderPicUrls', []))}")
            
            # 详细分析图片信息
            slider_pics = product_data.get('sliderPicUrls', [])
            print(f"\n🖼️ 图片详情:")
            if slider_pics:
                for i, pic in enumerate(slider_pics[:5]):  # 只显示前5张
                    print(f"  {i+1}. {pic}")
            else:
                print("  ❌ 没有找到图片数据")
                
            # 检查原始响应数据
            print(f"\n🔍 原始响应检查:")
            print(f"  - 响应状态: {result.get('code')}")
            print(f"  - 响应消息: {result.get('message')}")
            if 'data' in result:
                data_keys = list(result['data'].keys())
                print(f"  - 数据字段: {data_keys}")
                if 'sliderPicUrls' in result['data']:
                    raw_pics = result['data']['sliderPicUrls']
                    print(f"  - 原始图片数据类型: {type(raw_pics)}")
                    print(f"  - 原始图片数据长度: {len(raw_pics) if isinstance(raw_pics, list) else 'N/A'}")
                    if isinstance(raw_pics, list) and raw_pics:
                        print(f"  - 原始图片示例: {raw_pics[0]}")
                else:
                    print("  - ❌ 响应数据中没有sliderPicUrls字段")
            
            # 分析SKU信息
            skus = product_data.get('skus', [])
            print(f"\n📋 SKU详情 (共{len(skus)}个):")
            
            for i, sku in enumerate(skus[:5]):  # 只显示前5个
                print(f"\n  SKU {i+1}:")
                print(f"    - ID: {sku.get('id')}")
                print(f"    - 价格: {sku.get('price')}分")
                print(f"    - 库存: {sku.get('stock')}")
                print(f"    - 图片: {sku.get('picUrl')}")
                
                properties = sku.get('properties', [])
                print(f"    - 属性数量: {len(properties)}")
                for j, prop in enumerate(properties):
                    print(f"      属性{j+1}: {prop.get('propertyName')}={prop.get('valueName')} (ID:{prop.get('propertyId')}, ValueID:{prop.get('valueId')})")
            
            # 分析属性分布
            props = product_data.get('props', [])
            print(f"\n📊 属性分析:")
            for prop in props:
                values = prop.get('values', [])
                value_names = [v.get('name', '') for v in values[:3]]  # 只显示前3个值
                print(f"  - {prop.get('name')}: {len(values)}个值 ({', '.join(value_names)}{'...' if len(values) > 3 else ''})")
                
        else:
            print(f"❌ 请求失败: {result.get('code')}")
            print(f"响应: {result}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_taobao_detail()
