#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试1688新数据结构的SKU和价格解析
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
backend_path = project_root / "backend"
sys.path.insert(0, str(backend_path))

from app.crawler.platforms.alibaba_1688 import Alibaba1688Crawler

async def test_1688_new_structure():
    """测试1688新数据结构解析"""
    print("🚀 开始测试1688新数据结构解析...")
    
    # 创建爬虫实例
    crawler = Alibaba1688Crawler()
    
    # 测试商品列表 - 重点测试iPhone手机壳商品
    test_products = [
        {
            'url': 'https://detail.1688.com/offer/932222752479.html',
            'description': 'iPhone手机壳商品 - 应该有多规格(颜色+型号)'
        }
    ]
    
    for i, product in enumerate(test_products, 1):
        print(f"\n{'='*60}")
        print(f"🔍 测试商品 {i}: {product['description']}")
        print(f"📍 URL: {product['url']}")
        print(f"{'='*60}")
        
        try:
            # 获取商品详情
            result = await crawler.get_product_detail(product['url'])

            if result.get('success'):
                data = result['data']
                print(f"✅ 商品获取成功")
                print(f"📦 商品标题: {data.get('name', 'N/A')}")
                print(f"💰 价格范围: {data.get('priceRange', 'N/A')}")
                print(f"🖼️ 图片数量: {len(data.get('images', []))}")

                # 重点测试SKU信息
                skus = data.get('skus', [])
                print(f"\n📋 SKU信息分析:")
                print(f"   - SKU总数: {len(skus)}")

                if skus:
                    # 分析SKU属性维度
                    all_properties = {}
                    for sku in skus:
                        for prop in sku.get('properties', []):
                            prop_name = prop.get('name', '未知属性')
                            prop_value = prop.get('value', '未知值')

                            if prop_name not in all_properties:
                                all_properties[prop_name] = set()
                            all_properties[prop_name].add(prop_value)

                    print(f"   - 属性维度数: {len(all_properties)}")
                    for prop_name, values in all_properties.items():
                        print(f"     * {prop_name}: {len(values)}个值")
                        if len(values) <= 10:
                            print(f"       值列表: {', '.join(sorted(values))}")
                        else:
                            sample_values = list(sorted(values))[:5]
                            print(f"       示例值: {', '.join(sample_values)}... (共{len(values)}个)")

                    # 显示前几个SKU的详细信息
                    print(f"\n   📋 前5个SKU详细信息:")
                    for j, sku in enumerate(skus[:5], 1):
                        print(f"     SKU {j}:")
                        print(f"       - ID: {sku.get('id')}")
                        print(f"       - 1688 SKU ID: {sku.get('skuId')}")
                        print(f"       - 价格: {sku.get('price', 0)/100:.2f}元")
                        print(f"       - 市场价: {sku.get('marketPrice', 0)/100:.2f}元")
                        print(f"       - 库存: {sku.get('stock', 0)}")
                        print(f"       - 重量: {sku.get('weight', 0)}克")
                        properties_str = [f"{p.get('name')}={p.get('value')}" for p in sku.get('properties', [])]
                        print(f"       - 属性: {properties_str}")
                        if sku.get('picUrl'):
                            print(f"       - 图片: {sku.get('picUrl')[:80]}...")
                else:
                    print("   ⚠️ 没有找到SKU信息")
                    print("   🔍 让我们检查一下原始数据结构...")

                    # 检查是否有其他可能的SKU数据
                    print(f"   📊 商品数据键: {list(data.keys())}")

                    # 如果有原始数据，显示一些调试信息
                    if hasattr(crawler, '_last_init_data') and crawler._last_init_data:
                        init_data = crawler._last_init_data
                        global_data = init_data.get('result', {}).get('global', {}).get('globalData', {})
                        print(f"   🔍 globalData键: {list(global_data.keys())}")

                        # 检查是否有skuModel
                        if 'skuModel' in global_data:
                            sku_model = global_data['skuModel']
                            print(f"   ✅ 找到skuModel: {list(sku_model.keys())}")
                        else:
                            print(f"   ⚠️ globalData中没有skuModel")

                            # 检查model字段
                            if 'model' in global_data:
                                model_data = global_data['model']
                                print(f"   🔍 检查model字段: {type(model_data)}")
                                if isinstance(model_data, dict):
                                    print(f"   🔍 model键: {list(model_data.keys())}")

                                    # 查找可能的SKU相关字段
                                    for key, value in model_data.items():
                                        if 'sku' in key.lower() or 'spec' in key.lower():
                                            print(f"   🎯 找到可能的SKU字段: {key} = {type(value)}")
                                            if isinstance(value, dict) and value:
                                                print(f"      键: {list(value.keys())[:10]}")  # 只显示前10个键

                # 测试价格范围计算
                if skus:
                    prices = [sku.get('price', 0) for sku in skus if sku.get('price', 0) > 0]
                    if prices:
                        min_price = min(prices) / 100
                        max_price = max(prices) / 100
                        print(f"\n💰 价格分析:")
                        print(f"   - 最低价: {min_price:.2f}元")
                        print(f"   - 最高价: {max_price:.2f}元")
                        print(f"   - 价格范围: {min_price:.2f}-{max_price:.2f}元")

            else:
                print(f"❌ 商品获取失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误:\n{traceback.format_exc()}")
    
    print(f"\n🎉 测试完成!")

if __name__ == "__main__":
    asyncio.run(test_1688_new_structure())
