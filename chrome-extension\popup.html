<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 15px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .header h2 {
            margin: 0;
            color: #ff6600;
            font-size: 16px;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .account-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        .account-info .label {
            font-weight: bold;
            color: #666;
            font-size: 12px;
        }
        
        .account-info .value {
            color: #333;
            font-size: 14px;
            word-break: break-all;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #ff6600;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #e55a00;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .config-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .config-item {
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .config-item .key {
            font-weight: bold;
            color: #666;
        }
        
        .config-item .value {
            color: #333;
            word-break: break-all;
        }
        
        .footer {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #ff6600;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 5px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🕷️ 淘宝配置提取器</h2>
    </div>
    
    <div id="status" class="status warning">
        请在淘宝页面使用此扩展
    </div>
    
    <div id="accountInfo" class="account-info" style="display: none;">
        <div class="config-item">
            <span class="label">账号：</span>
            <span id="username" class="value">未检测到</span>
        </div>
        <div class="config-item">
            <span class="label">登录状态：</span>
            <span id="loginStatus" class="value">检测中...</span>
        </div>
        <div class="config-item">
            <span class="label">Token状态：</span>
            <span id="tokenStatus" class="value">检测中...</span>
        </div>
    </div>
    
    <div class="buttons">
        <button id="extractBtn" class="btn btn-primary" disabled>
            <span id="extractText">提取配置</span>
        </button>
        <button id="settingsBtn" class="btn btn-secondary">
            设置
        </button>
    </div>
    
    <div id="configPreview" class="config-preview" style="display: none;">
        <div class="config-item">
            <div class="key">Cookie:</div>
            <div id="cookiePreview" class="value"></div>
        </div>
        <div class="config-item">
            <div class="key">Token:</div>
            <div id="tokenPreview" class="value"></div>
        </div>
        <div class="config-item">
            <div class="key">User-Agent:</div>
            <div id="userAgentPreview" class="value"></div>
        </div>
    </div>
    
    <div class="footer">
        版本 1.0.0 | AqentCrawler
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
