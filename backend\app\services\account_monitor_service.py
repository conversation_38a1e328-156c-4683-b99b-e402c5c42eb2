"""
账号监控服务

提供账号状态监控、Token过期检测、自动刷新等功能
"""

import asyncio
import logging
import platform
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.database import get_db
from app.models import CrawlerAccount, Platform

logger = logging.getLogger(__name__)


class AccountMonitorService:
    """账号监控服务"""

    def __init__(self):
        self.running = False
        self.monitor_interval = 60  # 1分钟检查一次
        self.token_warning_hours = 2  # Token过期前2小时开始警告
        self.is_windows = platform.system().lower() == 'windows'
        self.health_check_interval = 300  # 5分钟进行一次健康检查
        self.last_health_check = None
        
    async def start_monitoring(self):
        """开始监控服务"""
        if self.running:
            logger.warning("监控服务已在运行中")
            return

        self.running = True

        if self.is_windows:
            logger.info("账号监控服务启动 (Windows环境 - 仅监控模式)")
            print("账号监控服务已启动 (Windows环境 - 仅监控模式)")
        else:
            logger.info("账号监控服务启动")
            print("账号监控服务已启动")

        while self.running:
            try:
                await self._monitor_cycle()
                await asyncio.sleep(self.monitor_interval)  # 使用正确的属性名
            except Exception as e:
                logger.error(f"监控服务异常: {str(e)}")
                await asyncio.sleep(30)  # 异常后等待30秒再继续
    
    def stop_monitoring(self):
        """停止监控服务"""
        self.running = False
        logger.info("账号监控服务停止")
    
    async def _monitor_cycle(self):
        """监控周期"""
        db = next(get_db())
        try:
            # 1. 检查Token过期状态
            await self._check_token_expiration(db)
            

            
            # 2. 定期健康检查
            if self._should_run_health_check():
                await self._health_check(db)
                self.last_health_check = datetime.now()

            # 3. 清理过期数据
            await self._cleanup_expired_data(db)
            
        except Exception as e:
            logger.error(f"监控周期执行异常: {str(e)}")
        finally:
            db.close()
    
    async def _check_token_expiration(self, db: Session):
        """检查Token过期状态"""
        try:
            now = datetime.now()
            warning_time = now + timedelta(hours=self.token_warning_hours)
            
            # 查找即将过期的Token
            expiring_accounts = db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.login_status == 'logged_in',
                    CrawlerAccount.token_expires_at.isnot(None),
                    CrawlerAccount.token_expires_at <= warning_time,
                    CrawlerAccount.token_expires_at > now
                )
            ).all()
            
            if expiring_accounts:
                logger.info(f"发现 {len(expiring_accounts)} 个账号Token即将过期")
                for account in expiring_accounts:
                    hours_left = (account.token_expires_at - now).total_seconds() / 3600
                    logger.warning(f"账号 {account.username} Token将在 {hours_left:.1f} 小时后过期")
            
            # 查找已过期的Token
            expired_accounts = db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.login_status == 'logged_in',
                    CrawlerAccount.token_expires_at.isnot(None),
                    CrawlerAccount.token_expires_at <= now
                )
            ).all()
            
            if expired_accounts:
                logger.warning(f"发现 {len(expired_accounts)} 个账号Token已过期")
                for account in expired_accounts:
                    account.login_status = 'expired'
                    account.last_error_message = f"Token已于 {account.token_expires_at} 过期"
                    account.updated_at = now
                    logger.warning(f"账号 {account.username} Token已过期，状态已更新")
                
                db.commit()
                
        except Exception as e:
            logger.error(f"检查Token过期状态失败: {str(e)}")
    

    
    def _should_run_health_check(self) -> bool:
        """判断是否应该运行健康检查"""
        if self.last_health_check is None:
            return True
        
        elapsed = (datetime.now() - self.last_health_check).total_seconds()
        return elapsed >= self.health_check_interval
    
    async def _health_check(self, db: Session):
        """账号健康检查"""
        try:
            logger.info("开始执行账号健康检查")
            
            # 统计各种状态的账号数量
            total_accounts = db.query(CrawlerAccount).count()
            logged_in_count = db.query(CrawlerAccount).filter(CrawlerAccount.login_status == 'logged_in').count()
            expired_count = db.query(CrawlerAccount).filter(CrawlerAccount.login_status == 'expired').count()
            error_count = db.query(CrawlerAccount).filter(CrawlerAccount.login_status == 'error').count()
            
            logger.info(f"账号健康状态 - 总计: {total_accounts}, 已登录: {logged_in_count}, 已过期: {expired_count}, 错误: {error_count}")
            
            # 检查长时间未使用的账号
            inactive_threshold = datetime.now() - timedelta(days=7)
            inactive_accounts = db.query(CrawlerAccount).filter(
                or_(
                    CrawlerAccount.last_used_at.is_(None),
                    CrawlerAccount.last_used_at < inactive_threshold
                )
            ).count()
            
            if inactive_accounts > 0:
                logger.warning(f"发现 {inactive_accounts} 个账号超过7天未使用")
            
            # 检查错误率高的账号
            high_error_accounts = db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.total_requests > 10,
                    CrawlerAccount.success_rate < 50.0
                )
            ).all()
            
            if high_error_accounts:
                logger.warning(f"发现 {len(high_error_accounts)} 个账号成功率低于50%")
                for account in high_error_accounts:
                    logger.warning(f"账号 {account.username} 成功率: {account.success_rate}%")
            
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
    
    async def _cleanup_expired_data(self, db: Session):
        """清理过期数据"""
        try:
            # 清理超过30天的错误消息
            old_threshold = datetime.now() - timedelta(days=30)
            
            old_error_accounts = db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.login_status == 'error',
                    CrawlerAccount.updated_at < old_threshold
                )
            ).all()
            
            if old_error_accounts:
                logger.info(f"清理 {len(old_error_accounts)} 个超过30天的错误账号状态")
                for account in old_error_accounts:
                    account.login_status = 'pending'
                    account.last_error_message = None
                    account.updated_at = datetime.now()
                
                db.commit()
                
        except Exception as e:
            logger.error(f"清理过期数据失败: {str(e)}")
    
    async def get_monitor_status(self) -> Dict:
        """获取监控服务状态"""
        db = next(get_db())
        try:
            now = datetime.now()
            
            # 统计账号状态
            total_accounts = db.query(CrawlerAccount).count()
            logged_in_count = db.query(CrawlerAccount).filter(CrawlerAccount.login_status == 'logged_in').count()
            expired_count = db.query(CrawlerAccount).filter(CrawlerAccount.login_status == 'expired').count()
            error_count = db.query(CrawlerAccount).filter(CrawlerAccount.login_status == 'error').count()
            
            # 统计Token状态
            warning_time = now + timedelta(hours=self.token_warning_hours)
            expiring_count = db.query(CrawlerAccount).filter(
                and_(
                    CrawlerAccount.login_status == 'logged_in',
                    CrawlerAccount.token_expires_at.isnot(None),
                    CrawlerAccount.token_expires_at <= warning_time,
                    CrawlerAccount.token_expires_at > now
                )
            ).count()
            
            return {
                "monitor_running": self.running,
                "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
                "account_stats": {
                    "total": total_accounts,
                    "logged_in": logged_in_count,
                    "expired": expired_count,
                    "error": error_count,
                    "expiring_soon": expiring_count
                },
                "settings": {
                    "monitor_interval": self.monitor_interval,
                    "token_warning_hours": self.token_warning_hours,
                    "health_check_interval": self.health_check_interval
                }
            }
            
        except Exception as e:
            logger.error(f"获取监控状态失败: {str(e)}")
            return {"error": str(e)}
        finally:
            db.close()


# 全局监控服务实例
account_monitor_service = AccountMonitorService()
