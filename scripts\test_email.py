#!/usr/bin/env python3
"""
邮件功能测试脚本
用于测试各种邮件模板和功能
"""

import os
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量（如果没有.env文件）
if not os.getenv('SMTP_SERVER'):
    os.environ.setdefault('SMTP_SERVER', 'smtp.qq.com')
    os.environ.setdefault('SMTP_PORT', '587')
    os.environ.setdefault('SMTP_USE_TLS', 'true')
    os.environ.setdefault('SENDER_NAME', 'AqentCrawler测试')

from backend.app.services.email_service import email_service

async def test_connection():
    """测试邮件服务器连接"""
    print("🔗 测试邮件服务器连接...")
    
    try:
        success = await email_service.test_connection()
        if success:
            print("✅ 邮件服务器连接成功")
            return True
        else:
            print("❌ 邮件服务器连接失败")
            return False
    except Exception as e:
        print(f"❌ 连接测试出错: {e}")
        return False

async def test_plain_email(to_email: str):
    """测试发送纯文本邮件"""
    print(f"📧 发送纯文本邮件到: {to_email}")
    
    try:
        success = await email_service.send_email(
            to_emails=[to_email],
            subject="[测试] AqentCrawler 纯文本邮件测试",
            content="这是一封纯文本测试邮件。\n\n如果您收到这封邮件，说明邮件服务配置正确。\n\n发送时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            content_type='plain'
        )
        
        if success:
            print("✅ 纯文本邮件发送成功")
        else:
            print("❌ 纯文本邮件发送失败")
        
        return success
    except Exception as e:
        print(f"❌ 发送纯文本邮件出错: {e}")
        return False

async def test_html_email(to_email: str):
    """测试发送HTML邮件"""
    print(f"📧 发送HTML邮件到: {to_email}")
    
    html_content = """
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #1890ff;">AqentCrawler HTML邮件测试</h2>
            <p>这是一封HTML格式的测试邮件。</p>
            <div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="margin: 0 0 10px 0; color: #1890ff;">测试信息</h3>
                <ul>
                    <li><strong>发送时间:</strong> {}</li>
                    <li><strong>邮件类型:</strong> HTML测试邮件</li>
                    <li><strong>状态:</strong> <span style="color: #52c41a;">✅ 成功</span></li>
                </ul>
            </div>
            <p>如果您能看到这封邮件的格式，说明HTML邮件功能正常。</p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
            <p style="font-size: 12px; color: #666;">
                此邮件由 AqentCrawler 系统自动发送，请勿回复。
            </p>
        </div>
    </body>
    </html>
    """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    try:
        success = await email_service.send_email(
            to_emails=[to_email],
            subject="[测试] AqentCrawler HTML邮件测试",
            content=html_content,
            content_type='html'
        )
        
        if success:
            print("✅ HTML邮件发送成功")
        else:
            print("❌ HTML邮件发送失败")
        
        return success
    except Exception as e:
        print(f"❌ 发送HTML邮件出错: {e}")
        return False

async def test_notification_template(to_email: str):
    """测试通用通知模板"""
    print(f"📧 发送通知模板邮件到: {to_email}")
    
    template_data = {
        'title': '邮件模板测试',
        'message': '这是一封使用通用通知模板的测试邮件。',
        'notification_type': 'success',
        'content': '<p>如果您收到这封邮件，说明邮件模板功能正常工作。</p>',
        'key_value_data': {
            'title': '测试信息',
            'items': [
                {'key': '测试时间', 'value': datetime.now().strftime('%Y-%m-%d %H:%M:%S')},
                {'key': '模板类型', 'value': '通用通知模板'},
                {'key': '发送状态', 'value': '成功', 'type': 'badge', 'class': 'success'},
                {'key': '邮件服务', 'value': '正常运行', 'type': 'badge', 'class': 'success'}
            ]
        },
        'actions': [
            {
                'text': '查看系统状态',
                'url': 'http://localhost:3000',
                'type': 'primary'
            }
        ]
    }
    
    try:
        success = await email_service.send_template_email(
            to_emails=[to_email],
            template_name='notification',
            subject='[测试] AqentCrawler 通知模板测试',
            template_data=template_data
        )
        
        if success:
            print("✅ 通知模板邮件发送成功")
        else:
            print("❌ 通知模板邮件发送失败")
        
        return success
    except Exception as e:
        print(f"❌ 发送通知模板邮件出错: {e}")
        return False

async def test_system_alert_template(to_email: str):
    """测试系统告警模板"""
    print(f"📧 发送系统告警模板邮件到: {to_email}")
    
    template_data = {
        'alert_title': '邮件功能测试告警',
        'alert_message': '这是一个测试告警，用于验证系统告警邮件模板功能。',
        'alert_type': 'warning',
        'alert_details': {
            '测试项目': '邮件模板功能',
            '测试时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '测试状态': '进行中',
            '预期结果': '邮件发送成功'
        },
        'affected_services': [
            {
                'name': '邮件服务',
                'status': 'success',
                'description': '正常运行'
            },
            {
                'name': '模板引擎',
                'status': 'success', 
                'description': '渲染正常'
            }
        ],
        'metrics': [
            {
                'name': '邮件发送成功率',
                'current_value': '100%',
                'threshold': '95%',
                'status': 'success',
                'status_text': '正常'
            },
            {
                'name': '模板渲染时间',
                'current_value': '<100ms',
                'threshold': '500ms',
                'status': 'success',
                'status_text': '优秀'
            }
        ],
        'recommendations': [
            '继续监控邮件发送状态',
            '定期测试邮件模板功能',
            '保持邮件服务器连接稳定'
        ],
        'alert_id': f'TEST-{datetime.now().strftime("%Y%m%d%H%M%S")}'
    }
    
    try:
        success = await email_service.send_template_email(
            to_emails=[to_email],
            template_name='system_alert',
            subject='[测试告警] AqentCrawler 系统告警模板测试',
            template_data=template_data
        )
        
        if success:
            print("✅ 系统告警模板邮件发送成功")
        else:
            print("❌ 系统告警模板邮件发送失败")
        
        return success
    except Exception as e:
        print(f"❌ 发送系统告警模板邮件出错: {e}")
        return False

async def test_crawler_status_template(to_email: str):
    """测试爬虫状态模板"""
    print(f"📧 发送爬虫状态模板邮件到: {to_email}")
    
    template_data = {
        'notification_title': '爬虫状态测试通知',
        'notification_message': '这是一个测试通知，用于验证爬虫状态邮件模板功能。',
        'status_type': 'info',
        'crawler_accounts': [
            {
                'platform': 'taobao',
                'platform_name': '淘宝',
                'username': 'test_user_001',
                'status': 'active',
                'status_text': '正常',
                'status_class': 'success',
                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'remark': '测试账号'
            },
            {
                'platform': '1688',
                'platform_name': '1688',
                'username': 'test_user_002',
                'status': 'warning',
                'status_text': 'Token即将过期',
                'status_class': 'warning',
                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'remark': '需要刷新Token'
            }
        ],
        'statistics': [
            {
                'name': '总账号数',
                'value': '2',
                'unit': '个'
            },
            {
                'name': '可用账号数',
                'value': '2',
                'unit': '个'
            },
            {
                'name': '测试成功率',
                'value': '100',
                'unit': '%'
            }
        ],
        'next_actions': [
            '继续监控账号状态',
            '定期刷新Token',
            '保持爬虫服务稳定'
        ]
    }
    
    try:
        success = await email_service.send_template_email(
            to_emails=[to_email],
            template_name='crawler_status',
            subject='[测试通知] AqentCrawler 爬虫状态模板测试',
            template_data=template_data
        )
        
        if success:
            print("✅ 爬虫状态模板邮件发送成功")
        else:
            print("❌ 爬虫状态模板邮件发送失败")
        
        return success
    except Exception as e:
        print(f"❌ 发送爬虫状态模板邮件出错: {e}")
        return False

async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 AqentCrawler 邮件功能测试")
    print("=" * 60)
    
    # 检查配置
    if not os.getenv('SMTP_USERNAME') or not os.getenv('SMTP_PASSWORD'):
        print("❌ 邮件配置不完整，请设置以下环境变量:")
        print("   SMTP_USERNAME - SMTP用户名")
        print("   SMTP_PASSWORD - SMTP密码")
        print("   可以在 .env 文件中配置")
        return
    
    # 获取测试邮箱
    test_email = input("请输入测试邮箱地址: ").strip()
    if not test_email:
        print("❌ 未输入测试邮箱地址")
        return
    
    print(f"📧 测试邮箱: {test_email}")
    print()
    
    # 测试列表
    tests = [
        ("连接测试", test_connection),
        ("纯文本邮件", lambda: test_plain_email(test_email)),
        ("HTML邮件", lambda: test_html_email(test_email)),
        ("通知模板", lambda: test_notification_template(test_email)),
        ("系统告警模板", lambda: test_system_alert_template(test_email)),
        ("爬虫状态模板", lambda: test_crawler_status_template(test_email))
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}出错: {e}")
            results.append((test_name, False))
        print()
    
    # 显示测试结果
    print("=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过，邮件功能正常！")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    asyncio.run(main())
