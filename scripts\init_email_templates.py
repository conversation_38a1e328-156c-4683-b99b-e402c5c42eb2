#!/usr/bin/env python3
"""
邮件模板初始化脚本
创建默认的邮件模板文件
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.app.services.email_service import email_service

def init_email_templates():
    """初始化邮件模板"""
    print("🚀 开始初始化邮件模板...")
    
    # 确保模板目录存在
    template_dir = Path(__file__).parent.parent / "backend" / "app" / "templates" / "email"
    template_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 模板目录: {template_dir}")
    
    # 检查模板文件
    templates = [
        "base.html",
        "system_alert.html", 
        "crawler_status.html",
        "api_report.html",
        "notification.html"
    ]
    
    existing_templates = []
    missing_templates = []
    
    for template in templates:
        template_path = template_dir / template
        if template_path.exists():
            existing_templates.append(template)
        else:
            missing_templates.append(template)
    
    print(f"✅ 已存在的模板: {len(existing_templates)}个")
    for template in existing_templates:
        print(f"   - {template}")
    
    if missing_templates:
        print(f"❌ 缺失的模板: {len(missing_templates)}个")
        for template in missing_templates:
            print(f"   - {template}")
    else:
        print("🎉 所有邮件模板都已存在！")
    
    return len(missing_templates) == 0

def test_email_service():
    """测试邮件服务配置"""
    print("\n🔧 测试邮件服务配置...")
    
    # 检查环境变量
    required_vars = [
        'SMTP_SERVER',
        'SMTP_PORT', 
        'SMTP_USERNAME',
        'SMTP_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺失的环境变量: {', '.join(missing_vars)}")
        print("请在 .env 文件中配置邮件相关环境变量")
        return False
    
    print("✅ 邮件环境变量配置完整")
    
    # 显示当前配置
    print(f"📧 SMTP服务器: {os.getenv('SMTP_SERVER')}")
    print(f"📧 SMTP端口: {os.getenv('SMTP_PORT')}")
    print(f"📧 发件人: {os.getenv('SENDER_EMAIL', os.getenv('SMTP_USERNAME'))}")
    print(f"📧 发件人名称: {os.getenv('SENDER_NAME', 'AqentCrawler系统')}")
    
    return True

async def test_email_connection():
    """测试邮件连接"""
    print("\n🌐 测试邮件服务器连接...")
    
    try:
        success = await email_service.test_connection()
        if success:
            print("✅ 邮件服务器连接成功")
            return True
        else:
            print("❌ 邮件服务器连接失败")
            return False
    except Exception as e:
        print(f"❌ 邮件服务器连接测试出错: {e}")
        return False

def create_sample_email_config():
    """创建示例邮件配置"""
    print("\n📝 创建示例邮件配置...")
    
    config_content = """# 邮件服务配置示例

## QQ邮箱配置
SMTP_SERVER=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password  # QQ邮箱需要使用应用专用密码
SMTP_USE_TLS=true
SMTP_USE_SSL=false
SENDER_EMAIL=<EMAIL>
SENDER_NAME=AqentCrawler系统

## 163邮箱配置
# SMTP_SERVER=smtp.163.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_password
# SMTP_USE_TLS=true
# SMTP_USE_SSL=false
# SENDER_EMAIL=<EMAIL>
# SENDER_NAME=AqentCrawler系统

## Gmail配置
# SMTP_SERVER=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password  # Gmail需要使用应用专用密码
# SMTP_USE_TLS=true
# SMTP_USE_SSL=false
# SENDER_EMAIL=<EMAIL>
# SENDER_NAME=AqentCrawler系统

## 企业邮箱配置
# SMTP_SERVER=smtp.exmail.qq.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_password
# SMTP_USE_TLS=true
# SMTP_USE_SSL=false
# SENDER_EMAIL=<EMAIL>
# SENDER_NAME=AqentCrawler系统

## 配置说明
# 1. 将上述配置添加到 .env 文件中
# 2. 根据你的邮箱类型选择对应的配置
# 3. 替换 your_email 和 your_password 为实际的邮箱和密码
# 4. QQ邮箱和Gmail需要使用应用专用密码，不是登录密码
# 5. 确保邮箱已开启SMTP服务

## 获取应用专用密码方法
# QQ邮箱: 邮箱设置 -> 账户 -> POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务 -> 生成授权码
# Gmail: Google账户 -> 安全性 -> 两步验证 -> 应用专用密码
"""
    
    config_file = Path(__file__).parent.parent / "邮件配置示例.md"
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 示例配置已保存到: {config_file}")

def show_usage_examples():
    """显示使用示例"""
    print("\n📚 邮件功能使用示例:")
    
    examples = """
# 1. 发送系统告警邮件
curl -X POST "http://localhost:8000/api/v1/email/alert/system" \\
  -H "Authorization: Bearer your_token" \\
  -H "Content-Type: application/json" \\
  -d '{
    "to_emails": ["<EMAIL>"],
    "alert_title": "CPU使用率过高",
    "alert_message": "当前CPU使用率已达到85%",
    "alert_type": "warning"
  }'

# 2. 发送爬虫状态通知
curl -X POST "http://localhost:8000/api/v1/email/alert/crawler" \\
  -H "Authorization: Bearer your_token" \\
  -H "Content-Type: application/json" \\
  -d '{
    "to_emails": ["<EMAIL>"],
    "notification_title": "爬虫账号异常",
    "notification_message": "检测到账号登录失效",
    "status_type": "warning"
  }'

# 3. 测试邮件连接
curl -X GET "http://localhost:8000/api/v1/email/test" \\
  -H "Authorization: Bearer your_token"

# 4. 发送测试邮件
curl -X POST "http://localhost:8000/api/v1/email/test-send?to_email=<EMAIL>" \\
  -H "Authorization: Bearer your_token"
"""
    
    print(examples)

async def main():
    """主函数"""
    print("=" * 60)
    print("🚀 AqentCrawler 邮件功能初始化")
    print("=" * 60)
    
    # 1. 检查模板文件
    templates_ok = init_email_templates()
    
    # 2. 检查配置
    config_ok = test_email_service()
    
    # 3. 测试连接（如果配置正确）
    connection_ok = False
    if config_ok:
        connection_ok = await test_email_connection()
    
    # 4. 创建示例配置
    create_sample_email_config()
    
    # 5. 显示使用示例
    show_usage_examples()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 初始化结果总结")
    print("=" * 60)
    print(f"📁 邮件模板: {'✅ 完整' if templates_ok else '❌ 缺失'}")
    print(f"⚙️ 环境配置: {'✅ 完整' if config_ok else '❌ 缺失'}")
    print(f"🌐 服务器连接: {'✅ 正常' if connection_ok else '❌ 失败' if config_ok else '⏭️ 跳过'}")
    
    if templates_ok and config_ok and connection_ok:
        print("\n🎉 邮件功能初始化完成，可以正常使用！")
    elif templates_ok and config_ok:
        print("\n⚠️ 邮件功能基本配置完成，但连接测试失败，请检查网络和邮箱设置")
    else:
        print("\n❌ 邮件功能初始化未完成，请按照提示完成配置")
    
    print("\n📖 详细使用说明请查看: 邮件功能使用文档.md")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
