#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试1688平台修复后的组件查找和SKU属性问题
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.crawler.platforms.alibaba_1688 import Alibaba1688Crawler

async def test_1688_debug_fixes():
    """测试1688平台修复后的功能"""
    
    # 测试商品URL - 印度小叶紫檀佛珠
    test_url = "https://detail.1688.com/offer/591940079968.html"
    
    print(f"🧪 测试1688平台修复后的功能")
    print(f"📦 测试商品: {test_url}")
    print("=" * 80)
    
    # 创建爬虫实例
    crawler = Alibaba1688Crawler()
    
    try:
        print(f"📡 开始获取商品详情...")
        
        # 调用商品详情获取方法
        result = await crawler.get_product_detail(test_url)
        
        if result and result.get('success'):
            product_data = result.get('data', {})
            
            print(f"\n✅ 商品详情获取成功!")
            print(f"📦 商品名称: {product_data.get('name', '')}")
            print(f"💰 价格: {product_data.get('price')} 分")
            print(f"📋 SKU数量: {len(product_data.get('skus', []))}")
            
            # 重点检查SKU属性信息
            skus = product_data.get('skus', [])
            if skus:
                print(f"\n🔍 SKU属性详细检查:")
                for i, sku in enumerate(skus[:5]):  # 检查前5个SKU
                    print(f"\n  SKU {i+1}:")
                    print(f"    - ID: {sku.get('id')}")
                    print(f"    - 价格: {sku.get('price')} 分")
                    print(f"    - 库存: {sku.get('stock')}")
                    
                    properties = sku.get('properties', [])
                    print(f"    - 属性数量: {len(properties)}")
                    
                    for j, prop in enumerate(properties):
                        property_id = prop.get('propertyId')
                        property_name = prop.get('propertyName')
                        value_id = prop.get('valueId')
                        value_name = prop.get('valueName')
                        
                        print(f"      属性{j+1}:")
                        print(f"        propertyId: {property_id}")
                        print(f"        propertyName: {property_name}")
                        print(f"        valueId: {value_id}")
                        print(f"        valueName: {value_name}")
                        
                        # 检查是否还有null值
                        if property_name is None:
                            print(f"        ❌ propertyName为null!")
                        if value_id is None:
                            print(f"        ❌ valueId为null!")
                        if value_name is None:
                            print(f"        ❌ valueName为null!")
                
                # 统计属性维度
                all_properties = {}
                for sku in skus:
                    for prop in sku.get('properties', []):
                        prop_name = prop.get('propertyName', '未知属性')
                        prop_value = prop.get('valueName', '未知值')
                        
                        if prop_name not in all_properties:
                            all_properties[prop_name] = set()
                        all_properties[prop_name].add(prop_value)
                
                print(f"\n📊 属性维度统计:")
                print(f"   - 属性维度数: {len(all_properties)}")
                for prop_name, values in all_properties.items():
                    print(f"     * {prop_name}: {len(values)}个值")
                    if len(values) <= 10:
                        print(f"       值列表: {', '.join(sorted(values))}")
                    else:
                        sample_values = list(sorted(values))[:5]
                        print(f"       值列表(前5个): {', '.join(sample_values)}...")
            else:
                print(f"\n⚠️ 没有找到SKU数据")
                
        else:
            error_msg = result.get('error', '未知错误') if result else '返回结果为空'
            print(f"\n❌ 商品详情获取失败: {error_msg}")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_1688_debug_fixes())
