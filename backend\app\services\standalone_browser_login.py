#!/usr/bin/env python3
"""
独立浏览器登录脚本

使用平台特定的登录服务，支持扩展新平台
"""

import sys
import os
import json
from datetime import datetime
from playwright.sync_api import sync_playwright

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, backend_dir)

from app.database import get_db
from app.models import CrawlerAccount
from app.services.platform_login import TaobaoLogin, Alibaba1688Login


# 平台登录服务映射
PLATFORM_SERVICES = {
    "taobao": TaobaoLogin,
    "1688": Alibaba1688Login,
    # 可以在这里添加更多平台
    # "jingdong": JingdongLogin,
    # "pinduoduo": PinduoduoLogin,
}


def get_platform_service(platform_code: str):
    """获取平台登录服务"""
    service_class = PLATFORM_SERVICES.get(platform_code)
    if service_class:
        return service_class()
    else:
        raise ValueError(f"不支持的平台: {platform_code}")


def get_chrome_user_data_dir(platform_id: int, username: str) -> str:
    """获取Chrome用户数据目录"""
    # 从环境变量获取基础目录
    base_dir = os.getenv('CHROME_USER_DATA_DIR', 'C:\\AgentCrawler\\ChromeUserData')

    # 为每个平台和用户创建独立目录
    user_data_dir = os.path.join(base_dir, f"platform_{platform_id}_{username}")

    # 确保目录存在
    os.makedirs(user_data_dir, exist_ok=True)

    return user_data_dir








def update_account_in_database(account_id: int, login_result: dict):
    """更新数据库中的账号信息"""
    try:
        db = next(get_db())
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        
        if not account:
            return {"success": False, "message": "账号不存在"}

        if login_result.get("success") and login_result.get("data"):
            data = login_result["data"]
            
            # 更新账号信息
            account.cookie = data.get("cookie")
            account.token = data.get("token")
            account.user_agent = data.get("user_agent")
            account.login_status = 'logged_in'
            
            # 处理token过期时间
            if data.get("token_expires_at"):
                try:
                    account.token_expires_at = datetime.fromisoformat(data["token_expires_at"])
                except:
                    pass
            
            account.last_login_at = datetime.now()
            account.updated_at = datetime.now()
            account.last_error_message = None
            
            db.commit()
            print(f"账号 {account.username} 信息已更新到数据库", file=sys.stderr)
            
        else:
            # 登录失败，更新错误信息
            account.login_status = 'login_failed'
            account.last_error_message = login_result.get("message", "登录失败")
            account.updated_at = datetime.now()
            db.commit()
            print(f"账号 {account.username} 登录失败，错误信息已更新", file=sys.stderr)
        
        db.close()
        return {"success": True, "message": "数据库更新成功"}
        
    except Exception as e:
        print(f"更新数据库失败: {str(e)}", file=sys.stderr)
        return {"success": False, "message": f"更新数据库失败: {str(e)}"}


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python standalone_browser_login.py <account_id>", file=sys.stderr)
        sys.exit(1)

    account_id = int(sys.argv[1])

    try:
        # 设置输出编码为UTF-8
        import io
        import codecs

        # 重定向stdout到临时变量，避免数据库连接信息污染输出
        old_stdout = sys.stdout
        sys.stdout = io.StringIO()

        # 获取数据库连接
        db = next(get_db())

        # 恢复stdout，并设置编码
        sys.stdout = old_stdout

        # 确保stderr使用UTF-8编码
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')
        elif hasattr(sys.stderr, 'buffer'):
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
        
        # 获取账号信息
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        if not account:
            result = {"success": False, "message": "账号不存在"}
            print(json.dumps(result, ensure_ascii=False))
            sys.exit(1)

        print(f"开始登录账号: {account.username}", file=sys.stderr)
        print(f"平台: {account.platform.code}", file=sys.stderr)
        print(f"账号ID: {account.id}", file=sys.stderr)

        # 获取平台登录服务
        try:
            platform_service = get_platform_service(account.platform.code)
        except ValueError as e:
            result = {"success": False, "message": str(e)}
            print(json.dumps(result, ensure_ascii=False))
            sys.exit(1)

        # 获取Chrome用户数据目录
        user_data_dir = get_chrome_user_data_dir(account.platform_id, account.username)

        # 配置代理（如果有）
        proxy_config = None
        if account.proxy_id:
            from app.models import ProxyPool
            proxy = db.query(ProxyPool).filter(ProxyPool.id == account.proxy_id).first()
            if proxy and proxy.is_enabled and proxy.host and proxy.port:
                proxy_config = {
                    "server": f"http://{proxy.host}:{proxy.port}"
                }

                # 添加代理认证
                if proxy.username and proxy.password:
                    from app.utils.crypto import decrypt_password
                    decrypted_password = decrypt_password(proxy.password)
                    if decrypted_password:
                        proxy_config["username"] = proxy.username
                        proxy_config["password"] = decrypted_password

                print(f"使用代理: {proxy.host}:{proxy.port}", file=sys.stderr)

        # 使用Playwright启动Chrome浏览器
        print("初始化Playwright...", file=sys.stderr)
        with sync_playwright() as playwright:
            # 构建启动参数
            launch_args = {
                "user_data_dir": user_data_dir,
                "headless": False,  # 显示浏览器以便人工验证
                "args": [
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled",
                    "--exclude-switches=enable-automation",
                    "--disable-gpu",
                    "--disable-software-rasterizer",
                    "--no-first-run"
                ],
                "viewport": {"width": 1920, "height": 1080},
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }

            # 添加代理配置
            if proxy_config:
                # 使用Chrome命令行参数方式配置代理（更可靠）
                proxy_server = proxy_config["server"]
                launch_args["args"].append(f"--proxy-server={proxy_server}")
                print(f"[BROWSER_LAUNCH] 添加代理参数: --proxy-server={proxy_server}", file=sys.stderr)

                # 如果有代理认证，需要特殊处理
                if "username" in proxy_config and "password" in proxy_config:
                    print(f"[BROWSER_LAUNCH] 代理认证: {proxy_config['username']}:***", file=sys.stderr)
                    # 注意：Chrome命令行不直接支持代理认证，需要通过其他方式处理
                    # 这里我们仍然使用Playwright的proxy配置来处理认证
                    launch_args["proxy"] = proxy_config

            # 打印完整的浏览器启动参数
            print(f"[BROWSER_LAUNCH] Chrome用户数据目录: {user_data_dir}", file=sys.stderr)
            print(f"[BROWSER_LAUNCH] Chrome启动参数: {launch_args['args']}", file=sys.stderr)
            print(f"[BROWSER_LAUNCH] 代理配置: {proxy_config}", file=sys.stderr)

            # 使用持久化上下文，这样可以保存登录状态
            context = playwright.chromium.launch_persistent_context(**launch_args)

            # 设置浏览器上下文（反检测等）
            platform_service.setup_browser_context(context)

            page = context.new_page()

            # 执行登录
            print(f"开始执行{account.platform.code}登录流程...", file=sys.stderr)
            result = platform_service.login(page, account)

            # 更新数据库
            if result.get("success"):
                update_result = update_account_in_database(account_id, result)
                if not update_result.get("success"):
                    result["message"] += f" (数据库更新失败: {update_result.get('message')})"

            # 输出结果到stdout
            print(json.dumps(result, ensure_ascii=False))

            # 关闭浏览器
            try:
                context.close()
            except:
                pass

    except Exception as e:
        result = {"success": False, "message": f"登录过程发生异常: {str(e)}"}
        print(json.dumps(result, ensure_ascii=False))
        sys.exit(1)


if __name__ == "__main__":
    main()
