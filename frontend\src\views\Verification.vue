<template>
  <div class="verification-container">
    <!-- 页面标题和统计 -->
    <div class="header-section">
      <n-card>
        <div class="header-content">
          <div class="title-section">
            <h2>人工验证管理</h2>
            <p>处理需要人工干预的登录验证任务</p>
          </div>
          <div class="stats-section">
            <n-statistic label="待处理任务" :value="stats.pending_tasks" />
            <n-statistic label="今日完成" :value="stats.completed_today" />
            <n-statistic label="成功率" :value="stats.success_rate" suffix="%" />
          </div>
        </div>
      </n-card>
    </div>

    <!-- 设置面板 -->
    <div class="settings-section">
      <n-card title="验证设置">
        <n-space>
          <n-switch v-model:value="settings.enableThirdParty" @update:value="updateSettings">
            <template #checked>第三方打码</template>
            <template #unchecked>人工验证</template>
          </n-switch>
          <n-switch v-model:value="settings.autoRefresh" @update:value="updateSettings">
            <template #checked>自动刷新</template>
            <template #unchecked>手动刷新</template>
          </n-switch>
          <n-input-number
            v-model:value="settings.refreshInterval"
            :min="5"
            :max="60"
            suffix="秒"
            style="width: 120px"
            @update:value="updateSettings"
          />
        </n-space>
      </n-card>
    </div>

    <!-- 待处理任务列表 -->
    <div class="tasks-section">
      <n-card>
        <template #header>
          <div class="tasks-header">
            <span>待处理任务 ({{ pendingTasks.length }})</span>
            <n-space>
              <n-button @click="refreshTasks" :loading="loading">
                <template #icon>
                  <n-icon><RefreshIcon /></n-icon>
                </template>
                刷新
              </n-button>
              <n-button type="primary" @click="showBatchModal = true" :disabled="selectedTasks.length === 0">
                批量处理 ({{ selectedTasks.length }})
              </n-button>
            </n-space>
          </div>
        </template>

        <!-- 任务列表 -->
        <n-data-table
          :columns="taskColumns"
          :data="pendingTasks"
          :loading="loading"
          :row-key="row => row.task_id"
          @update:checked-row-keys="handleTaskSelection"
          :scroll-x="1200"
        />
      </n-card>
    </div>

    <!-- 验证处理模态框 -->
    <n-modal v-model:show="showVerificationModal" :mask-closable="false" style="width: 800px">
      <n-card title="处理验证任务" :bordered="false" size="huge">
        <template #header-extra>
          <n-button quaternary circle @click="showVerificationModal = false">
            <template #icon>
              <n-icon><CloseIcon /></n-icon>
            </template>
          </n-button>
        </template>

        <div v-if="currentTask" class="verification-content">
          <!-- 任务信息 -->
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="任务ID">{{ currentTask.task_id }}</n-descriptions-item>
            <n-descriptions-item label="平台">{{ getPlatformName(currentTask.platform) }}</n-descriptions-item>
            <n-descriptions-item label="用户名">{{ currentTask.username }}</n-descriptions-item>
            <n-descriptions-item label="验证类型">{{ getVerificationTypeName(currentTask.verification_type) }}</n-descriptions-item>
            <n-descriptions-item label="创建时间">{{ currentTask.created_at }}</n-descriptions-item>
            <n-descriptions-item label="剩余时间">{{ getRemainingTime(currentTask) }}</n-descriptions-item>
          </n-descriptions>

          <n-divider />

          <!-- 验证内容 -->
          <div class="verification-area">
            <!-- 扫码验证 -->
            <div v-if="currentTask.verification_type === 'qrcode'" class="qrcode-verification">
              <h3>扫码登录</h3>
              <div class="qr-container">
                <img :src="currentTask.data.qr_image" alt="二维码" class="qr-image" />
                <p>{{ currentTask.data.instruction }}</p>
              </div>
              <n-space vertical>
                <n-button type="primary" @click="refreshQRCode" :loading="refreshingQR">
                  刷新二维码
                </n-button>
                <n-button type="success" @click="completeQRCodeVerification">
                  扫码完成
                </n-button>
              </n-space>
            </div>

            <!-- 短信验证 -->
            <div v-else-if="currentTask.verification_type === 'sms'" class="sms-verification">
              <h3>短信验证码</h3>
              <p>{{ currentTask.data.instruction }}</p>
              <p>手机号：{{ currentTask.data.phone_number }}</p>
              <n-space vertical>
                <n-input
                  v-model:value="smsCode"
                  placeholder="请输入6位验证码"
                  :maxlength="6"
                  size="large"
                  style="width: 200px"
                />
                <n-button type="primary" @click="completeSMSVerification" :disabled="!smsCode || smsCode.length !== 6">
                  提交验证码
                </n-button>
              </n-space>
            </div>

            <!-- 滑块验证 -->
            <div v-else-if="currentTask.verification_type === 'slider'" class="slider-verification">
              <h3>滑块验证</h3>
              <p>{{ currentTask.data.instruction }}</p>
              <div class="slider-container">
                <img :src="currentTask.data.background_image" alt="背景图" class="slider-bg" />
                <img :src="currentTask.data.slider_image" alt="滑块" class="slider-piece" />
              </div>
              <n-space vertical>
                <n-input-number
                  v-model:value="sliderDistance"
                  placeholder="请输入滑动距离(像素)"
                  :min="0"
                  :max="500"
                  style="width: 200px"
                />
                <n-button type="primary" @click="completeSliderVerification" :disabled="!sliderDistance">
                  提交滑动距离
                </n-button>
              </n-space>
            </div>

            <!-- 图形验证码 -->
            <div v-else-if="currentTask.verification_type === 'captcha'" class="captcha-verification">
              <h3>图形验证码</h3>
              <div class="captcha-container">
                <img :src="currentTask.data.image" alt="验证码" class="captcha-image" />
              </div>
              <n-space vertical>
                <n-input
                  v-model:value="captchaText"
                  placeholder="请输入验证码"
                  size="large"
                  style="width: 200px"
                />
                <n-button type="primary" @click="completeCaptchaVerification" :disabled="!captchaText">
                  提交验证码
                </n-button>
              </n-space>
            </div>

            <!-- 其他验证类型 -->
            <div v-else class="other-verification">
              <h3>{{ getVerificationTypeName(currentTask.verification_type) }}</h3>
              <p>{{ currentTask.data.instruction || '请按照提示完成验证' }}</p>
              <n-space vertical>
                <n-input
                  v-model:value="otherResult"
                  placeholder="请输入验证结果"
                  size="large"
                  style="width: 300px"
                />
                <n-button type="primary" @click="completeOtherVerification" :disabled="!otherResult">
                  提交结果
                </n-button>
              </n-space>
            </div>
          </div>
        </div>

        <template #footer>
          <n-space justify="end">
            <n-button @click="showVerificationModal = false">取消</n-button>
            <n-button type="error" @click="failTask">标记失败</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>

    <!-- 批量处理模态框 -->
    <n-modal v-model:show="showBatchModal" style="width: 600px">
      <n-card title="批量处理任务" :bordered="false" size="huge">
        <p>选中了 {{ selectedTasks.length }} 个任务，请选择处理方式：</p>
        <n-space vertical>
          <n-button type="primary" @click="batchComplete" block>
            批量标记完成
          </n-button>
          <n-button type="error" @click="batchFail" block>
            批量标记失败
          </n-button>
        </n-space>
        <template #footer>
          <n-space justify="end">
            <n-button @click="showBatchModal = false">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, h } from 'vue'
import {
  NCard, NStatistic, NSpace, NSwitch, NInputNumber, NButton, NIcon,
  NDataTable, NModal, NDescriptions, NDescriptionsItem, NDivider,
  NInput, useMessage
} from 'naive-ui'
import { Refresh as RefreshIcon, Close as CloseIcon } from '@vicons/ionicons5'
import { verificationAPI } from '../api'

// 响应式数据
const loading = ref(false)
const showVerificationModal = ref(false)
const showBatchModal = ref(false)
const refreshingQR = ref(false)

// 统计数据
const stats = reactive({
  pending_tasks: 0,
  completed_today: 0,
  success_rate: 0
})

// 设置
const settings = reactive({
  enableThirdParty: false,
  autoRefresh: true,
  refreshInterval: 10
})

// 任务数据
const pendingTasks = ref([])
const selectedTasks = ref([])
const currentTask = ref(null)

// 验证输入
const smsCode = ref('')
const sliderDistance = ref(null)
const captchaText = ref('')
const otherResult = ref('')

// 自动刷新定时器
let refreshTimer = null

// 消息提示
const message = useMessage()

// 表格列定义
const taskColumns = [
  {
    type: 'selection'
  },
  {
    title: '任务ID',
    key: 'task_id',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '平台',
    key: 'platform',
    width: 80,
    render(row) {
      return getPlatformName(row.platform)
    }
  },
  {
    title: '用户名',
    key: 'username',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '验证类型',
    key: 'verification_type',
    width: 100,
    render(row) {
      return getVerificationTypeName(row.verification_type)
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 160
  },
  {
    title: '剩余时间',
    key: 'timeout',
    width: 100,
    render(row) {
      return getRemainingTime(row)
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render(row) {
      return h(NButton, {
        type: 'primary',
        size: 'small',
        onClick: () => handleTask(row)
      }, { default: () => '处理' })
    }
  }
]

// 方法定义
const refreshTasks = async () => {
  loading.value = true
  try {
    const response = await verificationAPI.getPendingTasks()
    if (response.code === 200) {
      pendingTasks.value = response.data || []
      updateStats()
    } else {
      message.error('获取任务失败：' + response.message)
    }
  } catch (error) {
    message.error('网络错误：' + error.message)
  } finally {
    loading.value = false
  }
}

const updateStats = async () => {
  try {
    const response = await verificationAPI.getStats()
    if (response.code === 200) {
      Object.assign(stats, response.data)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const updateSettings = () => {
  // 保存设置到本地存储
  localStorage.setItem('verification_settings', JSON.stringify(settings))

  // 重新设置自动刷新
  setupAutoRefresh()

  message.success('设置已保存')
}

const setupAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }

  if (settings.autoRefresh) {
    refreshTimer = setInterval(() => {
      refreshTasks()
    }, settings.refreshInterval * 1000)
  }
}

const handleTaskSelection = (keys) => {
  selectedTasks.value = keys
}

const handleTask = (task) => {
  currentTask.value = task
  // 重置输入值
  smsCode.value = ''
  sliderDistance.value = null
  captchaText.value = ''
  otherResult.value = ''
  showVerificationModal.value = true
}

const getPlatformName = (platform) => {
  const platforms = {
    'taobao': '淘宝',
    'jd': '京东',
    'pdd': '拼多多',
    '1688': '阿里巴巴'
  }
  return platforms[platform] || platform
}

const getVerificationTypeName = (type) => {
  const types = {
    'qrcode': '扫码登录',
    'sms': '短信验证',
    'slider': '滑块验证',
    'captcha': '图形验证码',
    'puzzle': '拼图验证',
    'click': '点击验证',
    'password': '密码验证',
    'email': '邮箱验证'
  }
  return types[type] || type
}

const getRemainingTime = (task) => {
  // 计算剩余时间的逻辑
  const created = new Date(task.created_at).getTime()
  const now = new Date().getTime()
  const elapsed = Math.floor((now - created) / 1000)
  const remaining = Math.max(0, (task.timeout || 300) - elapsed)

  if (remaining > 60) {
    return Math.floor(remaining / 60) + '分钟'
  } else {
    return remaining + '秒'
  }
}

// 验证完成方法
const completeQRCodeVerification = async () => {
  await completeVerification('扫码完成')
}

const completeSMSVerification = async () => {
  await completeVerification(smsCode.value)
}

const completeSliderVerification = async () => {
  await completeVerification(sliderDistance.value.toString())
}

const completeCaptchaVerification = async () => {
  await completeVerification(captchaText.value)
}

const completeOtherVerification = async () => {
  await completeVerification(otherResult.value)
}

const completeVerification = async (result) => {
  try {
    const response = await verificationAPI.completeTask({
      task_id: currentTask.value.task_id,
      result: result,
      verification_type: currentTask.value.verification_type
    })

    if (response.code === 200) {
      message.success('验证任务完成')
      showVerificationModal.value = false
      refreshTasks()
    } else {
      message.error('完成任务失败：' + response.message)
    }
  } catch (error) {
    message.error('网络错误：' + error.message)
  }
}

const failTask = async () => {
  try {
    const response = await verificationAPI.completeTask({
      task_id: currentTask.value.task_id,
      result: 'FAILED',
      verification_type: currentTask.value.verification_type
    })

    if (response.code === 200) {
      message.warning('任务已标记为失败')
      showVerificationModal.value = false
      refreshTasks()
    } else {
      message.error('标记失败：' + response.message)
    }
  } catch (error) {
    message.error('网络错误：' + error.message)
  }
}

const refreshQRCode = () => {
  refreshingQR.value = true
  // 模拟刷新二维码
  setTimeout(() => {
    refreshingQR.value = false
    message.info('二维码已刷新')
  }, 1000)
}

const batchComplete = async () => {
  // 批量完成任务
  for (const taskId of selectedTasks.value) {
    try {
      await verificationAPI.completeTask({
        task_id: taskId,
        result: 'BATCH_COMPLETED',
        verification_type: 'batch'
      })
    } catch (error) {
      console.error('批量完成任务失败:', error)
    }
  }
  message.success(`已批量完成 ${selectedTasks.value.length} 个任务`)
  showBatchModal.value = false
  selectedTasks.value = []
  refreshTasks()
}

const batchFail = async () => {
  // 批量失败任务
  for (const taskId of selectedTasks.value) {
    try {
      await verificationAPI.completeTask({
        task_id: taskId,
        result: 'BATCH_FAILED',
        verification_type: 'batch'
      })
    } catch (error) {
      console.error('批量失败任务失败:', error)
    }
  }
  message.warning(`已批量标记 ${selectedTasks.value.length} 个任务为失败`)
  showBatchModal.value = false
  selectedTasks.value = []
  refreshTasks()
}

// 生命周期
onMounted(() => {
  // 加载设置
  const savedSettings = localStorage.getItem('verification_settings')
  if (savedSettings) {
    Object.assign(settings, JSON.parse(savedSettings))
  }

  // 初始化数据
  refreshTasks()
  updateStats()
  setupAutoRefresh()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.verification-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header-section {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.title-section p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-section {
  display: flex;
  gap: 40px;
}

.settings-section {
  margin-bottom: 20px;
}

.tasks-section {
  margin-bottom: 20px;
}

.tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.verification-content {
  max-height: 600px;
  overflow-y: auto;
}

.verification-area {
  margin-top: 20px;
}

.qrcode-verification {
  text-align: center;
}

.qr-container {
  margin: 20px 0;
}

.qr-image {
  max-width: 200px;
  max-height: 200px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 10px;
}

.sms-verification,
.slider-verification,
.captcha-verification,
.other-verification {
  text-align: center;
}

.slider-container {
  margin: 20px 0;
  position: relative;
  display: inline-block;
}

.slider-bg {
  max-width: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.slider-piece {
  max-width: 60px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-left: 10px;
}

.captcha-container {
  margin: 20px 0;
}

.captcha-image {
  max-width: 150px;
  max-height: 60px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .verification-container {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .stats-section {
    flex-direction: column;
    gap: 10px;
  }

  .tasks-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 任务状态指示器 */
.task-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.task-status.pending {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.task-status.processing {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.task-status.urgent {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

/* 验证类型图标 */
.verification-type-icon {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
</style>