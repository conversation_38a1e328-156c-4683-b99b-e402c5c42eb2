#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接调试1688 SKU原始数据
"""

import sys
import os
sys.path.append('backend')

import asyncio
from app.crawler.platforms.alibaba_1688 import Alibaba1688Crawler
import json

async def debug_raw_sku_data():
    """直接调试SKU原始数据"""
    print("🔍 初始化1688爬虫...")
    
    crawler = Alibaba1688Crawler()
    url = "https://detail.1688.com/offer/932222752479.html"
    
    try:
        print(f"📡 获取商品页面: {url}")
        
        # 直接获取页面内容
        import requests
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=30)
        html_content = response.text
        print(f"✅ 页面内容长度: {len(html_content)}")
        print(f"✅ 实际访问的URL: {response.url}")

        # 检查页面标题
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        title = soup.find('title')
        if title:
            print(f"✅ 页面标题: {title.get_text()}")
        
        # 提取初始化数据
        print("\n🔍 提取初始化数据...")
        init_data = crawler._extract_init_data_from_html(html_content)
        
        if init_data:
            print("✅ 成功提取初始化数据")
            
            # 查找SKU模型数据
            result_section = init_data.get('result', {})
            data_section = result_section.get('data', {})
            root_component = data_section.get('Root', {})
            
            if root_component:
                fields = root_component.get('fields', {})
                data_json = fields.get('dataJson', {})
                sku_model = data_json.get('skuModel')
                
                if sku_model:
                    print(f"\n✅ 找到skuModel数据")
                    
                    # 打印SKU属性组
                    sku_props = sku_model.get('skuProps', [])
                    print(f"\n📋 SKU属性组 (共{len(sku_props)}个):")
                    for i, prop_group in enumerate(sku_props):
                        print(f"\n  属性组 {i+1}:")
                        print(f"    - prop: {prop_group.get('prop')}")
                        print(f"    - fid: {prop_group.get('fid')}")
                        values = prop_group.get('value', [])
                        print(f"    - 值数量: {len(values)}")
                        print(f"    - 所有值:")
                        for j, value in enumerate(values):
                            print(f"      [{j+1}] name: '{value.get('name')}', vid: {value.get('vid')}")
                    
                    # 打印SKU信息映射
                    sku_info_map = sku_model.get('skuInfoMap', {})
                    print(f"\n📋 SKU信息映射 (共{len(sku_info_map)}个):")
                    
                    # 按SKU名称排序显示
                    sorted_skus = sorted(sku_info_map.items())
                    for i, (sku_name, sku_info) in enumerate(sorted_skus):
                        print(f"\n  SKU {i+1}: '{sku_name}'")
                        print(f"    - price: {sku_info.get('price')}")
                        print(f"    - canBookCount: {sku_info.get('canBookCount')}")
                        print(f"    - specAttrs: {sku_info.get('specAttrs')}")
                        
                        # 如果有specAttrs，详细分析
                        spec_attrs = sku_info.get('specAttrs')
                        if spec_attrs:
                            print(f"    - specAttrs详细:")
                            if isinstance(spec_attrs, str):
                                print(f"      字符串: {spec_attrs}")
                            elif isinstance(spec_attrs, dict):
                                for key, value in spec_attrs.items():
                                    print(f"      {key}: {value}")
                            elif isinstance(spec_attrs, list):
                                for j, attr in enumerate(spec_attrs):
                                    print(f"      [{j}]: {attr}")
                    
                    # 分析SKU名称的模式
                    print(f"\n🔍 SKU名称模式分析:")
                    sku_names = list(sku_info_map.keys())
                    
                    # 检查分隔符
                    separators = ['>', '&gt;', ';', ',', '-', '|', '_', ' ']
                    for sep in separators:
                        count = sum(1 for name in sku_names if sep in name)
                        if count > 0:
                            print(f"  - 包含 '{sep}' 的SKU: {count}个")
                            # 显示几个例子
                            examples = [name for name in sku_names if sep in name][:3]
                            for example in examples:
                                parts = example.split(sep)
                                print(f"    例子: '{example}' -> {parts}")
                
                else:
                    print("❌ 未找到skuModel数据")
            else:
                print("❌ 未找到Root组件")
        else:
            print("❌ 未能提取到初始化数据")
            
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_raw_sku_data())
