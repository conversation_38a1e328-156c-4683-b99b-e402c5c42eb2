#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索淘宝商品获取测试URL
"""

import requests
import json

def search_taobao_products():
    """搜索淘宝商品"""
    print("🔍 搜索淘宝商品...")
    
    data = {
        "keyword": "手机壳",
        "platform": "taobao",
        "language": "zh",
        "page": 1
    }
    
    try:
        response = requests.post("http://localhost:8000/api/v1/upstream/product/search", json=data)
        result = response.json()
        
        if result.get('code') == 200:
            search_data = result.get('data', {})
            products = search_data.get('products', [])
            
            print(f"✅ 找到 {len(products)} 个商品")
            
            for i, product in enumerate(products[:3]):  # 只显示前3个
                print(f"\n商品 {i+1}:")
                print(f"  - ID: {product.get('id')}")
                print(f"  - 名称: {product.get('name', '')[:50]}...")
                print(f"  - 链接: {product.get('sourceLink')}")
                print(f"  - 规格类型: {product.get('specType')}")
                
            # 返回第一个商品的链接用于详情测试
            if products:
                return products[0].get('sourceLink')
                
        else:
            print(f"❌ 搜索失败: {result.get('code')}")
            print(f"响应: {result}")
            
    except Exception as e:
        print(f"❌ 搜索失败: {str(e)}")
    
    return None

if __name__ == "__main__":
    url = search_taobao_products()
    if url:
        print(f"\n🎯 测试URL: {url}")
    else:
        print("❌ 未获取到测试URL")
