# AqentCrawler 邮件功能使用文档

## 📧 功能概述

AqentCrawler 邮件功能提供了完整的邮件发送服务，支持HTML模板、系统告警、爬虫状态通知、API报告等多种邮件类型。

## 🔧 配置说明

### 环境变量配置

在 `.env` 文件中添加以下邮件配置：

```bash
# 邮件服务配置
SMTP_SERVER=smtp.qq.com              # SMTP服务器地址
SMTP_PORT=587                        # SMTP端口 (587-TLS, 465-SSL, 25-无加密)
SMTP_USERNAME=<EMAIL>      # SMTP用户名
SMTP_PASSWORD=your_app_password      # SMTP密码或应用专用密码
SMTP_USE_TLS=true                    # 是否使用TLS加密
SMTP_USE_SSL=false                   # 是否使用SSL加密
SENDER_EMAIL=<EMAIL>       # 发件人邮箱
SENDER_NAME=AqentCrawler系统         # 发件人名称
```

### 常用邮箱配置

#### QQ邮箱
```bash
SMTP_SERVER=smtp.qq.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USE_SSL=false
```

#### 163邮箱
```bash
SMTP_SERVER=smtp.163.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USE_SSL=false
```

#### Gmail
```bash
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USE_SSL=false
```

#### 企业邮箱
```bash
SMTP_SERVER=smtp.exmail.qq.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USE_SSL=false
```

## 📝 API接口说明

### 1. 测试邮件连接

**接口**: `GET /api/v1/email/test`

**说明**: 测试邮件服务器连接是否正常

**响应**:
```json
{
  "code": 200,
  "message": "邮件服务器连接正常"
}
```

### 2. 发送测试邮件

**接口**: `POST /api/v1/email/test-send`

**参数**:
```json
{
  "to_email": "<EMAIL>"
}
```

**说明**: 发送一封测试邮件验证邮件功能

### 3. 发送普通邮件

**接口**: `POST /api/v1/email/send`

**参数**:
```json
{
  "to_emails": ["<EMAIL>", "<EMAIL>"],
  "subject": "邮件主题",
  "content": "<h1>邮件内容</h1><p>这是HTML内容</p>",
  "content_type": "html",
  "cc_emails": ["<EMAIL>"],
  "bcc_emails": ["<EMAIL>"],
  "reply_to": "<EMAIL>"
}
```

### 4. 发送模板邮件

**接口**: `POST /api/v1/email/send-template`

**参数**:
```json
{
  "to_emails": ["<EMAIL>"],
  "template_name": "notification",
  "subject": "系统通知",
  "template_data": {
    "title": "通知标题",
    "message": "通知内容",
    "notification_type": "success"
  }
}
```

### 5. 发送系统告警邮件

**接口**: `POST /api/v1/email/alert/system`

**参数**:
```json
{
  "to_emails": ["<EMAIL>"],
  "alert_title": "系统CPU使用率过高",
  "alert_message": "当前CPU使用率已达到85%，请及时处理",
  "alert_type": "warning",
  "alert_details": {
    "服务器": "web-server-01",
    "CPU使用率": "85%",
    "内存使用率": "72%",
    "磁盘使用率": "45%"
  },
  "affected_services": [
    {
      "name": "API服务",
      "status": "warning",
      "description": "响应时间增加"
    }
  ],
  "metrics": [
    {
      "name": "CPU使用率",
      "current_value": "85%",
      "threshold": "80%",
      "status": "error",
      "status_text": "超出阈值"
    }
  ],
  "recommendations": [
    "检查是否有异常进程占用CPU",
    "考虑增加服务器资源",
    "优化应用程序性能"
  ],
  "action_url": "https://your-domain.com/dashboard"
}
```

### 6. 发送爬虫状态通知

**接口**: `POST /api/v1/email/alert/crawler`

**参数**:
```json
{
  "to_emails": ["<EMAIL>"],
  "notification_title": "爬虫账号状态异常",
  "notification_message": "检测到多个爬虫账号登录失效，请及时处理",
  "status_type": "warning",
  "crawler_accounts": [
    {
      "platform": "taobao",
      "platform_name": "淘宝",
      "username": "test_user_001",
      "status": "login_failed",
      "status_text": "登录失败",
      "status_class": "error",
      "updated_at": "2025-01-20 10:30:00",
      "remark": "密码可能已过期"
    }
  ],
  "token_info": [
    {
      "platform": "taobao",
      "platform_name": "淘宝",
      "username": "test_user_001",
      "status": "expired",
      "status_text": "已过期",
      "status_class": "error",
      "expires_at": "2025-01-20 09:00:00",
      "remaining_time": "已过期2小时",
      "is_expiring": true
    }
  ],
  "statistics": [
    {
      "name": "总账号数",
      "value": "15",
      "unit": "个"
    },
    {
      "name": "可用账号数",
      "value": "12",
      "unit": "个",
      "change": -3
    }
  ],
  "next_actions": [
    "重新登录失效的账号",
    "检查账号密码是否正确",
    "更新Token信息"
  ]
}
```

### 7. 发送API调用报告

**接口**: `POST /api/v1/email/report/api`

**参数**:
```json
{
  "to_emails": ["<EMAIL>"],
  "report_title": "每日API调用统计报告",
  "report_description": "2025年1月20日API调用情况统计",
  "report_period": {
    "start": "2025-01-20 00:00:00",
    "end": "2025-01-20 23:59:59",
    "duration": "24小时"
  },
  "summary_stats": [
    {
      "name": "总调用次数",
      "value": "1,234",
      "unit": "次",
      "trend": "up",
      "change": "+15%"
    },
    {
      "name": "成功率",
      "value": "98.5",
      "unit": "%",
      "trend": "up"
    }
  ],
  "api_endpoints": [
    {
      "path": "/api/v1/search",
      "total_calls": 856,
      "success_rate": 98.2,
      "avg_response_time": 1250
    },
    {
      "path": "/api/v1/detail",
      "total_calls": 378,
      "success_rate": 99.1,
      "avg_response_time": 890
    }
  ],
  "platform_stats": [
    {
      "name": "淘宝",
      "search_calls": 520,
      "detail_calls": 230,
      "success_rate": 98.5,
      "error_count": 8
    }
  ]
}
```

## 🎨 邮件模板说明

### 1. 基础模板 (base.html)

所有邮件模板的基础模板，包含：
- 统一的样式设计
- 响应式布局
- 邮件头部和底部
- 状态徽章样式
- 按钮样式

### 2. 系统告警模板 (system_alert.html)

用于系统监控告警，支持：
- 告警级别显示
- 系统指标表格
- 受影响服务列表
- 操作建议
- 快速操作按钮

### 3. 爬虫状态模板 (crawler_status.html)

用于爬虫状态通知，支持：
- 爬虫账号状态表格
- Token过期信息
- 失败操作列表
- 统计信息
- 操作建议

### 4. API报告模板 (api_report.html)

用于API调用统计报告，支持：
- 总体统计信息
- API端点性能表格
- 平台调用统计
- 错误分析
- 性能指标
- 优化建议

### 5. 通用通知模板 (notification.html)

用于通用通知，支持：
- 灵活的内容结构
- 键值对数据表格
- 列表数据
- 操作按钮
- 附件列表

## 💻 代码调用示例

### Python代码示例

```python
from app.services.email_service import email_service

# 发送系统告警邮件
async def send_system_alert():
    template_data = {
        'alert_title': 'CPU使用率过高',
        'alert_message': '服务器CPU使用率达到85%',
        'alert_type': 'warning',
        'alert_details': {
            '服务器': 'web-01',
            'CPU使用率': '85%'
        },
        'recommendations': [
            '检查异常进程',
            '优化应用性能'
        ]
    }
    
    success = await email_service.send_template_email(
        to_emails=['<EMAIL>'],
        template_name='system_alert',
        subject='[系统告警] CPU使用率过高',
        template_data=template_data
    )
    
    return success

# 发送爬虫状态通知
async def send_crawler_notification():
    template_data = {
        'notification_title': '爬虫账号异常',
        'notification_message': '检测到账号登录失效',
        'status_type': 'warning',
        'crawler_accounts': [
            {
                'platform_name': '淘宝',
                'username': 'test_user',
                'status_text': '登录失败',
                'status_class': 'error'
            }
        ]
    }
    
    success = await email_service.send_template_email(
        to_emails=['<EMAIL>'],
        template_name='crawler_status',
        subject='[爬虫通知] 账号状态异常',
        template_data=template_data
    )
    
    return success
```

### 在监控服务中使用

```python
# 在账号监控服务中发送告警
from app.services.email_service import email_service

class AccountMonitorService:
    async def check_account_status(self):
        # 检查账号状态逻辑...
        
        if failed_accounts:
            # 发送告警邮件
            await self.send_account_alert(failed_accounts)
    
    async def send_account_alert(self, failed_accounts):
        template_data = {
            'notification_title': '爬虫账号状态异常',
            'notification_message': f'检测到{len(failed_accounts)}个账号异常',
            'status_type': 'danger',
            'crawler_accounts': failed_accounts
        }
        
        await email_service.send_template_email(
            to_emails=['<EMAIL>'],
            template_name='crawler_status',
            subject='[紧急告警] 爬虫账号异常',
            template_data=template_data
        )
```

## 🔍 故障排除

### 常见问题

1. **邮件发送失败**
   - 检查SMTP配置是否正确
   - 确认邮箱密码或应用专用密码
   - 检查网络连接

2. **模板渲染错误**
   - 确认模板文件存在
   - 检查模板数据格式
   - 查看错误日志

3. **邮件被拒收**
   - 检查发件人邮箱是否被列入黑名单
   - 确认邮件内容不包含敏感词
   - 设置正确的SPF/DKIM记录

### 调试方法

```python
# 测试邮件连接
success = await email_service.test_connection()
print(f"连接测试: {'成功' if success else '失败'}")

# 发送测试邮件
success = await email_service.send_email(
    to_emails=['<EMAIL>'],
    subject='测试邮件',
    content='这是一封测试邮件',
    content_type='plain'
)
```

## 📈 最佳实践

1. **邮件频率控制**: 避免频繁发送邮件，设置合理的告警间隔
2. **模板复用**: 使用通用模板减少重复代码
3. **异步发送**: 使用异步方式发送邮件，避免阻塞主流程
4. **错误处理**: 妥善处理邮件发送失败的情况
5. **日志记录**: 记录邮件发送日志便于排查问题

---

**版本**: v1.0.0  
**更新时间**: 2025-01-20
