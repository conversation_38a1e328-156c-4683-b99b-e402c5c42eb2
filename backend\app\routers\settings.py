"""
系统设置相关API路由
"""
from fastapi import APIRouter, Request, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import json
from datetime import datetime

from ..database import get_db
from ..models import SystemConfig

router = APIRouter(prefix="/api/v1/settings", tags=["settings"])

@router.get("/verification")
async def get_verification_settings(db: Session = Depends(get_db)):
    """获取验证设置"""
    try:
        # 从数据库获取验证相关设置
        settings = {}
        
        # 获取第三方打码服务设置
        third_party_config = db.query(SystemConfig).filter(
            SystemConfig.config_key == "verification_third_party"
        ).first()
        
        if third_party_config:
            settings.update(json.loads(third_party_config.config_value))
        else:
            # 默认设置
            settings = {
                "enable_third_party": False,
                "third_party_provider": "chaojiying",  # 超级鹰
                "third_party_config": {
                    "chaojiying": {
                        "username": "",
                        "password": "",
                        "soft_id": "",
                        "enabled": False
                    },
                    "ruokuai": {
                        "username": "",
                        "password": "",
                        "enabled": False
                    },
                    "yundama": {
                        "username": "",
                        "password": "",
                        "app_id": "",
                        "enabled": False
                    }
                },
                "manual_verification": {
                    "enabled": True,
                    "auto_refresh": True,
                    "refresh_interval": 10,
                    "notification_enabled": True,
                    "sound_enabled": False
                },
                "fallback_strategy": "manual",  # manual, fail, retry
                "timeout_settings": {
                    "qrcode_timeout": 300,
                    "sms_timeout": 180,
                    "slider_timeout": 120,
                    "captcha_timeout": 60
                }
            }
        
        return {
            "code": 200,
            "message": "success",
            "data": settings
        }
        
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取验证设置失败: {str(e)}",
            "data": {}
        }

@router.post("/verification")
async def update_verification_settings(request: Request, db: Session = Depends(get_db)):
    """更新验证设置"""
    try:
        body = await request.json()
        
        # 验证设置数据
        required_fields = ["enable_third_party", "manual_verification"]
        for field in required_fields:
            if field not in body:
                return {
                    "code": 400,
                    "message": f"缺少必要字段: {field}",
                    "data": None
                }
        
        # 保存到数据库
        config = db.query(SystemConfig).filter(
            SystemConfig.config_key == "verification_third_party"
        ).first()
        
        if config:
            config.config_value = json.dumps(body)
            config.updated_at = datetime.now()
        else:
            config = SystemConfig(
                config_key="verification_third_party",
                config_value=json.dumps(body),
                description="验证系统设置"
            )
            db.add(config)
        
        db.commit()
        
        return {
            "code": 200,
            "message": "验证设置已更新",
            "data": body
        }
        
    except Exception as e:
        db.rollback()
        return {
            "code": 500,
            "message": f"更新验证设置失败: {str(e)}",
            "data": None
        }

@router.get("/third-party/providers")
async def get_third_party_providers():
    """获取第三方打码服务提供商列表"""
    providers = [
        {
            "id": "chaojiying",
            "name": "超级鹰",
            "description": "老牌打码平台，支持多种验证码类型",
            "website": "https://www.chaojiying.com",
            "supported_types": ["captcha", "slider", "click", "puzzle"],
            "pricing": "按次计费，0.01-0.1元/次",
            "accuracy": "95%+",
            "speed": "3-10秒",
            "status": "active"
        },
        {
            "id": "ruokuai",
            "name": "若快打码",
            "description": "专业的验证码识别服务",
            "website": "http://www.ruokuai.com",
            "supported_types": ["captcha", "slider", "click"],
            "pricing": "按次计费，0.008-0.08元/次",
            "accuracy": "93%+",
            "speed": "5-15秒",
            "status": "active"
        },
        {
            "id": "yundama",
            "name": "云打码",
            "description": "云端验证码识别平台",
            "website": "http://www.yundama.com",
            "supported_types": ["captcha", "click"],
            "pricing": "按次计费，0.01-0.05元/次",
            "accuracy": "90%+",
            "speed": "5-20秒",
            "status": "active"
        },
        {
            "id": "2captcha",
            "name": "2captcha",
            "description": "国际知名打码服务",
            "website": "https://2captcha.com",
            "supported_types": ["captcha", "recaptcha", "hcaptcha"],
            "pricing": "$0.5-3.0/1000次",
            "accuracy": "95%+",
            "speed": "10-30秒",
            "status": "beta"
        }
    ]
    
    return {
        "code": 200,
        "message": "success",
        "data": providers
    }

@router.post("/third-party/test")
async def test_third_party_service(request: Request):
    """测试第三方打码服务"""
    try:
        body = await request.json()
        provider = body.get("provider")
        config = body.get("config", {})
        
        if not provider:
            return {
                "code": 400,
                "message": "缺少服务提供商参数",
                "data": None
            }
        
        # 模拟测试不同的服务提供商
        test_results = {
            "chaojiying": {
                "success": True,
                "message": "连接成功",
                "balance": "1000.50元",
                "response_time": 1.2
            },
            "ruokuai": {
                "success": True,
                "message": "连接成功",
                "balance": "500.30元",
                "response_time": 1.8
            },
            "yundama": {
                "success": False,
                "message": "账号密码错误",
                "balance": "0.00元",
                "response_time": 0.5
            },
            "2captcha": {
                "success": True,
                "message": "连接成功",
                "balance": "$25.60",
                "response_time": 2.1
            }
        }
        
        result = test_results.get(provider, {
            "success": False,
            "message": "不支持的服务提供商",
            "balance": "未知",
            "response_time": 0
        })
        
        return {
            "code": 200,
            "message": "测试完成",
            "data": {
                "provider": provider,
                "test_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                **result
            }
        }
        
    except Exception as e:
        return {
            "code": 500,
            "message": f"测试失败: {str(e)}",
            "data": None
        }

@router.get("/verification/stats")
async def get_verification_stats():
    """获取验证统计信息"""
    try:
        # 模拟统计数据
        stats = {
            "today": {
                "total_tasks": 45,
                "manual_tasks": 32,
                "third_party_tasks": 13,
                "success_rate": 94.2,
                "avg_time": 38.5
            },
            "this_week": {
                "total_tasks": 312,
                "manual_tasks": 198,
                "third_party_tasks": 114,
                "success_rate": 92.8,
                "avg_time": 42.1
            },
            "this_month": {
                "total_tasks": 1456,
                "manual_tasks": 892,
                "third_party_tasks": 564,
                "success_rate": 91.5,
                "avg_time": 45.3
            },
            "by_type": {
                "qrcode": {"count": 234, "success_rate": 96.2, "avg_time": 45.0},
                "sms": {"count": 189, "success_rate": 89.4, "avg_time": 78.5},
                "slider": {"count": 156, "success_rate": 87.8, "avg_time": 25.3},
                "captcha": {"count": 98, "success_rate": 94.9, "avg_time": 12.8}
            },
            "by_platform": {
                "taobao": {"count": 345, "success_rate": 94.5},
                "jd": {"count": 234, "success_rate": 91.2},
                "pdd": {"count": 167, "success_rate": 88.6},
                "1688": {"count": 89, "success_rate": 92.1}
            },
            "cost_analysis": {
                "manual_cost": 0.0,  # 人工成本
                "third_party_cost": 45.67,  # 第三方服务费用
                "total_cost": 45.67,
                "cost_per_task": 0.031
            }
        }
        
        return {
            "code": 200,
            "message": "success",
            "data": stats
        }
        
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取统计信息失败: {str(e)}",
            "data": {}
        }

@router.post("/verification/switch")
async def switch_verification_mode(request: Request, db: Session = Depends(get_db)):
    """切换验证模式"""
    try:
        body = await request.json()
        mode = body.get("mode")  # manual, third_party, auto
        
        if mode not in ["manual", "third_party", "auto"]:
            return {
                "code": 400,
                "message": "无效的验证模式",
                "data": None
            }
        
        # 获取当前设置
        config = db.query(SystemConfig).filter(
            SystemConfig.config_key == "verification_mode"
        ).first()
        
        if config:
            config.config_value = mode
            config.updated_at = datetime.now()
        else:
            config = SystemConfig(
                config_key="verification_mode",
                config_value=mode,
                description="当前验证模式"
            )
            db.add(config)
        
        db.commit()
        
        mode_names = {
            "manual": "人工验证",
            "third_party": "第三方打码",
            "auto": "智能切换"
        }
        
        return {
            "code": 200,
            "message": f"已切换到{mode_names[mode]}模式",
            "data": {
                "mode": mode,
                "mode_name": mode_names[mode],
                "switched_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        }
        
    except Exception as e:
        db.rollback()
        return {
            "code": 500,
            "message": f"切换验证模式失败: {str(e)}",
            "data": None
        }

@router.get("/verification/mode")
async def get_verification_mode(db: Session = Depends(get_db)):
    """获取当前验证模式"""
    try:
        config = db.query(SystemConfig).filter(
            SystemConfig.config_key == "verification_mode"
        ).first()
        
        mode = config.config_value if config else "manual"
        
        mode_info = {
            "manual": {
                "name": "人工验证",
                "description": "所有验证任务都由人工处理",
                "cost": "免费",
                "speed": "较慢",
                "accuracy": "最高"
            },
            "third_party": {
                "name": "第三方打码",
                "description": "优先使用第三方打码服务",
                "cost": "按次付费",
                "speed": "较快",
                "accuracy": "较高"
            },
            "auto": {
                "name": "智能切换",
                "description": "根据验证类型自动选择最优方案",
                "cost": "混合",
                "speed": "平衡",
                "accuracy": "平衡"
            }
        }
        
        return {
            "code": 200,
            "message": "success",
            "data": {
                "current_mode": mode,
                "mode_info": mode_info[mode],
                "all_modes": mode_info
            }
        }
        
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取验证模式失败: {str(e)}",
            "data": {}
        }
