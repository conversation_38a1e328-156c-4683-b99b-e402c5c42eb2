<template>
  <div class="settings-container">
    <!-- 页面标题 -->
    <div class="header-section">
      <n-card>
        <div class="header-content">
          <div class="title-section">
            <h2>验证设置</h2>
            <p>配置人工验证和第三方打码服务</p>
          </div>
          <div class="mode-switch">
            <n-tag :type="currentMode === 'manual' ? 'success' : 'default'">
              {{ getModeInfo(currentMode).name }}
            </n-tag>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 验证模式切换 -->
    <div class="mode-section">
      <n-card title="验证模式">
        <n-space vertical>
          <n-radio-group v-model:value="selectedMode" @update:value="switchMode">
            <n-space vertical>
              <n-radio value="manual">
                <div class="mode-option">
                  <div class="mode-title">🔧 人工验证</div>
                  <div class="mode-desc">所有验证任务由人工处理，准确率最高但速度较慢</div>
                </div>
              </n-radio>
              <n-radio value="third_party">
                <div class="mode-option">
                  <div class="mode-title">🤖 第三方打码</div>
                  <div class="mode-desc">优先使用第三方打码服务，速度快但需要付费</div>
                </div>
              </n-radio>
              <n-radio value="auto">
                <div class="mode-option">
                  <div class="mode-title">⚡ 智能切换</div>
                  <div class="mode-desc">根据验证类型自动选择最优方案</div>
                </div>
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-space>
      </n-card>
    </div>

    <!-- 第三方打码服务配置 -->
    <div class="third-party-section">
      <n-card title="第三方打码服务">
        <template #header-extra>
          <n-switch v-model:value="settings.enable_third_party" @update:value="updateSettings">
            <template #checked>已启用</template>
            <template #unchecked>已禁用</template>
          </n-switch>
        </template>

        <div v-if="settings.enable_third_party">
          <!-- 服务提供商选择 -->
          <div class="provider-selection">
            <h3>选择服务提供商</h3>
            <n-grid :cols="2" :x-gap="16" :y-gap="16">
              <n-grid-item v-for="provider in providers" :key="provider.id">
                <n-card 
                  :class="{ 'provider-selected': settings.third_party_provider === provider.id }"
                  @click="selectProvider(provider.id)"
                  hoverable
                >
                  <div class="provider-info">
                    <div class="provider-header">
                      <h4>{{ provider.name }}</h4>
                      <n-tag :type="provider.status === 'active' ? 'success' : 'warning'" size="small">
                        {{ provider.status === 'active' ? '可用' : '测试中' }}
                      </n-tag>
                    </div>
                    <p>{{ provider.description }}</p>
                    <div class="provider-details">
                      <div class="detail-item">
                        <span class="label">准确率:</span>
                        <span class="value">{{ provider.accuracy }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">速度:</span>
                        <span class="value">{{ provider.speed }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">价格:</span>
                        <span class="value">{{ provider.pricing }}</span>
                      </div>
                    </div>
                  </div>
                </n-card>
              </n-grid-item>
            </n-grid>
          </div>

          <!-- 服务配置 -->
          <div v-if="settings.third_party_provider" class="provider-config">
            <n-divider />
            <h3>{{ getProviderName(settings.third_party_provider) }} 配置</h3>
            
            <div v-if="settings.third_party_provider === 'chaojiying'" class="config-form">
              <n-form :model="settings.third_party_config.chaojiying" label-placement="left" label-width="100px">
                <n-form-item label="用户名">
                  <n-input v-model:value="settings.third_party_config.chaojiying.username" placeholder="请输入用户名" />
                </n-form-item>
                <n-form-item label="密码">
                  <n-input v-model:value="settings.third_party_config.chaojiying.password" type="password" placeholder="请输入密码" />
                </n-form-item>
                <n-form-item label="软件ID">
                  <n-input v-model:value="settings.third_party_config.chaojiying.soft_id" placeholder="请输入软件ID" />
                </n-form-item>
              </n-form>
            </div>

            <div v-else-if="settings.third_party_provider === 'ruokuai'" class="config-form">
              <n-form :model="settings.third_party_config.ruokuai" label-placement="left" label-width="100px">
                <n-form-item label="用户名">
                  <n-input v-model:value="settings.third_party_config.ruokuai.username" placeholder="请输入用户名" />
                </n-form-item>
                <n-form-item label="密码">
                  <n-input v-model:value="settings.third_party_config.ruokuai.password" type="password" placeholder="请输入密码" />
                </n-form-item>
              </n-form>
            </div>

            <div v-else-if="settings.third_party_provider === 'yundama'" class="config-form">
              <n-form :model="settings.third_party_config.yundama" label-placement="left" label-width="100px">
                <n-form-item label="用户名">
                  <n-input v-model:value="settings.third_party_config.yundama.username" placeholder="请输入用户名" />
                </n-form-item>
                <n-form-item label="密码">
                  <n-input v-model:value="settings.third_party_config.yundama.password" type="password" placeholder="请输入密码" />
                </n-form-item>
                <n-form-item label="应用ID">
                  <n-input v-model:value="settings.third_party_config.yundama.app_id" placeholder="请输入应用ID" />
                </n-form-item>
              </n-form>
            </div>

            <!-- 测试连接 -->
            <n-space>
              <n-button type="primary" @click="testConnection" :loading="testing">
                测试连接
              </n-button>
              <n-button @click="updateSettings">
                保存配置
              </n-button>
            </n-space>

            <!-- 测试结果 -->
            <div v-if="testResult" class="test-result">
              <n-alert 
                :type="testResult.success ? 'success' : 'error'" 
                :title="testResult.success ? '连接成功' : '连接失败'"
                style="margin-top: 16px"
              >
                <div>
                  <p>{{ testResult.message }}</p>
                  <p v-if="testResult.balance">余额: {{ testResult.balance }}</p>
                  <p v-if="testResult.response_time">响应时间: {{ testResult.response_time }}秒</p>
                </div>
              </n-alert>
            </div>
          </div>
        </div>

        <div v-else class="disabled-notice">
          <n-empty description="第三方打码服务已禁用">
            <template #extra>
              <n-button @click="settings.enable_third_party = true; updateSettings()">
                启用服务
              </n-button>
            </template>
          </n-empty>
        </div>
      </n-card>
    </div>

    <!-- 人工验证设置 -->
    <div class="manual-section">
      <n-card title="人工验证设置">
        <n-form :model="settings.manual_verification" label-placement="left" label-width="120px">
          <n-form-item label="启用人工验证">
            <n-switch v-model:value="settings.manual_verification.enabled" />
          </n-form-item>
          <n-form-item label="自动刷新">
            <n-switch v-model:value="settings.manual_verification.auto_refresh" />
          </n-form-item>
          <n-form-item label="刷新间隔">
            <n-input-number 
              v-model:value="settings.manual_verification.refresh_interval" 
              :min="5" 
              :max="60" 
              suffix="秒"
              style="width: 150px"
            />
          </n-form-item>
          <n-form-item label="消息通知">
            <n-switch v-model:value="settings.manual_verification.notification_enabled" />
          </n-form-item>
          <n-form-item label="声音提醒">
            <n-switch v-model:value="settings.manual_verification.sound_enabled" />
          </n-form-item>
        </n-form>
      </n-card>
    </div>

    <!-- 超时设置 -->
    <div class="timeout-section">
      <n-card title="超时设置">
        <n-form :model="settings.timeout_settings" label-placement="left" label-width="120px">
          <n-form-item label="扫码超时">
            <n-input-number 
              v-model:value="settings.timeout_settings.qrcode_timeout" 
              :min="60" 
              :max="600" 
              suffix="秒"
              style="width: 150px"
            />
          </n-form-item>
          <n-form-item label="短信超时">
            <n-input-number 
              v-model:value="settings.timeout_settings.sms_timeout" 
              :min="60" 
              :max="300" 
              suffix="秒"
              style="width: 150px"
            />
          </n-form-item>
          <n-form-item label="滑块超时">
            <n-input-number 
              v-model:value="settings.timeout_settings.slider_timeout" 
              :min="30" 
              :max="180" 
              suffix="秒"
              style="width: 150px"
            />
          </n-form-item>
          <n-form-item label="验证码超时">
            <n-input-number 
              v-model:value="settings.timeout_settings.captcha_timeout" 
              :min="30" 
              :max="120" 
              suffix="秒"
              style="width: 150px"
            />
          </n-form-item>
        </n-form>
      </n-card>
    </div>

    <!-- 保存按钮 -->
    <div class="save-section">
      <n-space justify="center">
        <n-button type="primary" size="large" @click="saveAllSettings" :loading="saving">
          保存所有设置
        </n-button>
        <n-button @click="resetSettings">
          重置设置
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import {
  NCard, NSpace, NTag, NRadioGroup, NRadio, NSwitch, NGrid, NGridItem,
  NDivider, NForm, NFormItem, NInput, NInputNumber, NButton, NAlert,
  NEmpty, useMessage
} from 'naive-ui'
import axios from 'axios'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const testing = ref(false)
const currentMode = ref('manual')
const selectedMode = ref('manual')
const testResult = ref(null)
const providers = ref([])

// 设置数据
const settings = reactive({
  enable_third_party: false,
  third_party_provider: '',
  third_party_config: {
    chaojiying: {
      username: '',
      password: '',
      soft_id: '',
      enabled: false
    },
    ruokuai: {
      username: '',
      password: '',
      enabled: false
    },
    yundama: {
      username: '',
      password: '',
      app_id: '',
      enabled: false
    }
  },
  manual_verification: {
    enabled: true,
    auto_refresh: true,
    refresh_interval: 10,
    notification_enabled: true,
    sound_enabled: false
  },
  fallback_strategy: 'manual',
  timeout_settings: {
    qrcode_timeout: 300,
    sms_timeout: 180,
    slider_timeout: 120,
    captcha_timeout: 60
  }
})

// 消息提示
const message = useMessage()

// API基础URL
const API_BASE = 'http://localhost:8000/api/v1'

// 方法定义
const loadSettings = async () => {
  loading.value = true
  try {
    const response = await axios.get(`${API_BASE}/settings/verification`)
    if (response.code === 200) {
      Object.assign(settings, response.data)
    }
  } catch (error) {
    message.error('加载设置失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadProviders = async () => {
  try {
    const response = await axios.get(`${API_BASE}/settings/third-party/providers`)
    if (response.code === 200) {
      providers.value = response.data
    }
  } catch (error) {
    message.error('加载服务提供商失败: ' + error.message)
  }
}

const loadCurrentMode = async () => {
  try {
    const response = await axios.get(`${API_BASE}/settings/verification/mode`)
    if (response.code === 200) {
      currentMode.value = response.data.current_mode
      selectedMode.value = response.data.current_mode
    }
  } catch (error) {
    console.error('加载当前模式失败:', error)
  }
}

const switchMode = async (mode) => {
  try {
    const response = await axios.post(`${API_BASE}/settings/verification/switch`, {
      mode: mode
    })
    if (response.code === 200) {
      currentMode.value = mode
      message.success(response.message)
    } else {
      message.error(response.message)
      selectedMode.value = currentMode.value // 回退
    }
  } catch (error) {
    message.error('切换模式失败: ' + error.message)
    selectedMode.value = currentMode.value // 回退
  }
}

const selectProvider = (providerId) => {
  settings.third_party_provider = providerId
  testResult.value = null
}

const getProviderName = (providerId) => {
  const provider = providers.value.find(p => p.id === providerId)
  return provider ? provider.name : providerId
}

const getModeInfo = (mode) => {
  const modes = {
    manual: { name: '人工验证', color: 'success' },
    third_party: { name: '第三方打码', color: 'info' },
    auto: { name: '智能切换', color: 'warning' }
  }
  return modes[mode] || { name: '未知', color: 'default' }
}

const testConnection = async () => {
  if (!settings.third_party_provider) {
    message.warning('请先选择服务提供商')
    return
  }

  testing.value = true
  testResult.value = null

  try {
    const config = settings.third_party_config[settings.third_party_provider]
    const response = await axios.post(`${API_BASE}/settings/third-party/test`, {
      provider: settings.third_party_provider,
      config: config
    })

    if (response.code === 200) {
      testResult.value = response.data
      if (testResult.value.success) {
        message.success('连接测试成功')
      } else {
        message.error('连接测试失败')
      }
    } else {
      message.error(response.message)
    }
  } catch (error) {
    message.error('测试连接失败: ' + error.message)
  } finally {
    testing.value = false
  }
}

const updateSettings = async () => {
  try {
    const response = await axios.post(`${API_BASE}/settings/verification`, settings)
    if (response.code === 200) {
      message.success('设置已更新')
    } else {
      message.error(response.message)
    }
  } catch (error) {
    message.error('更新设置失败: ' + error.message)
  }
}

const saveAllSettings = async () => {
  saving.value = true
  try {
    await updateSettings()
    message.success('所有设置已保存')
  } catch (error) {
    message.error('保存设置失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  // 重置为默认设置
  Object.assign(settings, {
    enable_third_party: false,
    third_party_provider: '',
    third_party_config: {
      chaojiying: { username: '', password: '', soft_id: '', enabled: false },
      ruokuai: { username: '', password: '', enabled: false },
      yundama: { username: '', password: '', app_id: '', enabled: false }
    },
    manual_verification: {
      enabled: true,
      auto_refresh: true,
      refresh_interval: 10,
      notification_enabled: true,
      sound_enabled: false
    },
    fallback_strategy: 'manual',
    timeout_settings: {
      qrcode_timeout: 300,
      sms_timeout: 180,
      slider_timeout: 120,
      captcha_timeout: 60
    }
  })
  testResult.value = null
  message.info('设置已重置')
}

// 生命周期
onMounted(() => {
  loadSettings()
  loadProviders()
  loadCurrentMode()
})
</script>

<style scoped>
.settings-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.title-section p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.mode-section,
.third-party-section,
.manual-section,
.timeout-section {
  margin-bottom: 20px;
}

.mode-option {
  margin-left: 8px;
}

.mode-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.mode-desc {
  font-size: 12px;
  color: #666;
}

.provider-selection {
  margin-bottom: 20px;
}

.provider-info {
  cursor: pointer;
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.provider-header h4 {
  margin: 0;
  font-size: 16px;
}

.provider-details {
  margin-top: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.label {
  color: #666;
}

.value {
  font-weight: 500;
}

.provider-selected {
  border: 2px solid #18a058;
  background-color: #f0f9ff;
}

.config-form {
  margin: 16px 0;
}

.test-result {
  margin-top: 16px;
}

.disabled-notice {
  text-align: center;
  padding: 40px 20px;
}

.save-section {
  margin-top: 40px;
  padding: 20px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-container {
    padding: 10px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .provider-selection .n-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
.provider-info {
  transition: all 0.3s ease;
}

.provider-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 表单样式 */
.n-form-item {
  margin-bottom: 16px;
}

/* 卡片间距 */
.n-card {
  margin-bottom: 16px;
}

/* 按钮样式 */
.n-button {
  margin-right: 8px;
}

.n-button:last-child {
  margin-right: 0;
}
</style>
