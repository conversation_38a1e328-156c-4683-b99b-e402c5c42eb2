#!/usr/bin/env python3
"""
开发环境启动脚本
"""
import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "frontend"

class DevServer:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查开发环境依赖...")
        
        # 检查Python依赖
        requirements_file = BACKEND_DIR / "requirements.txt"
        if requirements_file.exists():
            try:
                result = subprocess.run([
                    sys.executable, "-m", "pip", "check"
                ], capture_output=True, text=True, cwd=BACKEND_DIR)
                if result.returncode != 0:
                    print("⚠️  Python依赖可能有问题，建议运行: pip install -r requirements.txt")
            except Exception as e:
                print(f"⚠️  检查Python依赖失败: {e}")
        
        # 检查Node.js依赖
        node_modules = FRONTEND_DIR / "node_modules"
        if not node_modules.exists():
            print("⚠️  前端依赖未安装，建议运行: npm install")
        
        print("✅ 依赖检查完成")
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env.update({
                "PYTHONPATH": str(BACKEND_DIR),
                "ENVIRONMENT": "development"
            })
            
            # 启动uvicorn服务器
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "app.main:app", 
                "--reload", 
                "--host", "0.0.0.0", 
                "--port", "8000",
                "--log-level", "info"
            ], cwd=BACKEND_DIR, env=env)
            
            print("✅ 后端服务启动成功 (http://localhost:8000)")
            return True
            
        except Exception as e:
            print(f"❌ 后端服务启动失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("🚀 启动前端服务...")
        
        try:
            # 检查package.json
            package_json = FRONTEND_DIR / "package.json"
            if not package_json.exists():
                print("❌ 未找到package.json文件")
                return False
            
            # 启动Vite开发服务器
            # Windows系统使用npm.cmd
            npm_cmd = "npm.cmd" if os.name == 'nt' else "npm"
            self.frontend_process = subprocess.Popen([
                npm_cmd, "run", "dev"
            ], cwd=FRONTEND_DIR)
            
            print("✅ 前端服务启动成功 (http://localhost:3000)")
            return True
            
        except Exception as e:
            print(f"❌ 前端服务启动失败: {e}")
            return False
    
    def wait_for_backend(self, timeout=30):
        """等待后端服务启动"""
        import requests
        
        print("⏳ 等待后端服务启动...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get("http://localhost:8000/health", timeout=2)
                if response.status_code == 200:
                    print("✅ 后端服务已就绪")
                    return True
            except:
                pass
            time.sleep(1)
        
        print("⚠️  后端服务启动超时")
        return False
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n🛑 收到停止信号，正在关闭服务...")
        self.running = False
        self.stop_services()
        sys.exit(0)
    
    def stop_services(self):
        """停止所有服务"""
        if self.backend_process:
            print("🛑 停止后端服务...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
        
        if self.frontend_process:
            print("🛑 停止前端服务...")
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
    
    def monitor_services(self):
        """监控服务状态"""
        backend_restart_count = 0
        frontend_restart_count = 0
        max_restarts = 3

        while self.running:
            time.sleep(5)

            # 检查后端进程
            if self.backend_process and self.backend_process.poll() is not None:
                if backend_restart_count < max_restarts:
                    print(f"❌ 后端服务异常退出，尝试重启 ({backend_restart_count + 1}/{max_restarts})")
                    if self.start_backend():
                        backend_restart_count += 1
                        print("✅ 后端服务重启成功")
                    else:
                        print("❌ 后端服务重启失败")
                        backend_restart_count += 1
                else:
                    print("❌ 后端服务重启次数过多，停止监控")
                    self.running = False
                    break

            # 检查前端进程
            if self.frontend_process and self.frontend_process.poll() is not None:
                if frontend_restart_count < max_restarts:
                    print(f"❌ 前端服务异常退出，尝试重启 ({frontend_restart_count + 1}/{max_restarts})")
                    if self.start_frontend():
                        frontend_restart_count += 1
                        print("✅ 前端服务重启成功")
                    else:
                        print("❌ 前端服务重启失败")
                        frontend_restart_count += 1
                else:
                    print("❌ 前端服务重启次数过多，停止监控")
                    # 前端服务失败不停止整个系统，只是记录日志
                    print("⚠️ 前端服务已停止，但后端服务继续运行")
    
    def run(self):
        """运行开发服务器"""
        print("🎯 AqentCrawler 开发环境启动")
        print("=" * 50)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 检查依赖
            self.check_dependencies()
            
            # 启动后端
            if not self.start_backend():
                return False
            
            # 等待后端启动
            if not self.wait_for_backend():
                self.stop_services()
                return False
            
            # 启动前端
            if not self.start_frontend():
                self.stop_services()
                return False
            
            print("\n🎉 开发环境启动完成!")
            print("📋 服务地址:")
            print("   后端API: http://localhost:8000")
            print("   前端界面: http://localhost:3000")
            print("   API文档: http://localhost:8000/docs")
            print("\n💡 使用说明:")
            print("   - 默认管理员账号: admin / admin123")
            print("   - 按 Ctrl+C 停止所有服务")
            print("   - 代码修改会自动重载")
            print("\n⏳ 服务运行中...")
            
            # 监控服务
            monitor_thread = threading.Thread(target=self.monitor_services)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 保持主线程运行
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 用户中断，正在停止服务...")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
        finally:
            self.stop_services()
            print("✅ 所有服务已停止")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("AqentCrawler 开发环境启动脚本")
        print("\n用法:")
        print("  python scripts/start_dev.py")
        print("\n功能:")
        print("  - 自动启动后端API服务 (端口8000)")
        print("  - 自动启动前端开发服务器 (端口3000)")
        print("  - 监控服务状态")
        print("  - 支持热重载")
        return
    
    server = DevServer()
    server.run()

if __name__ == "__main__":
    main()
