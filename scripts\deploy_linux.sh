#!/bin/bash
# AqentCrawler Linux 自动部署脚本
# 适用于 CentOS 7

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -eq 0 ]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_step "检查系统版本..."
    
    if [ ! -f /etc/redhat-release ]; then
        log_error "此脚本仅支持 CentOS 7"
        exit 1
    fi
    
    version=$(cat /etc/redhat-release | grep -oE '[0-9]+\.[0-9]+' | head -1)
    major_version=$(echo $version | cut -d. -f1)
    
    if [ "$major_version" != "7" ]; then
        log_error "此脚本仅支持 CentOS 7，当前版本: $version"
        exit 1
    fi
    
    log_info "系统版本检查通过: CentOS $version"
}

# 检查必需的软件
check_dependencies() {
    log_step "检查依赖软件..."
    
    dependencies=("python3" "pip3" "node" "npm" "mysql" "redis-cli" "nginx")
    missing_deps=()
    
    for dep in "${dependencies[@]}"; do
        if ! command -v $dep &> /dev/null; then
            missing_deps+=($dep)
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少以下依赖软件: ${missing_deps[*]}"
        log_info "请先运行环境准备脚本安装依赖"
        exit 1
    fi
    
    log_info "依赖软件检查通过"
}

# 设置变量
setup_variables() {
    APP_USER="aqentcrawler"
    APP_DIR="/home/<USER>/aqentcrawler"
    SERVICE_NAME="aqentcrawler"
    BACKUP_DIR="/home/<USER>/backup"
    LOG_DIR="/home/<USER>/logs"
    
    # 获取当前用户
    CURRENT_USER=$(whoami)
    
    log_info "部署配置:"
    log_info "  应用用户: $APP_USER"
    log_info "  应用目录: $APP_DIR"
    log_info "  当前用户: $CURRENT_USER"
}

# 创建应用目录
create_directories() {
    log_step "创建应用目录..."
    
    directories=("$APP_DIR" "$BACKUP_DIR" "$LOG_DIR" "/home/<USER>/chrome_data")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    # 设置权限
    chmod 755 "$APP_DIR"
    chmod 755 "$LOG_DIR"
    chmod 755 "$BACKUP_DIR"
}

# 备份现有部署
backup_existing() {
    if [ -d "$APP_DIR/backend" ]; then
        log_step "备份现有部署..."
        
        backup_name="backup_$(date +%Y%m%d_%H%M%S)"
        backup_path="$BACKUP_DIR/$backup_name"
        
        mkdir -p "$backup_path"
        cp -r "$APP_DIR"/* "$backup_path/" 2>/dev/null || true
        
        log_info "备份完成: $backup_path"
    fi
}

# 部署应用文件
deploy_files() {
    log_step "部署应用文件..."
    
    # 检查当前目录是否包含应用文件
    if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
        log_error "当前目录不包含应用文件，请在解压后的应用目录中运行此脚本"
        exit 1
    fi
    
    # 复制文件
    log_info "复制后端文件..."
    cp -r backend "$APP_DIR/"
    
    log_info "复制前端文件..."
    cp -r frontend "$APP_DIR/"
    
    log_info "复制脚本文件..."
    cp -r scripts "$APP_DIR/"
    
    if [ -d "chrome-extension" ]; then
        log_info "复制Chrome扩展..."
        cp -r chrome-extension "$APP_DIR/"
    fi
    
    # 复制文档
    for doc in *.md; do
        if [ -f "$doc" ]; then
            cp "$doc" "$APP_DIR/"
        fi
    done
    
    log_info "文件部署完成"
}

# 配置环境
setup_environment() {
    log_step "配置环境..."
    
    cd "$APP_DIR/backend"
    
    # 检查是否已有配置文件
    if [ ! -f ".env" ]; then
        if [ -f "../.env.production" ]; then
            cp "../.env.production" ".env"
            log_info "使用生产环境配置模板"
        else
            log_warn "未找到环境配置文件，请手动创建 .env 文件"
        fi
    else
        log_info "环境配置文件已存在"
    fi
    
    # 设置脚本权限
    chmod +x "$APP_DIR/scripts"/*.py 2>/dev/null || true
    chmod +x "$APP_DIR/scripts"/*.sh 2>/dev/null || true
}

# 安装Python依赖
install_python_deps() {
    log_step "安装Python依赖..."
    
    cd "$APP_DIR/backend"
    
    # 检查requirements.txt
    if [ ! -f "requirements.txt" ]; then
        log_error "未找到 requirements.txt 文件"
        exit 1
    fi
    
    # 安装依赖
    log_info "安装Python包..."
    pip3 install --user -r requirements.txt
    
    log_info "Python依赖安装完成"
}

# 配置数据库
setup_database() {
    log_step "配置数据库..."
    
    # 检查MySQL服务
    if ! systemctl is-active --quiet mysqld; then
        log_warn "MySQL服务未运行，请先启动MySQL服务"
        return 1
    fi
    
    # 提示用户配置数据库
    log_info "请手动配置数据库:"
    log_info "1. 登录MySQL: mysql -u root -p"
    log_info "2. 创建数据库: CREATE DATABASE aqentcrawler CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    log_info "3. 创建用户: CREATE USER 'aqentcrawler'@'localhost' IDENTIFIED BY 'your_password';"
    log_info "4. 授权: GRANT ALL PRIVILEGES ON aqentcrawler.* TO 'aqentcrawler'@'localhost';"
    log_info "5. 刷新权限: FLUSH PRIVILEGES;"
    
    read -p "数据库配置完成后按回车继续..."
}

# 初始化数据库
init_database() {
    log_step "初始化数据库..."
    
    cd "$APP_DIR"
    
    if [ -f "scripts/init_db.py" ]; then
        python3 scripts/init_db.py
        log_info "数据库初始化完成"
    else
        log_warn "未找到数据库初始化脚本"
    fi
}

# 配置Nginx
setup_nginx() {
    log_step "配置Nginx..."
    
    nginx_conf="/etc/nginx/conf.d/aqentcrawler.conf"
    
    # 检查是否已有配置
    if [ -f "$nginx_conf" ]; then
        log_info "Nginx配置已存在，跳过配置"
        return 0
    fi
    
    # 创建Nginx配置
    sudo tee "$nginx_conf" > /dev/null <<EOF
server {
    listen 80;
    server_name _;
    
    # 前端静态文件
    location / {
        root $APP_DIR/frontend;
        try_files \$uri \$uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # API文档
    location /docs {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8000;
        access_log off;
    }
}
EOF
    
    # 测试Nginx配置
    if sudo nginx -t; then
        sudo systemctl reload nginx
        log_info "Nginx配置完成"
    else
        log_error "Nginx配置测试失败"
        return 1
    fi
}

# 创建systemd服务
setup_systemd() {
    log_step "配置systemd服务..."
    
    service_file="/etc/systemd/system/$SERVICE_NAME.service"
    
    # 创建服务文件
    sudo tee "$service_file" > /dev/null <<EOF
[Unit]
Description=AqentCrawler API Service
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=exec
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$APP_DIR/backend
Environment=PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/usr/bin/python3 -m gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 127.0.0.1:8000
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF
    
    # 重载systemd配置
    sudo systemctl daemon-reload
    sudo systemctl enable "$SERVICE_NAME"
    
    log_info "systemd服务配置完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    # 启动应用服务
    if sudo systemctl start "$SERVICE_NAME"; then
        log_info "应用服务启动成功"
    else
        log_error "应用服务启动失败"
        return 1
    fi
    
    # 检查服务状态
    sleep 5
    if sudo systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "服务运行正常"
    else
        log_error "服务运行异常"
        sudo systemctl status "$SERVICE_NAME"
        return 1
    fi
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    # 检查API健康状态
    if curl -s http://localhost:8000/health > /dev/null; then
        log_info "API服务正常"
    else
        log_warn "API服务可能未正常启动"
    fi
    
    # 检查前端访问
    if curl -s http://localhost/ > /dev/null; then
        log_info "前端服务正常"
    else
        log_warn "前端服务可能未正常启动"
    fi
    
    log_info "部署验证完成"
}

# 显示部署结果
show_result() {
    log_step "部署完成！"
    
    echo
    log_info "🎉 AqentCrawler 部署成功！"
    echo
    log_info "📝 重要信息:"
    log_info "  应用目录: $APP_DIR"
    log_info "  配置文件: $APP_DIR/backend/.env"
    log_info "  日志目录: $LOG_DIR"
    log_info "  备份目录: $BACKUP_DIR"
    echo
    log_info "🌐 访问地址:"
    log_info "  前端界面: http://$(hostname -I | awk '{print $1}')/"
    log_info "  API文档: http://$(hostname -I | awk '{print $1}')/docs"
    log_info "  健康检查: http://$(hostname -I | awk '{print $1}')/health"
    echo
    log_info "🔧 服务管理:"
    log_info "  启动服务: sudo systemctl start $SERVICE_NAME"
    log_info "  停止服务: sudo systemctl stop $SERVICE_NAME"
    log_info "  重启服务: sudo systemctl restart $SERVICE_NAME"
    log_info "  查看状态: sudo systemctl status $SERVICE_NAME"
    log_info "  查看日志: sudo journalctl -u $SERVICE_NAME -f"
    echo
    log_warn "⚠️  下一步操作:"
    log_warn "1. 编辑配置文件: $APP_DIR/backend/.env"
    log_warn "2. 修改数据库密码和安全密钥"
    log_warn "3. 重启服务: sudo systemctl restart $SERVICE_NAME"
}

# 主函数
main() {
    log_info "🚀 开始部署 AqentCrawler..."
    
    check_root
    check_system
    check_dependencies
    setup_variables
    
    create_directories
    backup_existing
    deploy_files
    setup_environment
    install_python_deps
    setup_database
    init_database
    setup_nginx
    setup_systemd
    start_services
    verify_deployment
    show_result
    
    log_info "✅ 部署流程完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
