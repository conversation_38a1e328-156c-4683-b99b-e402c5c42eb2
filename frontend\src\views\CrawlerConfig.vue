<template>
  <div class="crawler-config-page">
    <div class="page-header">
      <h2>爬虫配置管理</h2>
      <div class="header-actions">
        <div class="auto-refresh-toggle">
          <label class="toggle-label">
            <input
              type="checkbox"
              v-model="autoRefreshEnabled"
              @change="toggleAutoRefresh"
            >
            <span class="toggle-slider"></span>
            <span class="toggle-text">自动刷新Token</span>
          </label>
          <button
            v-if="autoRefreshEnabled"
            @click="openAutoRefreshConfig"
            class="btn-config"
            title="配置自动刷新"
          >
            ⚙️
          </button>
        </div>
        <button @click="openAddModal" class="btn-primary">➕ 添加配置</button>
        <button @click="refreshData" class="btn-secondary">🔄 刷新</button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-number">{{ stats.total_accounts || 0 }}</div>
        <div class="stat-label">总账号数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.active_accounts || 0 }}</div>
        <div class="stat-label">活跃账号</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ stats.total_requests || 0 }}</div>
        <div class="stat-label">总请求数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ (stats.avg_success_rate || 0).toFixed(1) }}%</div>
        <div class="stat-label">平均成功率</div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-item">
        <label>平台:</label>
        <select v-model="filters.platform" @change="loadAccounts">
          <option value="">全部平台</option>
          <option value="taobao">淘宝</option>
          <option value="jingdong">京东</option>
          <option value="pinduoduo">拼多多</option>
          <option value="1688">1688</option>
        </select>
      </div>
      <div class="filter-item">
        <label>状态:</label>
        <select v-model="filters.status" @change="loadAccounts">
          <option value="">全部状态</option>
          <option value="active">活跃</option>
          <option value="inactive">非活跃</option>
          <option value="expired">已过期</option>
          <option value="error">错误</option>
        </select>
      </div>
    </div>

    <!-- 账号列表 -->
    <div class="accounts-table">
      <div class="table-header">
        <div class="col col-id">ID</div>
        <div class="col col-platform">平台</div>
        <div class="col col-username">用户名</div>
        <div class="col col-status">状态</div>
        <div class="col col-token-time">Token剩余</div>
        <div class="col col-success-rate">成功率</div>
        <div class="col col-requests">总请求</div>
        <div class="col col-errors">错误次数</div>
        <!-- <div class="col col-last-used">最后使用</div> -->
        <div class="col col-actions">操作</div>
      </div>

      <div v-if="loading" class="loading">加载中...</div>

      <div v-else-if="accounts.length === 0" class="empty">
        暂无配置数据
      </div>

      <div v-else>
        <div v-for="account in accounts" :key="account.id" class="table-row">
          <div class="col col-id">{{ account.id }}</div>
          <div class="col col-platform">
            <span class="platform-tag" :class="'platform-' + account.platform?.code">
              {{ account.platform?.name || '未知' }}
            </span>
          </div>
          <div class="col col-username">
            <div class="username-info">
              <div class="username">{{ account.username }}</div>
              <div class="login-status" :class="account.login_status || 'not_logged_in'">
                {{ getLoginStatusName(account.login_status) }}
              </div>
            </div>
          </div>
          <div class="col col-status">
            <div class="status-container">
              <div class="status-info">
                <span class="status-tag" :class="account.status">
                  {{ getStatusName(account.status) }}
                </span>
                <span class="pool-status-tag" :class="account.pool_status || 'unknown'">
                  {{ getPoolStatusName(account.pool_status) }}
                </span>
              </div>
              <div class="toggle-switch" @click="toggleAccount(account)">
                <input
                  type="checkbox"
                  :checked="account.is_enabled"
                  :id="'toggle-' + account.id"
                  @click.stop
                >
                <label :for="'toggle-' + account.id" class="switch-label">
                  <span class="switch-slider"></span>
                </label>
              </div>
            </div>
          </div>
          <div class="col col-token-time">
            <div class="token-info">
              <div class="token-remaining" :class="getTokenStatusClass(account.token_status)">
                {{ account.token_remaining_time || '无Token' }}
              </div>
              <div class="last-login" v-if="account.last_login_at">
                最后登录: {{ formatDate(account.last_login_at) }}
              </div>
              <div class="last-login" v-if="account.last_used_at">
                最后使用: {{ formatDate(account.last_used_at) }}
              </div>
            </div>
          </div>
          <div class="col col-success-rate">{{ (account.success_rate || 0).toFixed(1) }}%</div>
          <div class="col col-requests">{{ account.total_requests || 0 }}</div>
          <div class="col col-errors">
            <span :class="{ 'error-count': account.error_count > 0 }">
              {{ account.error_count || 0 }}
            </span>
          </div>
          <!-- <div class="col col-last-used">{{ formatDate(account.last_used_at) }}</div> -->
          <div class="col col-actions">
            <div class="actions-container">
              <button @click="loginAccount(account)" class="btn-action btn-login" :disabled="logging === account.id" title="登录账号">
                🔐登录
              </button>
              <button @click="refreshToken(account)" class="btn-action btn-refresh" :disabled="logging === account.id" title="刷新Token">
                🔄刷新
              </button>
              <button @click="viewAccount(account)" class="btn-action btn-view" title="查看详情">
                👁️查看
              </button>
              <button @click="editAccount(account)" class="btn-action btn-edit" title="编辑配置">
                ✏️编辑
              </button>
              <button @click="deleteAccount(account)" class="btn-action btn-delete" title="删除账号">
                🗑️删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 登录模态框 -->
    <div v-if="showLoginModal" class="modal-overlay" @click="closeLoginModal">
      <div class="modal login-modal" @click.stop>
        <div class="modal-header">
          <h3>账号登录</h3>
          <button @click="closeLoginModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <!-- 左侧：登录配置 -->
          <div class="login-config">
            <div class="login-tip">
              <div class="tip-icon">💡</div>
              <div class="tip-content">
                <p><strong>简化登录流程：</strong></p>
                <ol>
                  <li>点击"开始登录"按钮</li>
                  <li>系统自动打开浏览器并访问登录页面</li>
                  <li>在浏览器中手动输入账号密码</li>
                  <li>完成验证码、滑块等验证</li>
                  <li>系统自动提取Cookie和Token</li>
                </ol>
                <p class="warning">⚠️ 请确保在浏览器中使用正确的账号登录</p>
              </div>
            </div>

            <form @submit.prevent="executeLogin">
              <div class="account-info">
                <div class="info-item">
                  <span class="label">用户名：</span>
                  <span class="value">{{ loginForm.username }}</span>
                </div>
                <div class="info-item">
                  <span class="label">平台：</span>
                  <span class="value">{{ getPlatformName(loginForm.platform_id) }}</span>
                </div>
              </div>

              <div class="form-group checkbox-group">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="loginForm.force_relogin">
                  <span class="checkmark"></span>
                  强制重新登录（即使Token有效）
                </label>
              </div>

              <div class="form-actions">
                <button type="button" @click="closeLoginModal" class="btn-secondary">取消</button>
                <button type="submit" class="btn-primary" :disabled="logging">
                  {{ logging ? '登录中...' : '开始登录' }}
                </button>
              </div>
            </form>
          </div>

          <!-- 右侧：日志区域 -->
          <div class="login-logs">
            <div class="logs-header">
              <h4>登录日志</h4>
              <button type="button" @click="clearLogs" class="btn-clear">清空</button>
            </div>
            <div class="logs-content" ref="logsContainer">
              <div v-if="loginLogs.length === 0" class="logs-empty">
                等待登录操作...
              </div>
              <div v-for="(log, index) in loginLogs" :key="index" class="log-item" :class="log.type">
                <span class="log-time">{{ formatLogTime(log.time) }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Token刷新模态框 -->
    <div v-if="showRefreshModal" class="modal-overlay" @click="closeRefreshModal">
      <div class="modal refresh-modal" @click.stop>
        <div class="modal-header">
          <h3>Token获取</h3>
          <button @click="closeRefreshModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <!-- 左侧：刷新配置 -->
          <div class="refresh-config">
            <div class="refresh-tip">
              <div class="tip-icon">🔄</div>
              <div class="tip-content">
                <p><strong>Token获取流程：</strong></p>
                <ol>
                  <li>点击"开始获取"按钮</li>
                  <li>系统自动打开浏览器并访问平台主页</li>
                  <li>使用已有的登录环境或手动登录</li>
                  <li>系统每2秒检测Token状态</li>
                  <li>自动提取最新的Cookie和Token</li>
                </ol>
                <p class="warning">⚠️ 如果未登录，请在浏览器中完成登录</p>
              </div>
            </div>

            <form @submit.prevent="executeRefreshToken">
              <div class="account-info">
                <div class="info-item">
                  <span class="label">用户名：</span>
                  <span class="value">{{ refreshForm.username }}</span>
                </div>
                <div class="info-item">
                  <span class="label">平台：</span>
                  <span class="value">{{ getPlatformName(refreshForm.platform_id) }}</span>
                </div>
              </div>

              <div class="form-actions">
                <button type="button" @click="closeRefreshModal" class="btn-secondary">取消</button>
                <button type="submit" class="btn-primary" :disabled="logging">
                  {{ logging ? '获取中...' : '开始获取' }}
                </button>
              </div>
            </form>
          </div>

          <!-- 右侧：日志区域 -->
          <div class="refresh-logs">
            <div class="logs-header">
              <h4>Token获取日志</h4>
              <button type="button" @click="clearRefreshLogs" class="btn-clear">清空</button>
            </div>
            <div class="logs-content" ref="refreshLogsContainer">
              <div v-if="refreshLogs.length === 0" class="logs-empty">
                等待Token获取操作...
              </div>
              <div v-for="(log, index) in refreshLogs" :key="index" class="log-item" :class="log.type">
                <span class="log-time">{{ formatLogTime(log.time) }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加配置模态框 -->
    <div v-if="showAddModal" class="modal-overlay" @click="closeAddModal">
      <div class="modal add-modal" @click.stop>
        <div class="modal-header">
          <h3>添加爬虫配置</h3>
          <button @click="closeAddModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitAddForm">
            <div class="form-group">
              <label>平台 *</label>
              <select v-model="addForm.platform_id" required>
                <option value="">请选择平台</option>
                <option v-for="platform in platforms" :key="platform.id" :value="platform.id">
                  {{ platform.name }}
                </option>
              </select>
            </div>

            <div class="form-group">
              <label>用户名 *</label>
              <input
                type="text"
                v-model="addForm.username"
                placeholder="请输入用户名"
                required
              >
            </div>

            <div class="form-group">
              <label>密码</label>
              <input
                type="password"
                v-model="addForm.password"
                placeholder="请输入密码（可选，用于自动登录）"
              >
              <small class="form-hint">密码将加密存储，用于自动登录时填入</small>
            </div>

            <div class="form-group">
              <label>代理配置</label>
              <select v-model="addForm.proxy_id">
                <option value="">不使用代理</option>
                <option v-for="proxy in proxies" :key="proxy.id" :value="proxy.id">
                  {{ proxy.name }} ({{ proxy.host }}:{{ proxy.port }})
                </option>
              </select>
            </div>

            <div class="form-group">
              <label>备注</label>
              <textarea
                v-model="addForm.notes"
                placeholder="可选的备注信息"
                rows="3"
              ></textarea>
            </div>

            <div class="form-actions">
              <button type="button" @click="closeAddModal" class="btn-secondary">取消</button>
              <button type="submit" class="btn-primary" :disabled="submitting">
                {{ submitting ? '添加中...' : '确认添加' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 编辑配置模态框 -->
    <div v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
      <div class="modal edit-modal" @click.stop>
        <div class="modal-header">
          <h3>编辑爬虫配置</h3>
          <button @click="closeEditModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitEditForm">
            <!-- 基本信息行 -->
            <div class="form-row">
              <div class="form-group">
                <label>平台 *</label>
                <select v-model="editForm.platform_id" required>
                  <option value="">请选择平台</option>
                  <option v-for="platform in platforms" :key="platform.id" :value="platform.id">
                    {{ platform.name }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label>用户名 *</label>
                <input
                  type="text"
                  v-model="editForm.username"
                  placeholder="请输入用户名"
                  required
                >
              </div>
            </div>

            <!-- 认证信息行 -->
            <div class="form-row">
              <div class="form-group">
                <label>密码</label>
                <input
                  type="password"
                  v-model="editForm.password"
                  placeholder="请输入密码（可选）"
                >
                <small class="form-hint">密码将加密存储，用于自动登录</small>
              </div>
              <div class="form-group">
                <label>代理配置</label>
                <select v-model="editForm.proxy_id">
                  <option value="">不使用代理</option>
                  <option v-for="proxy in proxies" :key="proxy.id" :value="proxy.id">
                    {{ proxy.provider_name }} ({{ proxy.host }}:{{ proxy.port }})
                  </option>
                </select>
              </div>
            </div>

            <!-- 状态配置行 -->
            <div class="form-row">
              <div class="form-group">
                <label>状态</label>
                <select v-model="editForm.status">
                  <option value="active">活跃</option>
                  <option value="inactive">非活跃</option>
                  <option value="expired">已过期</option>
                  <option value="error">错误</option>
                </select>
              </div>
              <div class="form-group">
                <label>优先级</label>
                <input
                  type="number"
                  v-model.number="editForm.priority"
                  min="1"
                  max="10"
                  placeholder="1-10"
                >
              </div>
            </div>

            <!-- 请求限制 -->
            <div class="form-group">
              <label>每小时最大请求数</label>
              <input
                type="number"
                v-model.number="editForm.max_requests_per_hour"
                min="1"
                max="10000"
                placeholder="每小时最大请求数"
              >
            </div>

            <!-- 备注 -->
            <div class="form-group">
              <label>备注</label>
              <textarea
                v-model="editForm.notes"
                placeholder="可选的备注信息"
                rows="2"
              ></textarea>
            </div>

            <div class="form-actions">
              <button type="button" @click="closeEditModal" class="btn-secondary">取消</button>
              <button type="submit" class="btn-primary" :disabled="submitting">
                {{ submitting ? '更新中...' : '确认更新' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 查看配置模态框 -->
    <div v-if="showViewModal" class="modal-overlay" @click="closeViewModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>配置详情</h3>
          <button @click="closeViewModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div class="config-details">
            <div class="detail-item">
              <label>平台:</label>
              <span>{{ viewingAccount?.platform?.name || '未知' }}</span>
            </div>
            <div class="detail-item">
              <label>用户名:</label>
              <span>{{ viewingAccount?.username }}</span>
            </div>
            <div class="detail-item">
              <label>状态:</label>
              <span class="status-tag" :class="viewingAccount?.status">
                {{ getStatusName(viewingAccount?.status) }}
              </span>
            </div>
            <div class="detail-item">
              <label>成功率:</label>
              <span>{{ (viewingAccount?.success_rate || 0).toFixed(1) }}%</span>
            </div>
            <div class="detail-item">
              <label>总请求数:</label>
              <span>{{ viewingAccount?.total_requests || 0 }}</span>
            </div>
            <div class="detail-item">
              <label>成功请求:</label>
              <span>{{ viewingAccount?.success_requests || 0 }}</span>
            </div>
            <div class="detail-item">
              <label>错误次数:</label>
              <span>{{ viewingAccount?.error_count || 0 }}</span>
            </div>
            <div class="detail-item">
              <label>最后错误:</label>
              <span>{{ viewingAccount?.last_error_message || '无' }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间:</label>
              <span>{{ formatDate(viewingAccount?.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间:</label>
              <span>{{ formatDate(viewingAccount?.updated_at) }}</span>
            </div>
            <div class="detail-item">
              <label>最后使用:</label>
              <span>{{ formatDate(viewingAccount?.last_used_at) }}</span>
            </div>
            <div class="detail-item">
              <label>提取来源:</label>
              <span>{{ viewingAccount?.extracted_from || '手动添加' }}</span>
            </div>
            <div class="detail-item">
              <label>备注:</label>
              <span>{{ viewingAccount?.notes || '无' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import api from '../api'

// 响应式数据
const accounts = ref([])
const platforms = ref([])
const proxies = ref([])
const stats = ref({})
const loading = ref(false)
const logging = ref(null)
const showLoginModal = ref(false)
const showRefreshModal = ref(false)  // 独立的Token刷新模态框
const showAddModal = ref(false)      // 添加配置模态框
const showEditModal = ref(false)     // 编辑配置模态框
const showViewModal = ref(false)
const viewingAccount = ref(null)
const submitting = ref(false)        // 提交状态

// 筛选器
const filters = ref({
  platform: '',
  status: ''
})



// 登录表单
const loginForm = ref({
  platform_id: '',
  username: '',
  proxy_id: null,
  force_relogin: true  // 默认勾选强制重新登录
})

// Token刷新表单
const refreshForm = ref({
  platform_id: '',
  username: '',
  proxy_id: null
})

// 添加配置表单
const addForm = ref({
  platform_id: '',
  username: '',
  password: '',
  proxy_id: null,
  notes: ''
})

// 编辑配置表单
const editForm = ref({
  id: null,
  platform_id: '',
  username: '',
  password: '',
  proxy_id: null,
  status: '',
  priority: 1,
  max_requests_per_hour: 100,
  notes: ''
})

// 登录日志
const loginLogs = ref([])
const logsContainer = ref(null)

// Token刷新日志
const refreshLogs = ref([])
const refreshLogsContainer = ref(null)

// 自动刷新相关
const autoRefreshEnabled = ref(false)
const autoRefreshTimer = ref(null)
const autoRefreshConfig = ref({
  scanInterval: 1, // 扫描间隔（分钟）
  expireWarning: 5, // 过期预警时间（分钟）
  maxFailures: 3 // 最大失败次数
})

// 状态名称映射
const getStatusName = (status) => {
  const names = {
    'active': '活跃',
    'inactive': '非活跃',
    'expired': '已过期',
    'error': '错误'
  }
  return names[status] || status
}

// 登录状态名称映射
const getLoginStatusName = (status) => {
  const names = {
    'not_logged_in': '未登录',
    'logged_in': '已登录',
    'login_failed': '登录失败',
    'expired': '已过期'
  }
  return names[status] || '未知'
}

// 爬虫池状态名称映射
const getPoolStatusName = (status) => {
  const names = {
    'available': '已启用',
    'disabled': '已禁用',
    'unhealthy': '不健康',
    'not_ready': '未就绪',
    'rate_limited': '限流中',
    'unknown': '未知'
  }
  return names[status] || '未知'
}

// 获取平台名称
const getPlatformName = (platformId) => {
  const platform = platforms.value.find(p => p.id === platformId)
  return platform ? platform.name : '未知平台'
}

// Token状态样式类
const getTokenStatusClass = (tokenStatus) => {
  const classes = {
    'valid': 'token-valid',
    'expired': 'token-expired',
    'no_token': 'token-none',
    'unknown': 'token-unknown'
  }
  return classes[tokenStatus] || 'token-unknown'
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 格式化日志时间
const formatLogTime = (time) => {
  return time.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 添加日志
const addLog = (message, type = 'info') => {
  loginLogs.value.push({
    time: new Date(),
    message,
    type
  })

  // 自动滚动到底部
  setTimeout(() => {
    if (logsContainer.value) {
      logsContainer.value.scrollTop = logsContainer.value.scrollHeight
    }
  }, 100)
}

// 清空日志
const clearLogs = () => {
  loginLogs.value = []
}

// 添加Token刷新日志
const addRefreshLog = (message, type = 'info') => {
  refreshLogs.value.push({
    time: new Date(),
    message,
    type
  })

  // 自动滚动到底部
  setTimeout(() => {
    if (refreshLogsContainer.value) {
      refreshLogsContainer.value.scrollTop = refreshLogsContainer.value.scrollHeight
    }
  }, 100)
}

// 清空Token刷新日志
const clearRefreshLogs = () => {
  refreshLogs.value = []
}

// 加载账号列表
const loadAccounts = async () => {
  loading.value = true
  try {
    const params = {}
    if (filters.value.platform) params.platform = filters.value.platform
    if (filters.value.status) params.status = filters.value.status

    const response = await api.crawlerConfig.getAccounts(params)
    if (response.code === 200) {
      accounts.value = response.data || []
    } else {
      accounts.value = []
      showMessage('错误', response.message || '加载失败')
    }
  } catch (error) {
    console.error('加载账号失败:', error)
    showMessage('错误', '加载账号失败')
    accounts.value = []
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await api.crawlerConfig.getStats()
    if (response.code === 200) {
      stats.value = response.data || {}
    }
  } catch (error) {
    console.error('加载统计失败:', error)
  }
}

// 加载代理列表
const loadProxies = async () => {
  try {
    const response = await api.proxies.getAll()
    if (response.code === 200) {
      proxies.value = response.data || []
    }
  } catch (error) {
    console.error('加载代理失败:', error)
    proxies.value = []
  }
}

// 显示添加配置模态框
const openAddModal = () => {
  // 重置表单
  addForm.value = {
    platform_id: '',
    username: '',
    password: '',
    proxy_id: null,
    notes: ''
  }
  showAddModal.value = true
}

// 关闭添加配置模态框
const closeAddModal = () => {
  showAddModal.value = false
  addForm.value = {
    platform_id: '',
    username: '',
    password: '',
    proxy_id: null,
    notes: ''
  }
}

// 提交添加配置表单
const submitAddForm = async () => {
  submitting.value = true
  try {
    const response = await api.crawlerConfig.createAccount(addForm.value)
    if (response.code === 200) {
      showMessage('成功', '配置添加成功！')
      closeAddModal()
      loadAccounts() // 重新加载账号列表
    } else {
      showMessage('错误', response.message || '添加失败')
    }
  } catch (error) {
    console.error('添加配置失败:', error)
    showMessage('错误', '添加配置失败')
  } finally {
    submitting.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadAccounts()
  loadStats()
}

// 自动刷新相关方法
const toggleAutoRefresh = async () => {
  if (autoRefreshEnabled.value) {
    console.log('[AUTO_REFRESH] 自动刷新已启用')
    await startAutoRefresh()
  } else {
    console.log('[AUTO_REFRESH] 自动刷新已禁用')
    stopAutoRefresh()
  }
}

const startAutoRefresh = async () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
  }

  // 立即执行一次扫描
  console.log('[AUTO_REFRESH] 自动刷新已启用，立即执行首次扫描')
  await scanAndRefreshTokens()

  // 然后启动定时器
  const intervalMs = autoRefreshConfig.value.scanInterval * 60 * 1000
  autoRefreshTimer.value = setInterval(() => {
    scanAndRefreshTokens()
  }, intervalMs)

  console.log(`[AUTO_REFRESH] 定时器已启动，间隔: ${autoRefreshConfig.value.scanInterval}分钟`)
}

const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value)
    autoRefreshTimer.value = null
  }
  console.log('[AUTO_REFRESH] 定时器已停止')
}

const scanAndRefreshTokens = async () => {
  const scanStartTime = new Date().toLocaleTimeString()
  console.log(`[AUTO_REFRESH] ${scanStartTime} - 开始扫描即将过期的Token`)

  try {
    // 先刷新账号列表以获取最新状态
    await loadAccounts()

    // 获取所有已启用的账号
    const enabledAccounts = accounts.value.filter(account =>
      account.is_enabled &&
      account.login_status === 'logged_in'
    )

    console.log(`[AUTO_REFRESH] 总账号数: ${accounts.value.length}, 已启用且已登录: ${enabledAccounts.length}`)

    if (enabledAccounts.length === 0) {
      console.log('[AUTO_REFRESH] 没有找到已启用且已登录的账号，跳过本次扫描')
      return
    }

    // 检查即将过期的Token
    const now = new Date()
    const warningTime = autoRefreshConfig.value.expireWarning * 60 * 1000 // 转换为毫秒

    const expiringAccounts = []
    const validAccounts = []

    enabledAccounts.forEach(account => {
      if (!account.token_expires_at) {
        console.log(`[AUTO_REFRESH] 账号 ${account.username} 没有Token过期时间`)
        return
      }

      const expiresAt = new Date(account.token_expires_at)
      const timeUntilExpiry = expiresAt.getTime() - now.getTime()
      const minutesUntilExpiry = Math.floor(timeUntilExpiry / (1000 * 60))

      if (timeUntilExpiry <= warningTime) {
        // Token已过期或即将在预警时间内过期
        expiringAccounts.push({
          ...account,
          minutesUntilExpiry,
          isExpired: timeUntilExpiry <= 0
        })
        console.log(`[AUTO_REFRESH] 账号 ${account.username} Token${timeUntilExpiry <= 0 ? '已过期' : `将在${minutesUntilExpiry}分钟后过期`}`)
      } else {
        validAccounts.push(account)
        console.log(`[AUTO_REFRESH] 账号 ${account.username} Token还有${minutesUntilExpiry}分钟过期，无需刷新`)
      }
    })

    console.log(`[AUTO_REFRESH] Token状态统计: 需要刷新 ${expiringAccounts.length} 个, 正常 ${validAccounts.length} 个`)

    if (expiringAccounts.length === 0) {
      console.log('[AUTO_REFRESH] 没有找到需要刷新的Token，本次扫描完成')
      return
    }

    // 逐个刷新Token
    let refreshedCount = 0
    let failedCount = 0

    for (const account of expiringAccounts) {
      try {
        console.log(`[AUTO_REFRESH] 开始自动刷新账号: ${account.username} (平台: ${getPlatformName(account.platform_id)}) - ${account.isExpired ? '已过期' : `${account.minutesUntilExpiry}分钟后过期`}`)

        const refreshData = {
          platform_id: account.platform_id,
          username: account.username,
          proxy_id: account.proxy_id || null
        }

        const response = await api.crawlerConfig.refreshToken(refreshData)

        if (response.code === 200 && response.data.success) {
          refreshedCount++
          console.log(`[AUTO_REFRESH] ✅ 账号 ${account.username} 刷新成功`)
          if (response.data.data?.token_expires_at) {
            const newExpiresAt = new Date(response.data.data.token_expires_at)
            console.log(`[AUTO_REFRESH] 新Token过期时间: ${newExpiresAt.toLocaleString()}`)
          }
        } else {
          failedCount++
          const errorMsg = response.data?.message || response.message || '未知错误'
          console.log(`[AUTO_REFRESH] ❌ 账号 ${account.username} 刷新失败: ${errorMsg}`)
        }

        // 避免请求过于频繁，每个账号之间间隔3秒
        await new Promise(resolve => setTimeout(resolve, 3000))

      } catch (error) {
        failedCount++
        console.error(`[AUTO_REFRESH] ❌ 账号 ${account.username} 刷新异常:`, error)
      }
    }

    const scanEndTime = new Date().toLocaleTimeString()
    console.log(`[AUTO_REFRESH] ${scanEndTime} - Token扫描完成: 成功刷新 ${refreshedCount} 个, 失败 ${failedCount} 个`)

    // 刷新完成后重新加载账号列表以获取最新状态
    if (refreshedCount > 0) {
      console.log('[AUTO_REFRESH] 重新加载账号列表以获取最新Token状态')
      await loadAccounts()
    }

  } catch (error) {
    console.error('[AUTO_REFRESH] 自动刷新过程中发生错误:', error)
  }
}

const openAutoRefreshConfig = async () => {
  // 这里可以打开配置对话框
  const currentInterval = autoRefreshConfig.value.scanInterval
  const currentWarning = autoRefreshConfig.value.expireWarning

  const newInterval = prompt(`请输入扫描间隔（分钟，当前: ${currentInterval}）:`, currentInterval)
  if (newInterval && !isNaN(newInterval) && newInterval > 0) {
    const newWarning = prompt(`请输入过期预警时间（分钟，当前: ${currentWarning}）:`, currentWarning)
    if (newWarning && !isNaN(newWarning) && newWarning > 0) {
      autoRefreshConfig.value.scanInterval = parseInt(newInterval)
      autoRefreshConfig.value.expireWarning = parseInt(newWarning)

      // 如果自动刷新正在运行，重新启动定时器
      if (autoRefreshEnabled.value) {
        console.log('[AUTO_REFRESH] 配置已更新，重新启动自动刷新')
        stopAutoRefresh()
        await startAutoRefresh()
      }

      showMessage('成功', `配置已更新: 扫描间隔 ${newInterval} 分钟, 预警时间 ${newWarning} 分钟`)
    }
  }
}



// 查看账号详情
const viewAccount = (account) => {
  viewingAccount.value = account
  showViewModal.value = true
}

// 编辑账号
const editAccount = async (account) => {
  try {
    // 获取账号详细信息（包括解密后的密码）
    const response = await api.crawlerConfig.getAccount(account.id)
    if (response.code === 200) {
      const accountData = response.data

      // 填充编辑表单
      editForm.value = {
        id: accountData.id,
        platform_id: accountData.platform_id,
        username: accountData.username,
        password: accountData.password || '', // 解密后的密码
        proxy_id: accountData.proxy_id || null,
        status: accountData.status || 'active',
        priority: accountData.priority || 1,
        max_requests_per_hour: accountData.max_requests_per_hour || 100,
        notes: accountData.notes || ''
      }

      showEditModal.value = true
    } else {
      showMessage('错误', response.message || '获取账号信息失败')
    }
  } catch (error) {
    console.error('获取账号信息失败:', error)
    showMessage('错误', '获取账号信息失败')
  }
}

// 删除账号
const deleteAccount = async (account) => {
  if (!confirm(`确定要删除账号 "${account.username}" 吗？`)) {
    return
  }

  try {
    const response = await api.crawlerConfig.deleteAccount(account.id)
    if (response.code === 200) {
      showMessage('成功', '账号删除成功')
      loadAccounts()
      loadStats()
    } else {
      showMessage('错误', response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除账号失败:', error)
    showMessage('错误', '删除账号失败')
  }
}

// 账号登录
const loginAccount = async (account) => {
  // 清空之前的日志
  loginLogs.value = []

  // 打开登录模态框
  loginForm.value = {
    platform_id: account.platform_id,
    username: account.username,
    proxy_id: account.proxy_id || null,
    force_relogin: true  // 默认勾选
  }
  showLoginModal.value = true

  // 添加初始日志
  addLog(`准备登录账号: ${account.username}`, 'info')
  addLog(`平台: ${getPlatformName(account.platform_id)}`, 'info')
}

// 执行登录
const executeLogin = async () => {
  logging.value = true

  try {
    addLog('开始执行登录...', 'info')
    addLog('正在启动Chrome浏览器...', 'info')
    addLog('浏览器将打开登录页面，请手动完成登录', 'info')
    addLog('系统将每2秒检测一次登录状态和Token', 'info')

    const response = await api.crawlerConfig.loginAccount(loginForm.value)

    if (response.code === 200) {
      if (response.data.success) {
        addLog('登录成功！Cookie和Token已提取', 'success')
        addLog(`提取到 ${response.data.data?.cookie_count || 0} 个Cookie`, 'success')
        if (response.data.data?.token) {
          addLog(`Token: ${response.data.data.token.substring(0, 10)}...`, 'success')
        }
        if (response.data.data?.token_expires_at) {
          addLog(`Token过期时间: ${new Date(response.data.data.token_expires_at).toLocaleString()}`, 'info')
        }
        addLog('正在更新账号信息...', 'info')
        addLog('登录流程完成', 'success')

        showMessage('成功', '账号登录成功！')

        // 延迟关闭模态框，让用户看到完整的日志
        setTimeout(() => {
          showLoginModal.value = false
          loadAccounts()
        }, 2000)
      } else {
        addLog(`登录失败: ${response.data.message}`, 'error')
        showMessage('错误', response.data.message || '登录失败')
      }
    } else {
      addLog(`请求失败: ${response.message}`, 'error')
      showMessage('错误', response.message || '登录失败')
    }
  } catch (error) {
    console.error('账号登录失败:', error)
    addLog(`登录异常: ${error.message}`, 'error')
    showMessage('错误', '账号登录失败')
  } finally {
    logging.value = false
  }
}

// 刷新Token
const refreshToken = async (account) => {
  // 清空之前的日志
  refreshLogs.value = []

  // 设置刷新表单
  refreshForm.value = {
    platform_id: account.platform_id,
    username: account.username,
    proxy_id: account.proxy_id || null
  }

  // 打开Token刷新模态框
  showRefreshModal.value = true

  // 添加初始日志
  addRefreshLog(`准备获取Token: ${account.username}`, 'info')
  addRefreshLog(`平台: ${getPlatformName(account.platform_id)}`, 'info')
  addRefreshLog('将访问平台主页获取最新Token和Cookie', 'info')

  // 自动执行刷新
  executeRefreshToken()
}

// 执行刷新Token
const executeRefreshToken = async () => {
  logging.value = true

  try {
    addRefreshLog('开始执行Token获取...', 'info')
    addRefreshLog('正在启动Chrome浏览器...', 'info')
    addRefreshLog('浏览器将访问平台主页获取Token和Cookie', 'info')
    addRefreshLog('系统将每2秒检测一次Token状态', 'info')

    const response = await api.crawlerConfig.refreshToken(refreshForm.value)

    if (response.code === 200) {
      if (response.data.success) {
        addRefreshLog('Token获取成功！Cookie和Token已提取', 'success')
        addRefreshLog(`提取到 ${response.data.data?.cookie_count || 0} 个Cookie`, 'success')
        if (response.data.data?.token) {
          addRefreshLog(`Token: ${response.data.data.token.substring(0, 10)}...`, 'success')
        }
        if (response.data.data?.token_expires_at) {
          addRefreshLog(`Token过期时间: ${new Date(response.data.data.token_expires_at).toLocaleString()}`, 'info')
        }
        addRefreshLog('正在更新账号信息...', 'info')
        addRefreshLog('Token获取流程完成', 'success')

        showMessage('成功', 'Token获取成功！')

        // 延迟关闭模态框，让用户看到完整的日志
        setTimeout(() => {
          showRefreshModal.value = false
          loadAccounts()
        }, 2000)
      } else {
        const message = response.data.message || 'Token获取失败'
        if (response.data.cancelled) {
          addRefreshLog(`用户关闭了浏览器`, 'info')
          showMessage('提示', '操作已取消')
        } else if (response.data.timeout) {
          addRefreshLog(`操作超时`, 'warning')
          showMessage('提示', '操作超时，请稍后重试')
        } else {
          addRefreshLog(`Token获取失败: ${message}`, 'error')
          showMessage('错误', message)
        }
      }
    } else {
      addRefreshLog(`请求失败: ${response.message}`, 'error')
      showMessage('错误', response.message || 'Token获取失败')
    }
  } catch (error) {
    console.error('Token获取失败:', error)
    addRefreshLog(`获取异常: ${error.message}`, 'error')
    showMessage('错误', 'Token获取失败')
  } finally {
    logging.value = false
  }
}





// 关闭登录模态框
const closeLoginModal = () => {
  showLoginModal.value = false
  loginForm.value = {
    platform_id: '',
    username: '',
    proxy_id: null,
    force_relogin: true
  }
  // 清空日志
  loginLogs.value = []
}

// 关闭Token刷新模态框
const closeRefreshModal = () => {
  showRefreshModal.value = false
  refreshForm.value = {
    platform_id: '',
    username: '',
    proxy_id: null
  }
  // 清空日志
  refreshLogs.value = []
}

// 提交编辑表单
const submitEditForm = async () => {
  submitting.value = true
  try {
    const response = await api.crawlerConfig.updateAccount(editForm.value.id, editForm.value)
    if (response.code === 200) {
      showMessage('成功', '配置更新成功！')
      closeEditModal()
      loadAccounts() // 重新加载账号列表
    } else {
      showMessage('错误', response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新配置失败:', error)
    showMessage('错误', '更新配置失败')
  } finally {
    submitting.value = false
  }
}

// 关闭编辑模态框
const closeEditModal = () => {
  showEditModal.value = false
  editForm.value = {
    id: null,
    platform_id: '',
    username: '',
    password: '',
    proxy_id: null,
    status: '',
    priority: 1,
    max_requests_per_hour: 100,
    notes: ''
  }
}

// 关闭查看模态框
const closeViewModal = () => {
  showViewModal.value = false
  viewingAccount.value = null
}

// 简单的消息提示
const showMessage = (type, text) => {
  alert(`${type}: ${text}`)
}

// 加载平台数据
const loadPlatforms = async () => {
  try {
    const response = await api.platforms.getList()
    if (response.code === 200) {
      platforms.value = response.data
    }
  } catch (error) {
    console.error('加载平台失败:', error)
  }
}

// 切换账号启用状态
const toggleAccount = async (account) => {
  try {
    const response = await api.crawlerConfig.toggleAccount(account.id)
    if (response.code === 200) {
      // 更新本地数据
      account.is_enabled = response.data.is_enabled
      account.pool_status = response.data.pool_status
      account.can_use = response.data.can_use

      const statusText = account.is_enabled ? '启用' : '禁用'
      showMessage('成功', `账号已${statusText}`)
    } else {
      showMessage('错误', response.message || '切换状态失败')
    }
  } catch (error) {
    console.error('切换账号状态失败:', error)
    showMessage('错误', '切换账号状态失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadAccounts()
  loadStats()
  loadPlatforms()
  loadProxies()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.crawler-config-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 自动刷新开关样式 */
.auto-refresh-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.auto-refresh-toggle .toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.auto-refresh-toggle .toggle-label input[type="checkbox"] {
  display: none;
}

.auto-refresh-toggle .toggle-slider {
  width: 40px;
  height: 20px;
  background: #cbd5e1;
  border-radius: 10px;
  position: relative;
  transition: background 0.3s ease;
}

.auto-refresh-toggle .toggle-slider::before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.auto-refresh-toggle .toggle-label input[type="checkbox"]:checked + .toggle-slider {
  background: #10b981;
}

.auto-refresh-toggle .toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.auto-refresh-toggle .toggle-text {
  font-weight: 500;
  white-space: nowrap;
}

.btn-config {
  padding: 6px 8px;
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.btn-config:hover {
  background: #e2e8f0;
  border-color: #94a3b8;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.filter-item select {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.accounts-table {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid #e9ecef;
  overflow-x: auto;
}

.table-header {
  display: grid;
  grid-template-columns: 60px 80px 120px 120px 180px 60px 60px 60px 250px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 14px;
  height: 50px;
  min-width: 1000px;
}

.table-header .col {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  padding: 0 8px;
  text-align: center;
  white-space: nowrap;
}

.table-row {
  display: grid;
  grid-template-columns: 60px 80px 120px 120px 180px 60px 60px 60px 250px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  min-height: 80px;
  align-items: center;
  min-width: 1000px;
}

.table-row:hover {
  background: linear-gradient(135deg, #f8f9ff 0%, #f5f7ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.col {
  padding: 16px 12px;
  display: flex;
  align-items: center;
  font-size: 14px;
  min-height: 60px;
}

/* 列宽度定义 */
.col-id {
  width: 50px;
  min-width: 50px;
  justify-content: center;
  font-weight: 600;
  /* color: #666; */
}

.col-platform {
  width: 80px;
  min-width: 80px;
}

.col-username {
  width: 120px;
  min-width: 120px;
}

.col-status {
  width: 110px;
  min-width: 110px;
}

.col-token-time {
  width: 200px;
  min-width: 140px;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.col-success-rate {
  width: 60px;
  min-width: 60px;
  justify-content: center;
}

.col-requests {
  width: 60px;
  min-width: 60px;
  justify-content: center;
}

.col-errors {
  width: 60px;
  min-width: 60px;
  justify-content: center;
}

.col-last-used {
  width: 120px;
  min-width: 120px;
}

.col-actions {
  width: 250px;
  min-width: 200px;
}

/* Token信息样式 */
.token-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  font-size: 12px;
}

.token-remaining {
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.token-remaining.token-valid {
  background: #f0fdf4;
  color: #16a34a;
  border: 1px solid #86efac;
}

.token-remaining.token-expired {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fca5a5;
}

.token-remaining.token-none {
  background: #f9fafb;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.token-remaining.token-unknown {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #fcd34d;
}

.last-login {
  color: #6b7280;
  font-size: 12px;
  text-align: center;
}

.col-platform {
  justify-content: center;
}

.col-username {
  /* flex-direction: column; */
  /* align-items: flex-start; */
  justify-content: center;
}

.col-status {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.col-success-rate,
.col-requests,
.col-errors {
  justify-content: center;
  /* font-weight: 500; */
}

.col-last-used {
  font-size: 12px;
  color: #666;
  justify-content: center;
  text-align: center;
}

.col-actions {
  justify-content: center;
}

/* 用户名信息样式 */
.username-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.username {
  font-weight: 600;
  color: #333;
}

/* 状态容器样式 */
.status-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.status-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

/* 操作容器样式 */
.actions-container {
  /* display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 160px; */



  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.platform-tag {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.platform-tag.platform-taobao {
  background: #ff6900;
  color: white;
}

.platform-tag.platform-jingdong {
  background: #e3101e;
  color: white;
}

.platform-tag.platform-pinduoduo {
  background: #e02e24;
  color: white;
}

.platform-tag.platform-1688 {
  background: #ff6600;
  color: white;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.status-tag.active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-tag.inactive {
  background: #f5f5f5;
  color: #999;
  border: 1px solid #d9d9d9;
}

.status-tag.expired {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-tag.error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.error-count {
  color: #ff4d4f;
  font-weight: bold;
}

/* 爬虫池状态样式 */
.pool-status-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pool-status-tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
}

.pool-status-tag.available {
  background: #f6ffed;
  color: #52c41a;
  border-color: #b7eb8f;
}

.pool-status-tag.disabled {
  background: #f5f5f5;
  color: #999;
  border-color: #d9d9d9;
}

.pool-status-tag.unhealthy {
  background: #fff2f0;
  color: #ff4d4f;
  border-color: #ffccc7;
}

.pool-status-tag.not_ready {
  background: #fff7e6;
  color: #fa8c16;
  border-color: #ffd591;
}

.pool-status-tag.rate_limited {
  background: #f0f5ff;
  color: #1890ff;
  border-color: #adc6ff;
}

.pool-status-tag.unknown {
  background: #fafafa;
  color: #666;
  border-color: #d9d9d9;
}

/* 苹果风格开关样式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
  cursor: pointer;
}

.toggle-switch input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.switch-label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 24px;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.switch-slider {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .switch-label {
  background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
}

.toggle-switch input:checked + .switch-label .switch-slider {
  transform: translateX(20px);
}

.toggle-switch:hover .switch-label {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.toggle-switch input:checked:hover + .switch-label {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 120px;
}

/* 操作按钮容器 */
.actions-container {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

/* 操作按钮基础样式 */
.btn-action {
  /* width: 32px; */
  /* height: 32px;
  padding: 0;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: #f8fafc;
  border: 1px solid #e2e8f0; */
}

.btn-action:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}



/* 登录按钮 */
.btn-login {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.btn-login:hover {
  background: #059669;
  border-color: #059669;
}

/* 刷新按钮 */
.btn-refresh {
  background: #f59e0b;
  color: white;
  border-color: #f59e0b;
}

.btn-refresh:hover {
  background: #d97706;
  border-color: #d97706;
}

/* 查看按钮 */
.btn-view {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
  padding: 3px 6px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
}

.btn-view:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* 编辑按钮 */
.btn-edit {
  background: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
  padding: 3px 6px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
}

.btn-edit:hover {
  background: #7c3aed;
  border-color: #7c3aed;
}

/* 删除按钮 */
.btn-delete {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  padding: 3px 6px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
}

.btn-delete:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.loading, .empty {
  text-align: center;
  padding: 40px;
  color: #666;
}

.btn-primary {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary:hover {
  background: #40a9ff;
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  padding: 8px 16px;
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-secondary:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 600px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

.login-modal {
  width: 900px;
  max-width: 95vw;
}

.login-modal .modal-body {
  display: flex;
  gap: 20px;
  padding: 20px;
}

.refresh-modal {
  width: 900px;
  max-width: 95vw;
}

.refresh-modal .modal-body {
  display: flex;
  gap: 20px;
  padding: 20px;
}

.add-modal {
  width: 500px;
  max-width: 95vw;
}

.add-modal .modal-body {
  padding: 20px;
}

.edit-modal {
  width: 700px;
  max-width: 95vw;
}

.edit-modal .modal-body {
  padding: 20px;
}

/* 表单行布局 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row .form-group {
  margin-bottom: 0;
}

/* 表单提示文字样式 */
.form-hint {
  display: block;
  margin-top: 4px;
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #999;
  padding: 4px;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}



/* 登录配置区域 */
.login-config {
  flex: 1;
  min-width: 400px;
}

/* Token刷新配置区域 */
.refresh-config {
  flex: 1;
  min-width: 400px;
}

.login-tip {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.refresh-tip {
  background: #fff7ed;
  border: 1px solid #fa8c16;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.tip-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.tip-content p {
  margin: 4px 0;
  font-size: 14px;
  color: #0369a1;
}

.tip-content ol {
  margin: 8px 0;
  padding-left: 20px;
  color: #0369a1;
}

.tip-content ol li {
  margin: 4px 0;
  font-size: 14px;
}

.tip-content .warning {
  color: #d97706;
  font-weight: bold;
  margin-top: 8px;
}

.account-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
}

.info-item .label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.info-item .value {
  color: #212529;
  font-size: 14px;
  font-weight: 500;
}

/* 自定义复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
  user-select: none;
  padding: 8px 0;  /* 增加上下内边距 */
  min-height: 34px; /* 确保最小高度 */
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  min-width: 18px;  /* 防止被挤压 */
  min-height: 18px; /* 防止被挤压 */
  border: 2px solid #dee2e6;
  border-radius: 4px;
  margin-right: 8px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;   /* 防止在flex布局中被压缩 */
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background-color: #0ea5e9;
  border-color: #0ea5e9;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.form-group {
  margin-bottom: 16px;
}

/* 复选框表单组特殊样式 */
.checkbox-group {
  margin-bottom: 20px;
  padding: 4px 0;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.config-details {
  display: grid;
  gap: 12px;
}

.detail-item {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 12px;
  align-items: center;
}

.detail-item label {
  font-weight: 500;
  color: #666;
}

.detail-item span {
  color: #333;
  word-break: break-all;
}

/* 登录状态样式 */
.login-status {
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid;
}

.login-status.not_logged_in {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fca5a5;
}

.login-status.logged_in {
  background: #f0fdf4;
  color: #16a34a;
  border-color: #86efac;
}

.login-status.login_failed {
  background: #fffbeb;
  color: #d97706;
  border-color: #fcd34d;
}

.login-status.expired {
  background: #f9fafb;
  color: #6b7280;
  border-color: #d1d5db;
}

/* 登录和刷新按钮样式 */
.btn-login {
  background: #10b981;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
}

.btn-login:hover {
  background: #059669;
}

.btn-login:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.btn-refresh {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
}

.btn-refresh:hover {
  background: #2563eb;
}

.btn-refresh:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 登录提示样式 */
.login-tip {
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.login-tip p {
  margin: 4px 0;
  font-size: 14px;
  color: #0369a1;
}

.login-tip .warning {
  color: #d97706;
  font-weight: bold;
}

/* 账号信息样式 */
.account-info {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

/* 日志区域 */
.login-logs {
  flex: 1;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
}

.refresh-logs {
  flex: 1;
  min-width: 400px;
  display: flex;
  flex-direction: column;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  background: #ffffff;
  border-radius: 8px 8px 0 0;
}

.logs-header h4 {
  margin: 0;
  font-size: 16px;
  color: #495057;
  font-weight: 500;
}

.btn-clear {
  padding: 4px 8px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.btn-clear:hover {
  background: #5a6268;
}

.logs-content {
  flex: 1;
  height: 300px;
  overflow-y: auto;
  padding: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.logs-empty {
  color: #6c757d;
  text-align: center;
  padding: 40px 20px;
  font-style: italic;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.1s;
}

.log-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

.log-time {
  color: #6c757d;
  margin-right: 12px;
  font-size: 12px;
  flex-shrink: 0;
  width: 70px;
}

.log-message {
  flex: 1;
  word-break: break-word;
}

.log-item.info {
  color: #495057;
}

.log-item.success {
  color: #198754;
  background: rgba(25, 135, 84, 0.1);
}

.log-item.error {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.log-item.warning {
  color: #fd7e14;
  background: rgba(253, 126, 20, 0.1);
}

/* 日志滚动条样式 */
.logs-content::-webkit-scrollbar {
  width: 6px;
}

.logs-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.logs-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.logs-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
