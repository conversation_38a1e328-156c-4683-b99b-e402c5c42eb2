#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试1688原始数据结构
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from app.crawler.platforms.alibaba_1688 import Alibaba1688Crawler
import json

def debug_1688_raw_data():
    """调试1688原始数据结构"""
    url = "https://detail.1688.com/offer/932222752479.html"
    
    try:
        print("🔍 获取1688原始数据...")
        crawler = Alibaba1688Crawler()
        
        # 获取商品详情（这会包含原始数据解析）
        import asyncio
        result = asyncio.run(crawler.get_product_detail(url))

        if not result.get('success'):
            print(f"❌ 无法获取商品详情: {result.get('error')}")
            return False

        print(f"✅ 获取商品详情成功")

        # 我们需要直接调用解析方法来获取原始数据
        # 先获取HTML内容
        account_info = asyncio.run(crawler._get_valid_account())
        if not account_info:
            print("❌ 无法获取有效账号")
            return False

        headers = {
            'User-Agent': account_info.get('user_agent', crawler.default_headers['user-agent']),
            'Cookie': account_info['cookies'],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        import requests
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code != 200:
            print(f"❌ 无法获取HTML内容，状态码: {response.status_code}")
            return False

        html_content = response.text
        print(f"✅ 获取HTML内容: {len(html_content)} 字符")

        # 提取初始化数据
        init_data = crawler._extract_init_data_from_html(html_content)
        if not init_data:
            print("❌ 无法提取初始化数据")
            return False
        
        print(f"✅ 提取初始化数据成功")
        
        # 分析数据结构
        print("\n📊 数据结构分析:")
        print(f"根节点键: {list(init_data.keys())}")
        
        if 'result' in init_data:
            result = init_data['result']
            print(f"result键: {list(result.keys())}")
            
            if 'data' in result:
                data = result['data']
                print(f"result.data键: {list(data.keys())}")
                
                if 'Root' in data:
                    root = data['Root']
                    print(f"result.data.Root键: {list(root.keys())}")
                    
                    if 'fields' in root:
                        fields = root['fields']
                        print(f"result.data.Root.fields键: {list(fields.keys())}")
                        
                        if 'dataJson' in fields:
                            data_json = fields['dataJson']
                            print(f"result.data.Root.fields.dataJson键: {list(data_json.keys())}")
                            
                            # 检查图片相关字段
                            print("\n🖼️ 图片相关字段:")
                            for key in data_json.keys():
                                if any(img_word in key.lower() for img_word in ['image', 'img', 'pic', 'photo']):
                                    print(f"  - {key}: {type(data_json[key])}")
                                    if isinstance(data_json[key], list):
                                        print(f"    长度: {len(data_json[key])}")
                                        if data_json[key]:
                                            print(f"    示例: {data_json[key][0]}")
                            
                            # 检查SKU相关字段
                            print("\n📋 SKU相关字段:")
                            if 'skuModel' in data_json:
                                sku_model = data_json['skuModel']
                                print(f"  skuModel键: {list(sku_model.keys())}")
                                
                                if 'skuProps' in sku_model:
                                    sku_props = sku_model['skuProps']
                                    print(f"  skuProps数量: {len(sku_props)}")
                                    for i, prop in enumerate(sku_props[:2]):  # 只显示前2个
                                        print(f"    属性{i+1}: {prop}")
                                
                                if 'skuInfoMap' in sku_model:
                                    sku_info_map = sku_model['skuInfoMap']
                                    print(f"  skuInfoMap数量: {len(sku_info_map)}")
                                    # 显示前几个SKU
                                    for i, (sku_name, sku_info) in enumerate(list(sku_info_map.items())[:3]):
                                        print(f"    SKU{i+1}: {sku_name} -> {sku_info}")
            
            if 'global' in result:
                global_data = result['global']
                print(f"result.global键: {list(global_data.keys())}")
                
                if 'globalData' in global_data:
                    global_data_model = global_data['globalData']
                    print(f"result.global.globalData键: {list(global_data_model.keys())}")
                    
                    if 'model' in global_data_model:
                        model = global_data_model['model']
                        print(f"result.global.globalData.model键: {list(model.keys())}")
                        
                        # 检查图片字段
                        if 'images' in model:
                            images = model['images']
                            print(f"  images数量: {len(images)}")
                            if images:
                                print(f"  images示例: {images[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = debug_1688_raw_data()
    sys.exit(0 if success else 1)
