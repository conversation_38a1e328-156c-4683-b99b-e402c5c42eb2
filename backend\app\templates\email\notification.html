{% extends "base.html" %}

{% block title %}{{ title or '系统通知' }} - AqentCrawler{% endblock %}

{% block header_title %}{{ header_title or '系统通知' }}{% endblock %}
{% block header_subtitle %}{{ header_subtitle or 'AqentCrawler 系统通知' }}{% endblock %}

{% block content %}
{% if notification_type %}
<div class="alert alert-{{ notification_type }}">
    <h3 style="margin: 0 0 10px 0;">
        {% if notification_type == 'success' %}✅{% elif notification_type == 'warning' %}⚠️{% elif notification_type == 'danger' %}❌{% else %}ℹ️{% endif %}
        {{ title or '系统通知' }}
    </h3>
    {% if message %}
    <p style="margin: 0;">{{ message }}</p>
    {% endif %}
</div>
{% endif %}

{% if content %}
<div style="margin: 20px 0;">
    {{ content | safe }}
</div>
{% endif %}

{% if data_table %}
<h4>{{ data_table.title or '详细信息' }}</h4>
<table class="data-table">
    {% if data_table.headers %}
    <thead>
        <tr>
            {% for header in data_table.headers %}
            <th>{{ header }}</th>
            {% endfor %}
        </tr>
    </thead>
    {% endif %}
    <tbody>
        {% for row in data_table.rows %}
        <tr>
            {% for cell in row %}
            <td>
                {% if cell is mapping %}
                    {% if cell.type == 'badge' %}
                    <span class="status-badge status-{{ cell.class or 'info' }}">
                        {{ cell.text }}
                    </span>
                    {% elif cell.type == 'link' %}
                    <a href="{{ cell.url }}" style="color: #1890ff; text-decoration: none;">{{ cell.text }}</a>
                    {% else %}
                    {{ cell.value or cell }}
                    {% endif %}
                {% else %}
                {{ cell }}
                {% endif %}
            </td>
            {% endfor %}
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}

{% if key_value_data %}
<h4>{{ key_value_data.title or '详细信息' }}</h4>
<table class="data-table">
    {% for item in key_value_data.items %}
    <tr>
        <td style="font-weight: 600; width: 30%;">{{ item.key or item.name }}</td>
        <td>
            {% if item.type == 'badge' %}
            <span class="status-badge status-{{ item.class or 'info' }}">
                {{ item.value }}
            </span>
            {% elif item.type == 'link' %}
            <a href="{{ item.url }}" style="color: #1890ff; text-decoration: none;">{{ item.value }}</a>
            {% elif item.type == 'timestamp' %}
            <span class="timestamp">{{ item.value }}</span>
            {% else %}
            {{ item.value }}
            {% endif %}
            {% if item.unit %}{{ item.unit }}{% endif %}
        </td>
    </tr>
    {% endfor %}
</table>
{% endif %}

{% if list_data %}
<h4>{{ list_data.title or '列表信息' }}</h4>
<ul>
    {% for item in list_data.items %}
    <li>
        {% if item is mapping %}
        <strong>{{ item.title }}:</strong> {{ item.description }}
        {% if item.status %}
        <span class="status-badge status-{{ item.status_class or 'info' }}">
            {{ item.status }}
        </span>
        {% endif %}
        {% else %}
        {{ item }}
        {% endif %}
    </li>
    {% endfor %}
</ul>
{% endif %}

{% if actions %}
<h4>操作按钮</h4>
<div style="text-align: center; margin: 20px 0;">
    {% for action in actions %}
    <a href="{{ action.url }}" class="btn btn-{{ action.type or 'primary' }}" style="margin: 5px;">
        {{ action.text }}
    </a>
    {% endfor %}
</div>
{% endif %}

{% if attachments %}
<h4>附件</h4>
<ul>
    {% for attachment in attachments %}
    <li>
        {% if attachment.url %}
        <a href="{{ attachment.url }}" style="color: #1890ff; text-decoration: none;">
            📎 {{ attachment.name }}
        </a>
        {% else %}
        📎 {{ attachment.name }}
        {% endif %}
        {% if attachment.size %}
        <small style="color: #666;">({{ attachment.size }})</small>
        {% endif %}
    </li>
    {% endfor %}
</ul>
{% endif %}

{% if footer_note %}
<div class="divider"></div>
<div class="alert alert-info">
    <p style="margin: 0;">{{ footer_note }}</p>
</div>
{% endif %}

<div class="divider"></div>

<p style="font-size: 14px; color: #666;">
    <strong>通知时间:</strong> {{ timestamp or now().strftime('%Y-%m-%d %H:%M:%S') }}<br>
    {% if sender %}
    <strong>发送者:</strong> {{ sender }}<br>
    {% endif %}
    {% if reference_id %}
    <strong>参考ID:</strong> {{ reference_id }}
    {% endif %}
</p>
{% endblock %}
