#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据结构路径问题
"""

import json

def debug_data_structure():
    """调试数据结构"""
    
    # 读取保存的数据文件
    with open('temp/1688_detail_data_591940079968.json', 'r', encoding='utf-8') as f:
        init_data = json.load(f)
    
    print("🔍 调试数据结构路径:")
    
    # 检查顶层键
    print(f"📋 顶层键: {list(init_data.keys())}")
    
    # 检查result路径
    result_data = init_data.get('result', {})
    if result_data:
        result_data_data = result_data.get('data', {})
        print(f"📋 result.data 存在: {len(result_data_data)} 个组件")
        if len(result_data_data) > 0:
            print(f"📋 result.data 前5个键: {list(result_data_data.keys())[:5]}")
    else:
        print("📋 result 不存在")
    
    # 检查直接data路径
    direct_data = init_data.get('data', {})
    if direct_data:
        print(f"📋 data 存在: {len(direct_data)} 个组件")
        if len(direct_data) > 0:
            print(f"📋 data 前5个键: {list(direct_data.keys())[:5]}")
            
            # 查找价格组件
            price_component_found = False
            logistics_component_found = False
            
            for component_key, component in direct_data.items():
                if isinstance(component, dict):
                    component_type = component.get('componentType')
                    if component_type == '@ali/tdmod-od-pc-offer-price':
                        print(f"✅ 在data路径找到价格组件: {component_key}")
                        price_component_found = True
                    elif component_type == '@ali/tdmod-od-pc-offer-logistics':
                        print(f"✅ 在data路径找到物流组件: {component_key}")
                        logistics_component_found = True
            
            if not price_component_found:
                print("❌ 在data路径未找到价格组件")
            if not logistics_component_found:
                print("❌ 在data路径未找到物流组件")
    else:
        print("📋 data 不存在")
    
    # 模拟路径选择逻辑
    print(f"\n🔍 路径选择逻辑测试:")
    print(f"len(direct_data) = {len(direct_data)}")
    print(f"len(result_data_data) = {len(result_data_data) if 'result_data_data' in locals() else 0}")
    
    if len(direct_data) > 0:
        print("✅ 应该选择 data 路径")
    elif len(result_data_data) > 0:
        print("✅ 应该选择 result.data 路径")
    else:
        print("❌ 没有可用的数据路径")

if __name__ == "__main__":
    debug_data_structure()
