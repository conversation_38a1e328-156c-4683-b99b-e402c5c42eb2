#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试1688多规格商品查找
根据用户提供的参考数据，查找真正的多规格商品
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
backend_path = project_root / "backend"
sys.path.insert(0, str(backend_path))

from app.crawler.platforms.alibaba_1688 import Alibaba1688Crawler

async def test_1688_multi_sku():
    """测试1688多规格商品解析"""
    print("🚀 开始测试1688多规格商品查找...")
    print("✅ 成功导入淘宝和1688爬虫")
    
    # 创建1688爬虫实例
    crawler = Alibaba1688Crawler()
    
    # 根据用户分析文档，这是一个iPhone手机壳商品，有颜色和型号两个规格
    # 我们需要找到这个商品的实际URL
    test_products = [
        {
            'url': 'https://detail.1688.com/offer/714791854866.html',
            'description': '测试已知商品 - 检查是否有多规格'
        },
        {
            'url': 'https://detail.1688.com/offer/932222752479.html',
            'description': '用户提到的多规格商品'
        }
    ]
    
    # 搜索iPhone手机壳相关商品
    search_keywords = [
        "iPhone手机壳",
        "苹果手机壳",
        "手机保护套"
    ]
    
    print("\n" + "="*60)
    print("🔍 首先搜索iPhone手机壳商品，寻找多规格商品")
    print("="*60)
    
    for keyword in search_keywords:
        try:
            print(f"\n🔍 搜索关键词: {keyword}")
            search_result = await crawler.search_products(keyword, page=1, page_size=5)
            
            if search_result.get('code') == 200:
                products = search_result.get('data', {}).get('products', [])
                print(f"✅ 找到 {len(products)} 个商品")
                
                for i, product in enumerate(products[:3]):  # 只测试前3个
                    product_id = product.get('id')
                    product_name = product.get('name', '')
                    
                    if product_id:
                        detail_url = f"https://detail.1688.com/offer/{product_id}.html"
                        print(f"\n📦 测试商品 {i+1}: {product_name[:50]}...")
                        print(f"🔗 URL: {detail_url}")
                        
                        # 获取商品详情
                        detail_result = await crawler.get_product_detail(detail_url)
                        
                        if detail_result.get('code') == 200:
                            detail_data = detail_result.get('data', {})
                            skus = detail_data.get('skus', [])
                            
                            print(f"📋 SKU数量: {len(skus)}")
                            
                            if len(skus) > 1:
                                print(f"🎯 找到多规格商品！SKU数量: {len(skus)}")
                                print(f"📦 商品名称: {detail_data.get('name', '')}")
                                print(f"🔗 商品链接: {detail_url}")
                                
                                # 显示前几个SKU的属性
                                for j, sku in enumerate(skus[:3]):
                                    properties = sku.get('properties', [])
                                    prop_str = " | ".join([f"{p.get('name')}:{p.get('value')}" for p in properties])
                                    print(f"  SKU {j+1}: {prop_str}")
                                
                                # 找到多规格商品后，进行详细测试
                                print(f"\n🔬 对多规格商品进行详细分析...")
                                await test_product_detail(crawler, detail_url)
                                return  # 找到一个就够了
                            else:
                                print("⚠️ 单规格商品")
                        else:
                            print(f"❌ 获取商品详情失败: {detail_result.get('message', '')}")
            else:
                print(f"❌ 搜索失败: {search_result.get('message', '')}")
                
        except Exception as e:
            print(f"❌ 搜索异常: {str(e)}")
    
    print("\n⚠️ 未找到多规格商品，测试已知商品...")
    
    # 如果搜索没找到，测试已知商品
    for i, product in enumerate(test_products, 1):
        print(f"\n" + "="*60)
        print(f"🔍 测试商品 {i}: {product['description']}")
        print(f"📍 URL: {product['url']}")
        print("="*60)
        
        await test_product_detail(crawler, product['url'])

async def test_product_detail(crawler, url):
    """详细测试单个商品"""
    try:
        result = await crawler.get_product_detail(url)
        
        if result.get('code') == 200:
            data = result.get('data', {})
            skus = data.get('skus', [])
            
            print(f"✅ 商品获取成功")
            print(f"📦 商品标题: {data.get('name', '')}")
            print(f"💰 价格范围: {data.get('price', 'N/A')}")
            print(f"🖼️ 图片数量: {len(data.get('sliderPicUrls', []))}")
            
            print(f"\n📋 SKU信息分析:")
            print(f"   - SKU总数: {len(skus)}")
            
            if len(skus) > 0:
                print(f"✅ 找到SKU信息")
                for i, sku in enumerate(skus[:5]):  # 显示前5个SKU
                    properties = sku.get('properties', [])
                    prop_str = " | ".join([f"{p.get('name')}:{p.get('value')}" for p in properties])
                    price = sku.get('price', 0) / 100  # 转换为元
                    stock = sku.get('stock', 0)
                    print(f"   SKU {i+1}: {prop_str} - ¥{price:.2f} (库存:{stock})")
            else:
                print(f"⚠️ 没有找到SKU信息")
                
        else:
            print(f"❌ 获取失败: {result.get('message', '')}")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(test_1688_multi_sku())
