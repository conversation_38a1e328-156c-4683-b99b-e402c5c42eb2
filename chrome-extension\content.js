/**
 * 淘宝配置提取器 - 内容脚本
 * 在淘宝页面中运行，负责提取配置信息
 */

class TaobaoConfigExtractor {
    constructor() {
        this.config = {
            username: null,
            cookie: null,
            token: null,
            userAgent: navigator.userAgent,
            isLoggedIn: false,
            hasToken: false
        };
        
        this.init();
    }

    init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 页面加载完成后检查状态
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.checkPageStatus();
            });
        } else {
            this.checkPageStatus();
        }

        console.log('🕷️ 淘宝配置提取器已加载');
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'checkStatus':
                    const status = await this.checkPageStatus();
                    sendResponse({ success: true, data: status });
                    break;

                case 'extractConfig':
                    const config = await this.extractAllConfig();
                    sendResponse({ success: true, data: config });
                    break;

                default:
                    sendResponse({ success: false, error: '未知操作' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async checkPageStatus() {
        try {
            // 检查登录状态
            this.config.isLoggedIn = this.checkLoginStatus();
            
            // 检查用户名
            this.config.username = this.extractUsername();
            
            // 检查Token
            this.config.hasToken = this.checkTokenAvailability();
            
            console.log('页面状态检查完成:', this.config);
            return this.config;
            
        } catch (error) {
            console.error('检查页面状态失败:', error);
            throw error;
        }
    }

    checkLoginStatus() {
        // 方法1：检查页面中的登录相关元素
        const loginElements = [
            '.site-nav-login-info-nick',  // 用户昵称元素
            '.site-nav-user',             // 用户信息区域
            '[data-spm="754894437"]',     // 登录后的用户区域
            '.site-nav-menu-hd .site-nav-menu-hd-text' // 用户菜单
        ];

        for (const selector of loginElements) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim() && !element.textContent.includes('登录')) {
                return true;
            }
        }

        // 方法2：检查cookie中的登录标识
        const loginCookies = ['lgc', 'tracknick', '_nk_'];
        for (const cookieName of loginCookies) {
            if (this.getCookieValue(cookieName)) {
                return true;
            }
        }

        // 方法3：检查页面URL和内容
        if (document.cookie.includes('lgc=') && !document.cookie.includes('lgc=""')) {
            return true;
        }

        return false;
    }

    extractUsername() {
        // 方法1：从cookie中提取
        const lgc = this.getCookieValue('lgc');
        if (lgc) {
            return decodeURIComponent(lgc);
        }

        const tracknick = this.getCookieValue('tracknick');
        if (tracknick) {
            return decodeURIComponent(tracknick);
        }

        const nk = this.getCookieValue('_nk_');
        if (nk) {
            return decodeURIComponent(nk);
        }

        // 方法2：从页面元素中提取
        const userElements = [
            '.site-nav-login-info-nick',
            '.site-nav-user .site-nav-menu-hd-text',
            '[data-spm="754894437"] .site-nav-menu-hd-text'
        ];

        for (const selector of userElements) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                const text = element.textContent.trim();
                if (!text.includes('登录') && !text.includes('注册')) {
                    return text;
                }
            }
        }

        return null;
    }

    checkTokenAvailability() {
        // 检查cookie中的_m_h5_tk
        const h5tk = this.getCookieValue('_m_h5_tk');
        if (h5tk && h5tk.includes('_')) {
            return true;
        }

        // 检查localStorage中的token
        try {
            const localToken = localStorage.getItem('_m_h5_tk');
            if (localToken && localToken.includes('_')) {
                return true;
            }
        } catch (error) {
            console.warn('无法访问localStorage:', error);
        }

        return false;
    }

    async extractAllConfig() {
        try {
            // 通过background script获取完整的Cookie（包括HttpOnly）
            const response = await chrome.runtime.sendMessage({
                action: 'getAllCookies',
                domain: window.location.hostname
            });

            if (response && response.success) {
                this.config.cookie = response.cookie;
                console.log('🍪 通过Chrome API获取Cookie成功，长度:', this.config.cookie.length);
            } else {
                // 回退到document.cookie
                this.config.cookie = this.cleanupCookie(document.cookie);
                console.log('🍪 回退到document.cookie，长度:', this.config.cookie.length);
            }
            
            // 提取Token
            this.config.token = this.extractToken();
            
            // 更新状态
            this.config.isLoggedIn = this.checkLoginStatus();
            this.config.username = this.extractUsername();
            this.config.hasToken = !!this.config.token;
            
            // 添加时间戳
            this.config.extractedAt = new Date().toISOString();
            this.config.pageUrl = window.location.href;
            
            console.log('配置提取完成:', this.config);
            
            // 验证配置完整性
            if (!this.config.cookie) {
                throw new Error('未能获取到Cookie信息');
            }
            
            if (!this.config.token) {
                throw new Error('未能获取到Token信息，请确保已登录');
            }
            
            return this.config;
            
        } catch (error) {
            console.error('提取配置失败:', error);
            throw error;
        }
    }

    extractToken() {
        // 方法1：从cookie中的_m_h5_tk提取
        const h5tk = this.getCookieValue('_m_h5_tk');
        if (h5tk && h5tk.includes('_')) {
            const token = h5tk.split('_')[0];
            if (token && token.length > 10) {
                console.log('从cookie中提取到token:', token);
                return token;
            }
        }

        // 方法2：从localStorage提取
        try {
            const localToken = localStorage.getItem('_m_h5_tk');
            if (localToken && localToken.includes('_')) {
                const token = localToken.split('_')[0];
                if (token && token.length > 10) {
                    console.log('从localStorage中提取到token:', token);
                    return token;
                }
            }
        } catch (error) {
            console.warn('无法访问localStorage:', error);
        }

        // 方法3：从页面脚本中搜索
        const scriptToken = this.searchTokenInScripts();
        if (scriptToken) {
            console.log('从页面脚本中提取到token:', scriptToken);
            return scriptToken;
        }

        // 方法4：从全局变量中获取
        try {
            if (window._m_h5_tk && window._m_h5_tk.includes('_')) {
                const token = window._m_h5_tk.split('_')[0];
                if (token && token.length > 10) {
                    console.log('从全局变量中提取到token:', token);
                    return token;
                }
            }
        } catch (error) {
            console.warn('无法访问全局变量:', error);
        }

        console.warn('未能提取到有效的token');
        return null;
    }

    searchTokenInScripts() {
        try {
            const scripts = document.getElementsByTagName('script');
            const patterns = [
                /_m_h5_tk['"]\s*:\s*['"]([^'"]+)['"]/g,
                /_m_h5_tk['"]\s*=\s*['"]([^'"]+)['"]/g,
                /h5tk['"]\s*:\s*['"]([^'"]+)['"]/g
            ];

            for (let script of scripts) {
                const content = script.innerHTML;
                if (!content) continue;

                for (let pattern of patterns) {
                    const matches = content.matchAll(pattern);
                    for (let match of matches) {
                        if (match[1] && match[1].includes('_')) {
                            const token = match[1].split('_')[0];
                            if (token && token.length > 10) {
                                return token;
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('搜索脚本中的token失败:', error);
        }
        
        return null;
    }

    getCookieValue(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            return parts.pop().split(';').shift();
        }
        return null;
    }

    cleanupCookie(cookieString) {
        if (!cookieString) {
            return '';
        }

        console.log('🔧 原始Cookie长度:', cookieString.length);
        console.log('🔧 原始Cookie前200字符:', cookieString.substring(0, 200));

        // 分割Cookie字符串为键值对
        const cookiePairs = cookieString.split(';').map(pair => pair.trim());

        // 使用Map去重，保留最后出现的值
        const cookieMap = new Map();

        for (const pair of cookiePairs) {
            if (pair && pair.includes('=')) {
                const [key, ...valueParts] = pair.split('=');
                const value = valueParts.join('='); // 处理值中包含=的情况
                const cleanKey = key.trim();

                if (cleanKey) {
                    cookieMap.set(cleanKey, value);
                }
            }
        }

        // 重新组装Cookie字符串
        const cleanedCookie = Array.from(cookieMap.entries())
            .map(([key, value]) => `${key}=${value}`)
            .join('; ');

        console.log('🔧 清理后Cookie长度:', cleanedCookie.length);
        console.log('🔧 清理后Cookie前200字符:', cleanedCookie.substring(0, 200));
        console.log('🔧 去重的Cookie数量:', cookieMap.size);

        return cleanedCookie;
    }

    // 创建浮动提示
    showFloatingMessage(message, type = 'success') {
        const existingMessage = document.getElementById('taobao-extractor-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.id = 'taobao-extractor-message';
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999999;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            color: white;
            background-color: ${type === 'success' ? '#28a745' : '#dc3545'};
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        `;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.opacity = '0';
                setTimeout(() => {
                    messageDiv.remove();
                }, 300);
            }
        }, 3000);
    }
}

// 初始化提取器
const extractor = new TaobaoConfigExtractor();
