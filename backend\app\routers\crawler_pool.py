#!/usr/bin/env python3
"""
爬虫池管理API路由
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_
from pydantic import BaseModel
import logging

from ..database import get_db
from ..services.crawler_pool_service import CrawlerPoolService
from ..models import (
    CrawlerSession, CrawlerPoolSchedule, CrawlerRateLimit,
    CrawlerPerformanceMetrics, AntiCrawlDetection
)
from ..utils.response import success_response, error_response

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/crawler-pool", tags=["爬虫池管理"])

# Pydantic模型
class SessionInfo(BaseModel):
    session_id: str
    client_ip: str
    platform_code: str
    status: str
    request_count: int
    success_count: int
    error_count: int
    risk_score: float
    created_at: datetime
    expires_at: Optional[datetime]

class PoolStatus(BaseModel):
    total_crawlers: int
    available: int
    busy: int
    cooling: int
    blocked: int
    maintenance: int
    avg_health_score: float
    total_sessions: int
    total_requests_per_minute: int

class CrawlerScheduleInfo(BaseModel):
    account_id: int
    platform_code: str
    status: str
    priority_score: float
    load_factor: float
    current_sessions: int
    max_concurrent_sessions: int
    requests_per_minute: int
    max_requests_per_minute: int
    health_score: float
    last_used_at: Optional[datetime]

@router.get("/status")
async def get_pool_status(
    platform: Optional[str] = Query(None, description="平台代码"),
    db: Session = Depends(get_db)
):
    """获取爬虫池状态"""
    try:
        pool_service = CrawlerPoolService(db)
        status = pool_service.get_pool_status(platform)
        return success_response(status, "获取爬虫池状态成功")
        
    except Exception as e:
        return error_response(f"获取爬虫池状态失败: {str(e)}")

@router.get("/sessions")
async def get_active_sessions(
    platform: Optional[str] = Query(None, description="平台代码"),
    client_ip: Optional[str] = Query(None, description="客户端IP"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取活跃会话列表"""
    try:
        query = db.query(CrawlerSession).filter(CrawlerSession.status == 'active')
        
        if platform:
            query = query.filter(CrawlerSession.platform_code == platform)
        if client_ip:
            query = query.filter(CrawlerSession.client_ip == client_ip)
        
        # 获取总数
        total = query.count()
        
        # 分页
        offset = (page - 1) * size
        sessions = query.order_by(CrawlerSession.created_at.desc()).offset(offset).limit(size).all()
        
        return success_response({
            "items": sessions,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }, "获取会话列表成功")
        
    except Exception as e:
        return error_response(f"获取会话列表失败: {str(e)}")

@router.get("/schedules")
async def get_crawler_schedules(
    platform: Optional[str] = Query(None, description="平台代码"),
    status: Optional[str] = Query(None, description="调度状态"),
    db: Session = Depends(get_db)
):
    """获取爬虫调度信息"""
    try:
        query = db.query(CrawlerPoolSchedule)
        
        if platform:
            query = query.filter(CrawlerPoolSchedule.platform_code == platform)
        if status:
            query = query.filter(CrawlerPoolSchedule.status == status)
        
        schedules = query.order_by(CrawlerPoolSchedule.priority_score.desc()).all()
        
        return success_response(schedules, "获取调度信息成功")
        
    except Exception as e:
        return error_response(f"获取调度信息失败: {str(e)}")

@router.post("/schedules/{account_id}/update-status")
async def update_crawler_status(
    account_id: int,
    status: str,
    db: Session = Depends(get_db)
):
    """更新爬虫调度状态"""
    try:
        valid_statuses = ['available', 'busy', 'cooling', 'blocked', 'maintenance']
        if status not in valid_statuses:
            return error_response(f"无效的状态值，支持的状态: {valid_statuses}")
        
        schedule = db.query(CrawlerPoolSchedule).filter(
            CrawlerPoolSchedule.account_id == account_id
        ).first()
        
        if not schedule:
            return error_response("调度记录不存在")
        
        schedule.status = status
        schedule.updated_at = datetime.now()
        
        # 如果设置为维护状态，清空当前会话
        if status == 'maintenance':
            schedule.current_sessions = 0
            schedule.load_factor = 0.0
        
        db.commit()
        
        return success_response({
            "account_id": account_id,
            "status": status,
            "updated_at": schedule.updated_at
        }, f"爬虫状态已更新为: {status}")
        
    except Exception as e:
        db.rollback()
        return error_response(f"更新爬虫状态失败: {str(e)}")

@router.get("/rate-limits")
async def get_rate_limits(
    identifier_type: Optional[str] = Query(None, description="标识符类型"),
    platform: Optional[str] = Query(None, description="平台代码"),
    blocked_only: bool = Query(False, description="只显示被阻塞的"),
    db: Session = Depends(get_db)
):
    """获取频率限制记录"""
    try:
        query = db.query(CrawlerRateLimit)
        
        if identifier_type:
            query = query.filter(CrawlerRateLimit.identifier_type == identifier_type)
        if platform:
            query = query.filter(CrawlerRateLimit.platform_code == platform)
        if blocked_only:
            query = query.filter(CrawlerRateLimit.blocked_until > datetime.now())
        
        rate_limits = query.order_by(CrawlerRateLimit.updated_at.desc()).limit(100).all()
        
        return success_response(rate_limits, "获取频率限制记录成功")
        
    except Exception as e:
        return error_response(f"获取频率限制记录失败: {str(e)}")

@router.delete("/rate-limits/{rate_limit_id}")
async def remove_rate_limit(
    rate_limit_id: int,
    db: Session = Depends(get_db)
):
    """移除频率限制"""
    try:
        rate_limit = db.query(CrawlerRateLimit).filter(
            CrawlerRateLimit.id == rate_limit_id
        ).first()
        
        if not rate_limit:
            return error_response("频率限制记录不存在")
        
        db.delete(rate_limit)
        db.commit()
        
        return success_response({
            "rate_limit_id": rate_limit_id,
            "identifier": rate_limit.identifier,
            "identifier_type": rate_limit.identifier_type
        }, "频率限制已移除")
        
    except Exception as e:
        db.rollback()
        return error_response(f"移除频率限制失败: {str(e)}")

@router.get("/anti-crawl-detections")
async def get_anti_crawl_detections(
    platform: Optional[str] = Query(None, description="平台代码"),
    detection_type: Optional[str] = Query(None, description="检测类型"),
    severity: Optional[str] = Query(None, description="严重程度"),
    status: Optional[str] = Query(None, description="处理状态"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取反爬检测记录"""
    try:
        query = db.query(AntiCrawlDetection)
        
        if platform:
            query = query.filter(AntiCrawlDetection.platform_code == platform)
        if detection_type:
            query = query.filter(AntiCrawlDetection.detection_type == detection_type)
        if severity:
            query = query.filter(AntiCrawlDetection.severity == severity)
        if status:
            query = query.filter(AntiCrawlDetection.status == status)
        
        # 获取总数
        total = query.count()
        
        # 分页
        offset = (page - 1) * size
        detections = query.order_by(AntiCrawlDetection.created_at.desc()).offset(offset).limit(size).all()
        
        return success_response({
            "items": detections,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }, "获取反爬检测记录成功")
        
    except Exception as e:
        return error_response(f"获取反爬检测记录失败: {str(e)}")

@router.post("/cleanup")
async def cleanup_pool_data(
    cleanup_sessions: bool = Query(True, description="清理过期会话"),
    cleanup_rate_limits: bool = Query(False, description="清理过期频率限制"),
    cleanup_detections: bool = Query(False, description="清理旧检测记录"),
    days: int = Query(7, ge=1, le=30, description="保留天数"),
    db: Session = Depends(get_db)
):
    """清理爬虫池数据"""
    try:
        pool_service = CrawlerPoolService(db)
        cleanup_results = {}
        
        if cleanup_sessions:
            # 清理过期会话
            pool_service.cleanup_expired_sessions()
            cleanup_results["sessions"] = "已清理过期会话"
        
        if cleanup_rate_limits:
            # 清理过期的频率限制记录
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = db.query(CrawlerRateLimit).filter(
                CrawlerRateLimit.updated_at < cutoff_date
            ).delete()
            cleanup_results["rate_limits"] = f"已清理 {deleted_count} 条频率限制记录"
        
        if cleanup_detections:
            # 清理旧的检测记录
            cutoff_date = datetime.now() - timedelta(days=days)
            deleted_count = db.query(AntiCrawlDetection).filter(
                AntiCrawlDetection.created_at < cutoff_date
            ).delete()
            cleanup_results["detections"] = f"已清理 {deleted_count} 条检测记录"
        
        db.commit()
        
        return success_response(cleanup_results, "数据清理完成")
        
    except Exception as e:
        db.rollback()
        return error_response(f"数据清理失败: {str(e)}")

@router.get("/performance-metrics")
async def get_performance_metrics(
    account_id: Optional[int] = Query(None, description="账号ID"),
    platform: Optional[str] = Query(None, description="平台代码"),
    metric_type: Optional[str] = Query(None, description="指标类型"),
    time_window: Optional[str] = Query(None, description="时间窗口"),
    db: Session = Depends(get_db)
):
    """获取性能指标"""
    try:
        query = db.query(CrawlerPerformanceMetrics)
        
        if account_id:
            query = query.filter(CrawlerPerformanceMetrics.account_id == account_id)
        if platform:
            query = query.filter(CrawlerPerformanceMetrics.platform_code == platform)
        if metric_type:
            query = query.filter(CrawlerPerformanceMetrics.metric_type == metric_type)
        if time_window:
            query = query.filter(CrawlerPerformanceMetrics.time_window == time_window)
        
        metrics = query.order_by(CrawlerPerformanceMetrics.window_end_at.desc()).limit(100).all()
        
        return success_response(metrics, "获取性能指标成功")
        
    except Exception as e:
        return error_response(f"获取性能指标失败: {str(e)}")

@router.post("/reset-crawler/{account_id}")
async def reset_crawler_state(
    account_id: int,
    db: Session = Depends(get_db)
):
    """重置爬虫状态"""
    try:
        pool_service = CrawlerPoolService(db)

        # 🔧 修复：使用新的重置方法
        released_sessions = pool_service.reset_crawler_sessions(account_id)

        return success_response({
            "account_id": account_id,
            "released_sessions": released_sessions,
            "reset_at": datetime.now()
        }, f"爬虫状态已重置，释放了 {released_sessions} 个会话")

    except Exception as e:
        db.rollback()
        return error_response(f"重置爬虫状态失败: {str(e)}")

@router.post("/force-release-session/{session_id}")
async def force_release_session(
    session_id: str,
    db: Session = Depends(get_db)
):
    """强制释放指定会话"""
    try:
        pool_service = CrawlerPoolService(db)
        success = pool_service.force_release_session(session_id)

        if success:
            return success_response({
                "session_id": session_id,
                "released_at": datetime.now()
            }, "会话已强制释放")
        else:
            return error_response("会话不存在或已释放")

    except Exception as e:
        db.rollback()
        return error_response(f"强制释放会话失败: {str(e)}")

@router.post("/fix-session-counts")
async def fix_session_counts(
    db: Session = Depends(get_db)
):
    """修复会话计数不一致问题"""
    try:
        # 获取所有调度记录
        schedules = db.query(CrawlerPoolSchedule).all()
        fixed_count = 0

        for schedule in schedules:
            # 计算实际的活跃会话数
            actual_sessions = db.query(CrawlerSession).filter(
                and_(
                    CrawlerSession.account_id == schedule.account_id,
                    CrawlerSession.platform_code == schedule.platform_code,
                    CrawlerSession.status == 'active',
                    CrawlerSession.expires_at > datetime.now()
                )
            ).count()

            # 如果计数不一致，修复它
            if schedule.current_sessions != actual_sessions:
                old_count = schedule.current_sessions
                schedule.current_sessions = actual_sessions
                schedule.load_factor = min(actual_sessions / schedule.max_concurrent_sessions, 1.0)

                # 更新状态
                if actual_sessions == 0:
                    schedule.status = 'available'
                elif actual_sessions > 0 and schedule.status == 'available':
                    schedule.status = 'busy'

                fixed_count += 1
                logger.info(f"修复会话计数: account_id={schedule.account_id}, "
                           f"platform={schedule.platform_code}, "
                           f"old_count={old_count}, new_count={actual_sessions}")

        db.commit()

        return success_response({
            "fixed_schedules": fixed_count,
            "fixed_at": datetime.now()
        }, f"已修复 {fixed_count} 个调度记录的会话计数")

    except Exception as e:
        db.rollback()
        return error_response(f"修复会话计数失败: {str(e)}")
