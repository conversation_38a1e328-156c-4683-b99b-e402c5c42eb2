#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试1688商品详情API接口
测试商品: https://detail.1688.com/offer/591940079968.html
验证SKU价格和重量是否正确返回给上游系统
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['ENVIRONMENT'] = 'development'

# 导入必要的模块
import asyncio
from app.database import test_database_connection, redis_client
from app.crawler.platforms.alibaba_1688 import Alibaba1688Crawler

async def test_1688_product_detail_api():
    """测试1688商品详情API接口"""
    print("🔍 开始测试1688商品详情API接口...")

    # 测试数据库连接
    if test_database_connection() and redis_client:
        print("✅ 数据库和Redis连接成功")
    else:
        print("⚠️ 数据库或Redis连接失败，但继续测试")
    
    # 创建1688爬虫实例
    crawler = Alibaba1688Crawler()
    print("✅ 成功创建1688爬虫实例")
    
    # 测试商品ID和URL
    product_id = "591940079968"
    product_url = f"https://detail.1688.com/offer/{product_id}.html"
    print(f"🎯 测试商品ID: {product_id}")
    print(f"🔗 商品链接: {product_url}")

    try:
        # 调用商品详情API
        print("\n📡 调用商品详情API...")
        result = await crawler.get_product_detail(product_url)
        
        if not result:
            print("❌ API返回空结果")
            return
            
        print("✅ API调用成功!")
        
        # 检查基本信息
        print(f"\n📋 基本信息:")
        print(f"   商品ID: {result.get('id')}")
        print(f"   商品名称: {result.get('name', 'N/A')}")
        print(f"   价格范围: {result.get('price', 0)/100:.2f} - {result.get('marketPrice', 0)/100:.2f} 元")
        print(f"   SKU数量: {len(result.get('skus', []))}")
        print(f"   销量: {result.get('salesCount', 0)}")
        
        # 重点检查SKU信息
        skus = result.get('skus', [])
        if not skus:
            print("❌ 没有找到SKU信息")
            return
            
        print(f"\n📦 SKU详细检查 (共{len(skus)}个):")
        
        # 检查前5个SKU的详细信息
        for i, sku in enumerate(skus[:5]):
            print(f"\n  SKU {i+1}:")
            print(f"    ID: {sku.get('id')}")
            print(f"    SKU ID: {sku.get('skuId')}")
            print(f"    💰 价格: {sku.get('price')} 分 ({sku.get('price', 0)/100:.2f} 元)")
            print(f"    💰 市场价: {sku.get('marketPrice')} 分 ({sku.get('marketPrice', 0)/100:.2f} 元)")
            print(f"    ⚖️ 重量: {sku.get('weight')} 克")
            print(f"    📦 库存: {sku.get('stock')}")
            print(f"    🖼️ 图片: {sku.get('picUrl', 'N/A')}")
            
            # 检查属性
            properties = sku.get('properties', [])
            print(f"    📋 属性 ({len(properties)}个):")
            for prop in properties:
                print(f"      - {prop.get('propertyName')} = {prop.get('valueName')} (ID: {prop.get('propertyId')}, ValueID: {prop.get('valueId')})")
        
        # 统计分析
        print(f"\n📊 SKU统计分析:")
        
        # 价格统计
        prices = [sku.get('price', 0) for sku in skus]
        zero_price_count = len([p for p in prices if p == 0])
        valid_price_count = len([p for p in prices if p > 0])
        
        print(f"   💰 价格统计:")
        print(f"     - 有效价格SKU: {valid_price_count}/{len(skus)}")
        print(f"     - 零价格SKU: {zero_price_count}/{len(skus)}")
        if valid_price_count > 0:
            print(f"     - 价格范围: {min([p for p in prices if p > 0])/100:.2f} - {max(prices)/100:.2f} 元")
        
        # 重量统计
        weights = [sku.get('weight', 0) for sku in skus]
        zero_weight_count = len([w for w in weights if w == 0])
        valid_weight_count = len([w for w in weights if w > 0])
        
        print(f"   ⚖️ 重量统计:")
        print(f"     - 有效重量SKU: {valid_weight_count}/{len(skus)}")
        print(f"     - 零重量SKU: {zero_weight_count}/{len(skus)}")
        if valid_weight_count > 0:
            unique_weights = list(set([w for w in weights if w > 0]))
            print(f"     - 重量值: {unique_weights} 克")
        
        # 图片统计
        pic_urls = [sku.get('picUrl', '') for sku in skus]
        valid_pic_count = len([url for url in pic_urls if url and url != 'N/A'])
        
        print(f"   🖼️ 图片统计:")
        print(f"     - 有图片SKU: {valid_pic_count}/{len(skus)}")
        
        # 最终验证结果
        print(f"\n🎯 最终验证结果:")
        if zero_price_count == 0:
            print("   ✅ 所有SKU都有正确的价格信息")
        else:
            print(f"   ❌ 发现 {zero_price_count} 个SKU缺少价格信息")
            
        if zero_weight_count == 0:
            print("   ✅ 所有SKU都有正确的重量信息")
        else:
            print(f"   ❌ 发现 {zero_weight_count} 个SKU缺少重量信息")
            
        if valid_pic_count > 0:
            print("   ✅ SKU图片信息正常")
        else:
            print("   ⚠️ 所有SKU都没有图片信息")
        
        # 保存完整结果到文件
        output_file = "temp/1688_api_test_result.json"
        os.makedirs("temp", exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 完整结果已保存到: {output_file}")
        
        print("\n✅ 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_1688_product_detail_api())
