"""
代理池管理API路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import Optional
from datetime import datetime, timezone
from pydantic import BaseModel

from app.database import get_db
from app.models import ProxyPool, ProxyStatus, ProxySource, ProxyTestLog
from app.utils.response import success_response, error_response
from app.services.alert_service import alert_service, AlertLevel

router = APIRouter(prefix="/api/v1/proxies", tags=["代理管理"])

# Pydantic模型
class ProxyCreate(BaseModel):
    provider_name: str
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    proxy_type: str
    proxy_source: Optional[str] = "static"
    api_config: Optional[dict] = None
    test_url: Optional[str] = "https://httpbin.org/ip"
    country: Optional[str] = None
    region: Optional[str] = None
    priority: Optional[int] = 1
    is_enabled: Optional[bool] = True

class ProxyUpdate(BaseModel):
    provider_name: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    proxy_type: Optional[str] = None
    proxy_source: Optional[str] = None
    api_config: Optional[dict] = None
    test_url: Optional[str] = None
    country: Optional[str] = None
    region: Optional[str] = None
    status: Optional[str] = None
    priority: Optional[int] = None
    is_enabled: Optional[bool] = None

class ProxyResponse(BaseModel):
    id: int
    provider_name: str
    host: Optional[str]
    port: Optional[int]
    username: Optional[str]
    # password字段不返回，保护敏感信息
    proxy_type: str
    proxy_source: str
    api_config: Optional[dict]
    test_url: str
    country: Optional[str]
    region: Optional[str]
    status: str
    success_rate: float
    avg_response_time: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    consecutive_failures: int
    priority: int
    is_enabled: bool
    created_at: datetime
    updated_at: datetime
    last_checked_at: Optional[datetime]
    last_test_at: Optional[datetime]
    last_success_at: Optional[datetime]

    class Config:
        from_attributes = True

@router.get("/")
async def get_proxies(
    db: Session = Depends(get_db),
    status: Optional[str] = Query(None, description="状态筛选"),
    proxy_type: Optional[str] = Query(None, description="类型筛选"),
    enabled_only: bool = Query(False, description="仅显示启用的代理"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """获取代理列表"""
    try:
        query = db.query(ProxyPool)
        
        # 筛选条件
        if status:
            query = query.filter(ProxyPool.status == status)
        if proxy_type:
            query = query.filter(ProxyPool.proxy_type == proxy_type)
        if enabled_only:
            query = query.filter(ProxyPool.is_enabled == True)
        
        # 分页
        offset = (page - 1) * size
        proxies = query.order_by(desc(ProxyPool.priority), desc(ProxyPool.created_at)).offset(offset).limit(size).all()

        return success_response(proxies, "获取代理列表成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取代理列表失败: {str(e)}")

@router.post("/", response_model=ProxyResponse)
async def create_proxy(proxy: ProxyCreate, db: Session = Depends(get_db)):
    """创建新代理"""
    try:
        # 验证代理配置
        if proxy.proxy_source == "static" and (not proxy.host or not proxy.port):
            raise HTTPException(status_code=400, detail="静态代理必须提供host和port")

        # 检查是否已存在相同的代理
        existing_query = db.query(ProxyPool).filter(
            ProxyPool.provider_name == proxy.provider_name
        )

        if proxy.host and proxy.port:
            existing_query = existing_query.filter(
                ProxyPool.host == proxy.host,
                ProxyPool.port == proxy.port
            )

        existing = existing_query.first()
        if existing:
            raise HTTPException(status_code=400, detail="相同的代理配置已存在")

        # 处理密码加密（如果有密码）
        encrypted_password = None
        if proxy.password:
            from app.utils.crypto import encrypt_password
            encrypted_password = encrypt_password(proxy.password)

        # 处理API配置
        api_config_str = None
        if proxy.api_config:
            import json
            api_config_str = json.dumps(proxy.api_config)

        # 创建代理
        db_proxy = ProxyPool(
            provider_name=proxy.provider_name,
            host=proxy.host,
            port=proxy.port,
            username=proxy.username,
            password=encrypted_password,
            proxy_type=proxy.proxy_type,
            proxy_source=ProxySource(proxy.proxy_source),
            api_config=api_config_str,
            test_url=proxy.test_url,
            country=proxy.country,
            region=proxy.region,
            status=ProxyStatus.TESTING,
            priority=proxy.priority,
            is_enabled=proxy.is_enabled,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

        db.add(db_proxy)
        db.commit()
        db.refresh(db_proxy)

        return db_proxy
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建代理失败: {str(e)}")

@router.get("/{proxy_id}", response_model=ProxyResponse)
async def get_proxy(proxy_id: int, db: Session = Depends(get_db)):
    """获取单个代理详情"""
    proxy = db.query(ProxyPool).filter(ProxyPool.id == proxy_id).first()
    if not proxy:
        raise HTTPException(status_code=404, detail="代理不存在")
    return proxy

@router.put("/{proxy_id}", response_model=ProxyResponse)
async def update_proxy(proxy_id: int, proxy_update: ProxyUpdate, db: Session = Depends(get_db)):
    """更新代理信息"""
    try:
        proxy = db.query(ProxyPool).filter(ProxyPool.id == proxy_id).first()
        if not proxy:
            raise HTTPException(status_code=404, detail="代理不存在")
        
        # 更新字段
        update_data = proxy_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if field == "status" and value:
                setattr(proxy, field, ProxyStatus(value))
            elif value is not None:
                setattr(proxy, field, value)
        
        proxy.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(proxy)
        
        return proxy
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新代理失败: {str(e)}")


@router.post("/{proxy_id}/test")
async def test_proxy(proxy_id: int, db: Session = Depends(get_db)):
    """测试单个代理"""
    try:
        print(f"[PROXY_TEST] 开始测试代理 ID: {proxy_id}")

        proxy = db.query(ProxyPool).filter(ProxyPool.id == proxy_id).first()
        if not proxy:
            print(f"[PROXY_TEST] 代理不存在: {proxy_id}")
            raise HTTPException(status_code=404, detail="代理不存在")

        print(f"[PROXY_TEST] 找到代理: {proxy.provider_name} ({proxy.host}:{proxy.port})")
        print(f"[PROXY_TEST] 代理来源: {proxy.proxy_source}")
        print(f"[PROXY_TEST] 当前状态: {proxy.status}")

        # 更新代理状态为测试中
        proxy.status = ProxyStatus.TESTING
        proxy.last_test_at = datetime.now(timezone.utc)
        db.commit()
        print(f"[PROXY_TEST] 状态已更新为测试中")

        # 执行代理测试
        print(f"[PROXY_TEST] 开始执行连通性测试...")
        test_result = await _test_proxy_connectivity(proxy, db)
        print(f"[PROXY_TEST] 测试完成，结果: {test_result}")

        return success_response(test_result, "代理测试完成")

    except HTTPException:
        raise
    except Exception as e:
        print(f"[PROXY_TEST] 测试异常: {str(e)}")
        import traceback
        print(f"[PROXY_TEST] 详细错误:\n{traceback.format_exc()}")
        return error_response(f"测试代理失败: {str(e)}")


@router.post("/batch-test")
async def batch_test_proxies(db: Session = Depends(get_db)):
    """批量测试代理"""
    try:
        # 获取所有启用的代理
        proxies = db.query(ProxyPool).filter(
            ProxyPool.is_enabled == True
        ).all()

        if not proxies:
            return success_response({"tested_count": 0}, "没有可测试的代理")

        tested_count = 0
        success_count = 0

        for proxy in proxies:
            try:
                # 更新测试时间
                proxy.last_test_at = datetime.now(timezone.utc)
                proxy.status = ProxyStatus.TESTING
                db.commit()

                # 执行测试
                test_result = await _test_proxy_connectivity(proxy, db)
                tested_count += 1

                if test_result.get("success"):
                    success_count += 1

            except Exception as e:
                print(f"测试代理 {proxy.id} 失败: {e}")
                tested_count += 1

        return success_response({
            "tested_count": tested_count,
            "success_count": success_count,
            "failed_count": tested_count - success_count
        }, "批量测试完成")

    except Exception as e:
        return error_response(f"批量测试失败: {str(e)}")


async def _test_proxy_connectivity(proxy: ProxyPool, db: Session):
    """测试代理连通性"""
    import asyncio
    import aiohttp
    import time
    import json

    print(f"[PROXY_CONNECTIVITY] 开始测试代理连通性")
    print(f"[PROXY_CONNECTIVITY] 代理信息: {proxy.host}:{proxy.port}")
    print(f"[PROXY_CONNECTIVITY] 代理来源: {proxy.proxy_source}")

    start_time = time.time()
    test_result = {
        "success": False,
        "response_time_ms": 0,
        "error_message": None,
        "test_ip": None
    }

    try:
        # 构建代理URL
        print(f"[PROXY_CONNECTIVITY] 构建代理URL...")
        if proxy.proxy_source == ProxySource.STATIC or proxy.proxy_source == ProxySource.SELF_HOSTED:
            if not proxy.host or not proxy.port:
                raise Exception("代理缺少host或port配置")

            proxy_url = f"http://{proxy.host}:{proxy.port}"
            print(f"[PROXY_CONNECTIVITY] 基础代理URL: {proxy_url}")

            if proxy.username and proxy.password:
                print(f"[PROXY_CONNECTIVITY] 检测到代理认证信息")
                from app.utils.crypto import decrypt_password
                decrypted_password = decrypt_password(proxy.password)
                if decrypted_password and decrypted_password.strip():
                    proxy_url = f"http://{proxy.username}:{decrypted_password}@{proxy.host}:{proxy.port}"
                    print(f"[PROXY_CONNECTIVITY] 已添加认证信息")
                else:
                    print(f"[PROXY_CONNECTIVITY] 密码解密失败或为空")
            else:
                print(f"[PROXY_CONNECTIVITY] 无需认证")
        else:
            # TODO: 处理第三方API代理
            raise Exception("暂不支持第三方API代理测试")

        # 设置超时
        print(f"[PROXY_CONNECTIVITY] 设置30秒超时")
        timeout = aiohttp.ClientTimeout(total=30)

        # 尝试多个测试URL（使用HTTP，因为很多代理不支持HTTPS）
        test_urls = [
            'http://httpbin.org/ip',
            'http://ip-api.com/json',
            'http://icanhazip.com',
            'http://ifconfig.me/ip',
            proxy.test_url or 'http://httpbin.org/ip'
        ]

        success = False
        for test_url in test_urls:
            try:
                print(f"[PROXY_CONNECTIVITY] 尝试请求: {test_url}")
                print(f"[PROXY_CONNECTIVITY] 使用代理: {proxy_url}")

                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(
                        test_url,
                        proxy=proxy_url,
                        headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    ) as response:
                        response_time_ms = int((time.time() - start_time) * 1000)
                        print(f"[PROXY_CONNECTIVITY] 收到响应，状态码: {response.status}")
                        print(f"[PROXY_CONNECTIVITY] 响应时间: {response_time_ms}ms")

                        if response.status == 200:
                            response_text = await response.text()
                            print(f"[PROXY_CONNECTIVITY] 响应内容: {response_text[:200]}...")

                            # 尝试解析IP地址
                            try:
                                if test_url.endswith('/json'):
                                    response_data = json.loads(response_text)
                                    test_ip = response_data.get('query', response_data.get('origin', 'unknown'))
                                elif 'httpbin.org' in test_url:
                                    response_data = json.loads(response_text)
                                    test_ip = response_data.get('origin', 'unknown')
                                else:
                                    test_ip = response_text.strip()
                                print(f"[PROXY_CONNECTIVITY] 解析到IP: {test_ip}")
                            except Exception as parse_error:
                                print(f"[PROXY_CONNECTIVITY] IP解析失败: {parse_error}")
                                test_ip = 'unknown'

                            test_result.update({
                                "success": True,
                                "response_time_ms": response_time_ms,
                                "test_ip": test_ip
                            })

                            print(f"[PROXY_CONNECTIVITY] 测试成功，更新代理状态")
                            # 更新代理状态
                            proxy.status = ProxyStatus.ACTIVE
                            proxy.avg_response_time = response_time_ms
                            proxy.last_success_at = datetime.now(timezone.utc)
                            proxy.consecutive_failures = 0

                            # 更新成功率
                            proxy.successful_requests += 1
                            proxy.total_requests += 1
                            if proxy.total_requests > 0:
                                proxy.success_rate = (proxy.successful_requests / proxy.total_requests) * 100

                            print(f"[PROXY_CONNECTIVITY] 代理状态已更新为ACTIVE")
                            success = True
                            break  # 成功后跳出循环

                        else:
                            print(f"[PROXY_CONNECTIVITY] HTTP错误 {response.status}，尝试下一个URL")
                            continue

            except Exception as url_error:
                print(f"[PROXY_CONNECTIVITY] URL {test_url} 测试失败: {url_error}")
                continue

        # 如果所有URL都失败了
        if not success:

            # 如果所有URL都失败，设置失败状态
            response_time_ms = int((time.time() - start_time) * 1000)
            test_result.update({
                "error_message": "所有测试URL都失败",
                "response_time_ms": response_time_ms
            })
            _handle_proxy_failure(proxy, "所有测试URL都失败")

    except asyncio.TimeoutError:
        response_time_ms = int((time.time() - start_time) * 1000)
        print(f"[PROXY_CONNECTIVITY] 连接超时 ({response_time_ms}ms)")
        test_result.update({
            "error_message": "连接超时",
            "response_time_ms": response_time_ms
        })
        _handle_proxy_failure(proxy, "连接超时")

    except Exception as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        print(f"[PROXY_CONNECTIVITY] 连接异常: {str(e)} ({response_time_ms}ms)")
        import traceback
        print(f"[PROXY_CONNECTIVITY] 详细错误:\n{traceback.format_exc()}")
        test_result.update({
            "error_message": str(e),
            "response_time_ms": response_time_ms
        })
        _handle_proxy_failure(proxy, str(e))

    # 记录测试日志
    test_log = ProxyTestLog(
        proxy_id=proxy.id,
        test_result="success" if test_result["success"] else "failed",
        response_time_ms=test_result["response_time_ms"],
        error_message=test_result["error_message"],
        test_ip=test_result["test_ip"],
        test_url=proxy.test_url,
        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    )

    db.add(test_log)
    db.commit()

    return test_result


def _handle_proxy_failure(proxy: ProxyPool, error_message: str):
    """处理代理失败"""
    proxy.consecutive_failures += 1
    proxy.failed_requests += 1
    proxy.total_requests += 1

    # 连续失败3次以上标记为错误状态
    if proxy.consecutive_failures >= 3:
        proxy.status = ProxyStatus.ERROR
        # 发送代理失败告警
        import asyncio
        asyncio.create_task(_send_proxy_failure_alert(proxy, error_message))
    else:
        proxy.status = ProxyStatus.INACTIVE

    # 更新成功率
    if proxy.total_requests > 0:
        proxy.success_rate = (proxy.successful_requests / proxy.total_requests) * 100


@router.delete("/{proxy_id}")
async def delete_proxy(proxy_id: int, db: Session = Depends(get_db)):
    """删除代理"""
    try:
        proxy = db.query(ProxyPool).filter(ProxyPool.id == proxy_id).first()
        if not proxy:
            raise HTTPException(status_code=404, detail="代理不存在")

        # 删除相关的测试日志
        db.query(ProxyTestLog).filter(ProxyTestLog.proxy_id == proxy_id).delete()

        # 删除代理
        db.delete(proxy)
        db.commit()

        return success_response(None, "代理删除成功")

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return error_response(f"删除代理失败: {str(e)}")


@router.post("/{proxy_id}/toggle")
async def toggle_proxy(proxy_id: int, db: Session = Depends(get_db)):
    """切换代理启用状态"""
    try:
        proxy = db.query(ProxyPool).filter(ProxyPool.id == proxy_id).first()
        if not proxy:
            raise HTTPException(status_code=404, detail="代理不存在")

        proxy.is_enabled = not proxy.is_enabled
        proxy.updated_at = datetime.now(timezone.utc)
        db.commit()

        status_text = "启用" if proxy.is_enabled else "禁用"
        return success_response(None, f"代理已{status_text}")

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return error_response(f"切换代理状态失败: {str(e)}")


@router.get("/{proxy_id}/test-logs")
async def get_proxy_test_logs(
    proxy_id: int,
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100)
):
    """获取代理测试日志"""
    try:
        proxy = db.query(ProxyPool).filter(ProxyPool.id == proxy_id).first()
        if not proxy:
            raise HTTPException(status_code=404, detail="代理不存在")

        # 查询测试日志
        query = db.query(ProxyTestLog).filter(
            ProxyTestLog.proxy_id == proxy_id
        ).order_by(desc(ProxyTestLog.test_time))

        # 分页
        offset = (page - 1) * size
        logs = query.offset(offset).limit(size).all()
        total = query.count()

        # 转换日志对象为字典
        logs_data = []
        for log in logs:
            logs_data.append({
                "id": log.id,
                "proxy_id": log.proxy_id,
                "test_time": log.test_time.isoformat() if log.test_time else None,
                "test_result": log.test_result,
                "response_time_ms": log.response_time_ms,
                "error_message": log.error_message,
                "test_ip": log.test_ip,
                "test_url": log.test_url,
                "user_agent": log.user_agent,
                "created_at": log.created_at.isoformat() if log.created_at else None
            })

        return success_response({
            "logs": logs_data,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size
        }, "获取测试日志成功")

    except HTTPException:
        raise
    except Exception as e:
        return error_response(f"获取测试日志失败: {str(e)}")



@router.get("/stats/summary")
async def get_proxies_stats(db: Session = Depends(get_db)):
    """获取代理统计信息"""
    try:
        # 总代理数
        total_proxies = db.query(func.count(ProxyPool.id)).scalar() or 0
        
        # 活跃代理数
        active_proxies = db.query(func.count(ProxyPool.id)).filter(
            ProxyPool.status == ProxyStatus.ACTIVE,
            ProxyPool.is_enabled == True
        ).scalar() or 0
        
        # 按类型统计
        type_stats = db.query(
            ProxyPool.proxy_type,
            func.count(ProxyPool.id).label('count')
        ).group_by(ProxyPool.proxy_type).all()
        
        # 按状态统计
        status_stats = db.query(
            ProxyPool.status,
            func.count(ProxyPool.id).label('count')
        ).group_by(ProxyPool.status).all()
        
        # 平均成功率
        avg_success_rate = db.query(func.avg(ProxyPool.success_rate)).scalar() or 0
        
        # 平均响应时间
        avg_response_time = db.query(func.avg(ProxyPool.avg_response_time)).scalar() or 0
        
        return {
            "total_proxies": total_proxies,
            "active_proxies": active_proxies,
            "avg_success_rate": round(float(avg_success_rate), 2),
            "avg_response_time": round(float(avg_response_time), 0),
            "type_distribution": [
                {"type": stat.proxy_type, "count": stat.count} 
                for stat in type_stats
            ],
            "status_distribution": [
                {"status": stat.status.value, "count": stat.count} 
                for stat in status_stats
            ]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


async def _send_proxy_failure_alert(proxy: ProxyPool, error_message: str):
    """发送代理失败告警"""
    try:
        proxy_info = [{
            'host': proxy.host or 'N/A',
            'port': proxy.port or 0,
            'status': proxy.status.value if proxy.status else 'unknown',
            'response_time': proxy.avg_response_time or 0,
            'last_check': proxy.last_checked_at.strftime('%Y-%m-%d %H:%M:%S') if proxy.last_checked_at else 'N/A'
        }]

        statistics = [
            {'name': '连续失败次数', 'value': str(proxy.consecutive_failures), 'unit': '次'},
            {'name': '总失败次数', 'value': str(proxy.failed_requests), 'unit': '次'},
            {'name': '成功率', 'value': f"{proxy.success_rate:.1f}", 'unit': '%'},
            {'name': '平均响应时间', 'value': str(proxy.avg_response_time or 0), 'unit': 'ms'}
        ]

        await alert_service.send_proxy_alert(
            title=f"代理服务器连续失败告警",
            message=f"代理服务器 {proxy.host}:{proxy.port} 连续失败{proxy.consecutive_failures}次，已标记为错误状态。错误信息：{error_message}",
            level=AlertLevel.ERROR,
            proxy_info=proxy_info,
            statistics=statistics
        )

    except Exception as e:
        print(f"发送代理失败告警失败: {e}")


async def _send_no_proxy_alert():
    """发送无可用代理告警"""
    try:
        await alert_service.send_proxy_alert(
            title="无可用代理服务器",
            message="系统检测到当前没有可用的代理服务器，这可能影响爬虫的正常工作。",
            level=AlertLevel.ERROR,
            proxy_info=[],
            statistics=[
                {'name': '可用代理数', 'value': '0', 'unit': '个'},
                {'name': '总代理数', 'value': '未知', 'unit': '个'}
            ]
        )

    except Exception as e:
        print(f"发送无可用代理告警失败: {e}")






