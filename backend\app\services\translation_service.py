#!/usr/bin/env python3
"""
翻译服务 - 支持多种翻译引擎
"""

import asyncio
import aiohttp
import hashlib
import json
import logging
from typing import Dict, Optional, List
from datetime import datetime, timedelta
import os
from urllib.parse import quote

from app.services.cache_service import cache_service

logger = logging.getLogger(__name__)

class TranslationService:
    """翻译服务"""
    
    def __init__(self):
        self.engines = {
            "google": self._google_translate,
            "baidu": self._baidu_translate,
            "youdao": self._youdao_translate,
            "free": self._free_translate
        }
        
        # 默认使用免费翻译引擎
        self.default_engine = "free"
        
        # 语言代码映射
        self.language_map = {
            "zh": {"google": "zh", "baidu": "zh", "youdao": "zh", "free": "zh"},
            "en": {"google": "en", "baidu": "en", "youdao": "en", "free": "en"},
            "fr": {"google": "fr", "baidu": "fra", "youdao": "fr", "free": "fr"},
            "de": {"google": "de", "baidu": "de", "youdao": "de", "free": "de"},
            "es": {"google": "es", "baidu": "spa", "youdao": "es", "free": "es"},
            "it": {"google": "it", "baidu": "it", "youdao": "it", "free": "it"},
            "ja": {"google": "ja", "baidu": "jp", "youdao": "ja", "free": "ja"},
            "ko": {"google": "ko", "baidu": "kor", "youdao": "ko", "free": "ko"}
        }
    
    async def translate(self, text: str, source_lang: str, target_lang: str, engine: str = None) -> str:
        """
        翻译文本
        
        Args:
            text: 要翻译的文本
            source_lang: 源语言
            target_lang: 目标语言
            engine: 翻译引擎 (google/baidu/youdao/free)
        
        Returns:
            翻译后的文本
        """
        if not text or not text.strip():
            return text
        
        if source_lang == target_lang:
            return text
        
        # 生成缓存键
        cache_key = self._generate_cache_key(text, source_lang, target_lang)
        
        # 检查缓存
        cached_result = await cache_service.get(cache_key)
        if cached_result:
            logger.debug(f"翻译缓存命中: {text[:50]}...")
            return cached_result
        
        # 选择翻译引擎
        engine = engine or self.default_engine
        if engine not in self.engines:
            engine = self.default_engine
        
        try:
            # 执行翻译
            translated_text = await self.engines[engine](text, source_lang, target_lang)
            
            # 验证并缓存结果 (只缓存有效翻译)
            if self._is_valid_translation(text, translated_text):
                import os
                cache_ttl = int(os.getenv("CACHE_TRANSLATION_TTL", "3600"))
                await cache_service.set(cache_key, translated_text, expire=cache_ttl)
                logger.debug(f"✅ 翻译结果已缓存: {text[:20]}... -> {translated_text[:20]}...")
            else:
                logger.warning(f"⚠️ 翻译结果无效，跳过缓存: '{text}' -> '{translated_text}'")
            
            logger.info(f"翻译成功 [{engine}]: {text[:30]}... -> {translated_text[:30]}...")
            return translated_text
            
        except Exception as e:
            logger.error(f"翻译失败 [{engine}]: {str(e)}")
            
            # 尝试备用引擎
            if engine != "free":
                logger.info("尝试使用备用翻译引擎...")
                return await self.translate(text, source_lang, target_lang, "free")
            
            # 所有引擎都失败，返回原文
            return text
    
    async def translate_batch(self, texts: List[str], source_lang: str, target_lang: str, engine: str = None) -> List[str]:
        """
        批量翻译
        
        Args:
            texts: 要翻译的文本列表
            source_lang: 源语言
            target_lang: 目标语言
            engine: 翻译引擎
        
        Returns:
            翻译后的文本列表
        """
        if not texts:
            return []
        
        # 并发翻译
        tasks = [
            self.translate(text, source_lang, target_lang, engine)
            for text in texts
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        translated_texts = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"批量翻译第{i}项失败: {str(result)}")
                translated_texts.append(texts[i])  # 返回原文
            else:
                translated_texts.append(result)
        
        return translated_texts
    
    def _generate_cache_key(self, text: str, source_lang: str, target_lang: str) -> str:
        """生成缓存键"""
        key_data = f"translate:{source_lang}:{target_lang}:{text}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _is_valid_translation(self, original_text: str, translated_text: str) -> bool:
        """验证翻译结果是否有效，值得缓存"""
        if not translated_text or not isinstance(translated_text, str):
            return False

        # 翻译结果不能为空或只有空白字符
        if not translated_text.strip():
            return False

        # 翻译结果长度不能太短（除非原文本身就很短）
        if len(translated_text.strip()) < 2 and len(original_text.strip()) >= 2:
            return False

        # 翻译结果不能与原文完全相同（除非是同语言翻译）
        if translated_text.strip() == original_text.strip() and len(original_text.strip()) > 10:
            return False

        # 翻译结果不能包含明显的错误标识
        error_indicators = ["error", "failed", "错误", "失败", "无法翻译"]
        translated_lower = translated_text.lower()
        for indicator in error_indicators:
            if indicator in translated_lower:
                return False

        return True
    
    async def _free_translate(self, text: str, source_lang: str, target_lang: str) -> str:
        """免费翻译引擎 (使用Google Translate免费API)"""
        try:
            # 获取语言代码
            source_code = self.language_map.get(source_lang, {}).get("free", source_lang)
            target_code = self.language_map.get(target_lang, {}).get("free", target_lang)
            
            # 使用Google Translate的免费接口
            url = "https://translate.googleapis.com/translate_a/single"
            params = {
                "client": "gtx",
                "sl": source_code,
                "tl": target_code,
                "dt": "t",
                "q": text
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result and len(result) > 0 and len(result[0]) > 0:
                            translated_text = "".join([item[0] for item in result[0] if item[0]])
                            return translated_text
                    
                    raise Exception(f"翻译请求失败: {response.status}")
        
        except Exception as e:
            logger.error(f"免费翻译失败: {str(e)}")
            raise
    
    async def _google_translate(self, text: str, source_lang: str, target_lang: str) -> str:
        """Google翻译 (需要API密钥)"""
        api_key = os.getenv("GOOGLE_TRANSLATE_API_KEY")
        if not api_key:
            raise Exception("Google翻译API密钥未配置")
        
        try:
            source_code = self.language_map.get(source_lang, {}).get("google", source_lang)
            target_code = self.language_map.get(target_lang, {}).get("google", target_lang)
            
            url = "https://translation.googleapis.com/language/translate/v2"
            data = {
                "key": api_key,
                "q": text,
                "source": source_code,
                "target": target_code,
                "format": "text"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=data, timeout=10) as response:
                    if response.status == 200:
                        result = await response.json()
                        translated_text = result["data"]["translations"][0]["translatedText"]
                        return translated_text
                    
                    raise Exception(f"Google翻译请求失败: {response.status}")
        
        except Exception as e:
            logger.error(f"Google翻译失败: {str(e)}")
            raise
    
    async def _baidu_translate(self, text: str, source_lang: str, target_lang: str) -> str:
        """百度翻译 (需要API密钥)"""
        app_id = os.getenv("BAIDU_TRANSLATE_APP_ID")
        secret_key = os.getenv("BAIDU_TRANSLATE_SECRET_KEY")
        
        if not app_id or not secret_key:
            raise Exception("百度翻译API密钥未配置")
        
        try:
            import random
            import hashlib
            
            source_code = self.language_map.get(source_lang, {}).get("baidu", source_lang)
            target_code = self.language_map.get(target_lang, {}).get("baidu", target_lang)
            
            salt = str(random.randint(32768, 65536))
            sign_str = app_id + text + salt + secret_key
            sign = hashlib.md5(sign_str.encode()).hexdigest()
            
            url = "https://fanyi-api.baidu.com/api/trans/vip/translate"
            data = {
                "q": text,
                "from": source_code,
                "to": target_code,
                "appid": app_id,
                "salt": salt,
                "sign": sign
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=data, timeout=10) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "trans_result" in result:
                            translated_text = result["trans_result"][0]["dst"]
                            return translated_text
                        else:
                            raise Exception(f"百度翻译错误: {result.get('error_msg', '未知错误')}")
                    
                    raise Exception(f"百度翻译请求失败: {response.status}")
        
        except Exception as e:
            logger.error(f"百度翻译失败: {str(e)}")
            raise
    
    async def _youdao_translate(self, text: str, source_lang: str, target_lang: str) -> str:
        """有道翻译 (需要API密钥)"""
        app_key = os.getenv("YOUDAO_TRANSLATE_APP_KEY")
        app_secret = os.getenv("YOUDAO_TRANSLATE_APP_SECRET")
        
        if not app_key or not app_secret:
            raise Exception("有道翻译API密钥未配置")
        
        try:
            import uuid
            import time
            import hashlib
            
            source_code = self.language_map.get(source_lang, {}).get("youdao", source_lang)
            target_code = self.language_map.get(target_lang, {}).get("youdao", target_lang)
            
            salt = str(uuid.uuid1())
            curtime = str(int(time.time()))
            
            sign_str = app_key + text + salt + curtime + app_secret
            sign = hashlib.sha256(sign_str.encode()).hexdigest()
            
            url = "https://openapi.youdao.com/api"
            data = {
                "q": text,
                "from": source_code,
                "to": target_code,
                "appKey": app_key,
                "salt": salt,
                "sign": sign,
                "signType": "v3",
                "curtime": curtime
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=data, timeout=10) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("errorCode") == "0":
                            translated_text = result["translation"][0]
                            return translated_text
                        else:
                            raise Exception(f"有道翻译错误: {result.get('errorCode')}")
                    
                    raise Exception(f"有道翻译请求失败: {response.status}")
        
        except Exception as e:
            logger.error(f"有道翻译失败: {str(e)}")
            raise

# 创建全局实例
translation_service = TranslationService()
