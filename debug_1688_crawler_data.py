#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试1688爬虫获取的原始数据
"""

import sys
import os
sys.path.append('backend')

from app.crawler.platforms.alibaba_1688 import Alibaba1688Crawler
import json

async def debug_crawler_data():
    """调试爬虫获取的数据"""
    print("🔍 初始化1688爬虫...")

    crawler = Alibaba1688Crawler()
    url = "https://detail.1688.com/offer/932222752479.html"

    try:
        print(f"📡 获取商品详情: {url}")

        # 直接调用get_product_detail方法
        result = await crawler.get_product_detail(url)

        if result:
            print("✅ 成功获取商品详情")

            # 分析返回的数据结构
            print(f"\n📊 返回数据顶层键:")
            for key in result.keys():
                value = result[key]
                print(f"  - {key}: {type(value).__name__}")
                if isinstance(value, (dict, list)):
                    print(f"    长度: {len(value)}")

            # 分析SKU数据
            skus = result.get('skus', [])
            print(f"\n🔍 SKU数据分析 (共{len(skus)}个):")
            if skus:
                for i, sku in enumerate(skus[:5]):
                    print(f"  SKU {i+1}:")
                    for key, value in sku.items():
                        if key == 'properties':
                            print(f"    - {key}: {len(value)}个属性")
                            for prop in value:
                                print(f"      * {prop.get('propertyName')}={prop.get('valueName')}")
                        else:
                            print(f"    - {key}: {value}")

            # 现在我们需要获取原始的初始化数据
            print(f"\n🔍 获取原始初始化数据...")

        else:
            print("❌ 未能获取到商品详情")

    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")

def find_sku_data_in_init(data, path="", max_depth=4, current_depth=0):
    """在初始化数据中查找SKU相关信息"""
    if current_depth > max_depth:
        return
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            # 检查是否是SKU相关的键
            if any(keyword in key.lower() for keyword in ['sku', 'spec', 'prop', 'attr', 'model', 'option']):
                print(f"\n🎯 找到SKU相关数据: {current_path}")
                print(f"   类型: {type(value).__name__}")
                
                if isinstance(value, dict):
                    print(f"   键数量: {len(value)}")
                    print(f"   前5个键: {list(value.keys())[:5]}")
                    
                    # 如果是SKU映射，显示详细信息
                    if len(value) < 50:  # 避免输出过多
                        for i, (k, v) in enumerate(list(value.items())[:10]):
                            print(f"   [{i}] {k}: {type(v).__name__}")
                            if isinstance(v, dict):
                                print(f"       子键: {list(v.keys())}")
                            elif isinstance(v, str):
                                print(f"       值: {v}")
                
                elif isinstance(value, list):
                    print(f"   列表长度: {len(value)}")
                    if value:
                        print(f"   第一项类型: {type(value[0]).__name__}")
                        if isinstance(value[0], dict):
                            print(f"   第一项键: {list(value[0].keys())}")
                        
                        # 显示前几项
                        for i, item in enumerate(value[:5]):
                            if isinstance(item, dict):
                                print(f"   [{i}] 键: {list(item.keys())}")
                            else:
                                print(f"   [{i}] 值: {item}")
                
                else:
                    print(f"   值: {str(value)[:100]}...")
            
            # 递归查找
            elif isinstance(value, (dict, list)) and current_depth < max_depth:
                find_sku_data_in_init(value, current_path, max_depth, current_depth + 1)
    
    elif isinstance(data, list):
        for i, item in enumerate(data[:3]):  # 只检查前3项
            find_sku_data_in_init(item, f"{path}[{i}]", max_depth, current_depth + 1)

def find_spec_data_in_init(data, path="", max_depth=4, current_depth=0):
    """在初始化数据中查找规格属性信息"""
    if current_depth > max_depth:
        return
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            # 检查是否是规格相关的键
            if any(keyword in key.lower() for keyword in ['color', 'size', 'model', '颜色', '尺寸', '型号', '规格', 'property', 'attribute']):
                print(f"\n🎯 找到规格相关数据: {current_path}")
                print(f"   类型: {type(value).__name__}")
                print(f"   值: {str(value)[:200]}...")
                
                if isinstance(value, list) and value:
                    print(f"   列表长度: {len(value)}")
                    for i, item in enumerate(value[:3]):
                        print(f"   [{i}] {type(item).__name__}: {str(item)[:100]}...")
            
            # 递归查找
            elif isinstance(value, (dict, list)) and current_depth < max_depth:
                find_spec_data_in_init(value, current_path, max_depth, current_depth + 1)
    
    elif isinstance(data, list):
        for i, item in enumerate(data[:3]):
            find_spec_data_in_init(item, f"{path}[{i}]", max_depth, current_depth + 1)

if __name__ == "__main__":
    import asyncio
    asyncio.run(debug_crawler_data())
