#!/usr/bin/env python3
"""
简单的认证系统
"""
import os
import jwt
import hashlib
from datetime import datetime, timedelta
from typing import Optional
from fastapi import HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
import logging

logger = logging.getLogger(__name__)

# JWT配置
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 24 * 60  # 24小时

# 默认管理员账号配置
DEFAULT_ADMIN_USERNAME = os.getenv("ADMIN_USERNAME", "admin")
DEFAULT_ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "admin123")

security = HTTPBearer()

class AuthManager:
    """认证管理器"""
    
    def __init__(self):
        self.admin_username = DEFAULT_ADMIN_USERNAME
        self.admin_password_hash = self._hash_password(DEFAULT_ADMIN_PASSWORD)
        logger.info(f"认证系统初始化完成，管理员用户名: {self.admin_username}")
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, username: str, password: str) -> bool:
        """验证用户名密码"""
        if username != self.admin_username:
            return False
        
        password_hash = self._hash_password(password)
        return password_hash == self.admin_password_hash
    
    def create_access_token(self, username: str) -> str:
        """创建访问令牌"""
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        to_encode = {
            "sub": username,
            "exp": expire,
            "iat": datetime.utcnow()
        }
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[str]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            username: str = payload.get("sub")
            if username is None:
                return None
            return username
        except jwt.PyJWTError:
            return None
    
    def authenticate_user(self, username: str, password: str) -> Optional[str]:
        """认证用户并返回令牌"""
        if self.verify_password(username, password):
            return self.create_access_token(username)
        return None

# 全局认证管理器实例
auth_manager = AuthManager()

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """获取当前用户（依赖注入）"""
    token = credentials.credentials

    # 首先尝试验证普通Token
    username = auth_manager.verify_token(token)

    # 如果普通Token验证失败，尝试验证预置Token
    if username is None:
        from .preset_tokens import preset_token_manager
        username = preset_token_manager.verify_preset_token(token)

    if username is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return username

def optional_auth(credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))) -> Optional[str]:
    """可选认证（用于某些接口可以匿名访问）"""
    if credentials is None:
        return None
    
    token = credentials.credentials
    return auth_manager.verify_token(token)

# 认证装饰器
def require_auth(func):
    """需要认证的装饰器"""
    def wrapper(*args, **kwargs):
        # 这个装饰器主要用于标识，实际认证通过Depends实现
        return func(*args, **kwargs)
    return wrapper
