# AqentCrawler 邮件功能快速配置指南

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装邮件相关依赖
pip install aiosmtplib email-validator jinja2
```

### 2. 配置邮箱

在 `.env` 文件中添加邮件配置：

#### QQ邮箱配置（推荐）
```bash
SMTP_SERVER=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password  # 使用应用专用密码
SMTP_USE_TLS=true
SMTP_USE_SSL=false
SENDER_EMAIL=<EMAIL>
SENDER_NAME=AqentCrawler系统
```

#### 163邮箱配置
```bash
SMTP_SERVER=smtp.163.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_password
SMTP_USE_TLS=true
SMTP_USE_SSL=false
SENDER_EMAIL=<EMAIL>
SENDER_NAME=AqentCrawler系统
```

### 3. 获取应用专用密码

#### QQ邮箱
1. 登录QQ邮箱
2. 设置 → 账户 → POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务
3. 开启SMTP服务
4. 生成授权码（应用专用密码）

#### Gmail
1. 登录Google账户
2. 安全性 → 两步验证
3. 应用专用密码 → 生成密码

### 4. 测试配置

```bash
# 运行邮件测试脚本
python scripts/test_email.py

# 或者初始化邮件模板
python scripts/init_email_templates.py
```

## 📧 基本使用

### 1. 发送测试邮件

```bash
curl -X POST "http://localhost:8000/api/v1/email/test-send?to_email=<EMAIL>" \
  -H "Authorization: Bearer your_token"
```

### 2. 发送系统告警

```python
from app.services.email_service import email_service

# 发送告警邮件
await email_service.send_template_email(
    to_emails=['<EMAIL>'],
    template_name='system_alert',
    subject='[系统告警] CPU使用率过高',
    template_data={
        'alert_title': 'CPU使用率过高',
        'alert_message': '当前CPU使用率已达到85%',
        'alert_type': 'warning'
    }
)
```

### 3. 发送爬虫状态通知

```python
# 发送爬虫状态通知
await email_service.send_template_email(
    to_emails=['<EMAIL>'],
    template_name='crawler_status',
    subject='[爬虫通知] 账号状态异常',
    template_data={
        'notification_title': '爬虫账号异常',
        'notification_message': '检测到账号登录失效',
        'status_type': 'warning'
    }
)
```

## 🔧 常见问题

### 1. 邮件发送失败

**问题**: 535 Authentication failed
**解决**: 
- 检查用户名和密码是否正确
- 确认使用应用专用密码（QQ邮箱、Gmail）
- 确认已开启SMTP服务

**问题**: Connection timeout
**解决**:
- 检查网络连接
- 确认SMTP服务器地址和端口
- 检查防火墙设置

### 2. 模板渲染错误

**问题**: Template not found
**解决**:
- 确认模板文件存在于 `backend/app/templates/email/` 目录
- 运行 `python scripts/init_email_templates.py` 检查模板

**问题**: Template data error
**解决**:
- 检查传入的模板数据格式
- 确认必需的字段已提供

### 3. 邮件被拒收

**问题**: 邮件进入垃圾箱
**解决**:
- 设置合适的发件人名称
- 避免使用敏感词汇
- 配置SPF/DKIM记录（生产环境）

## 📋 配置检查清单

- [ ] 已安装邮件相关依赖包
- [ ] 已在 `.env` 文件中配置SMTP信息
- [ ] 已获取并配置应用专用密码
- [ ] 已开启邮箱的SMTP服务
- [ ] 已测试邮件服务器连接
- [ ] 已验证邮件模板文件存在
- [ ] 已成功发送测试邮件

## 🎯 最佳实践

1. **安全性**
   - 使用应用专用密码，不要使用登录密码
   - 定期更换邮箱密码
   - 不要在代码中硬编码邮箱信息

2. **性能**
   - 使用异步发送避免阻塞
   - 设置合理的邮件发送频率
   - 批量发送时分批处理

3. **可靠性**
   - 添加重试机制
   - 记录发送日志
   - 监控发送成功率

4. **用户体验**
   - 使用有意义的邮件主题
   - 提供清晰的邮件内容
   - 包含必要的操作链接

## 📞 技术支持

如果遇到问题：

1. 查看详细错误日志
2. 运行测试脚本诊断问题
3. 检查网络和防火墙设置
4. 确认邮箱服务商的SMTP设置

---

**版本**: v1.0.0  
**更新时间**: 2025-01-20
