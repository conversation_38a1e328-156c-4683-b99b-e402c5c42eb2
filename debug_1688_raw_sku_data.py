#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试1688商品原始SKU数据结构
"""

import requests
import json
import re
from bs4 import BeautifulSoup

def debug_1688_raw_data():
    """调试1688原始数据"""
    url = "https://detail.1688.com/offer/932222752479.html"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print(f"🔍 获取页面数据: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找所有script标签中的JSON数据
        scripts = soup.find_all('script')
        
        for i, script in enumerate(scripts):
            if script.string and 'window.__INIT_DATA' in script.string:
                print(f"\n{'='*80}")
                print(f"🎯 找到 window.__INIT_DATA (script {i})")
                print(f"{'='*80}")
                
                # 提取JSON数据
                script_content = script.string
                
                # 查找JSON开始位置
                start_pos = script_content.find('{')
                if start_pos != -1:
                    # 找到最后一个}
                    end_pos = script_content.rfind('}')
                    if end_pos != -1:
                        json_str = script_content[start_pos:end_pos+1]
                        
                        try:
                            data = json.loads(json_str)
                            
                            # 分析数据结构
                            print(f"\n📊 数据结构分析:")
                            analyze_data_structure(data, "", 0, max_depth=3)
                            
                            # 查找SKU相关信息
                            print(f"\n🔍 查找SKU相关信息:")
                            find_sku_info(data, "")
                            
                            # 查找规格相关信息
                            print(f"\n🔍 查找规格相关信息:")
                            find_spec_info(data, "")
                            
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON解析失败: {e}")
                            # 尝试修复JSON
                            print("🔧 尝试修复JSON...")
                            try:
                                # 移除可能的JavaScript代码
                                json_str = re.sub(r';\s*$', '', json_str)
                                data = json.loads(json_str)
                                print("✅ JSON修复成功")
                                
                                # 分析修复后的数据
                                print(f"\n📊 修复后数据结构分析:")
                                analyze_data_structure(data, "", 0, max_depth=3)
                                
                            except Exception as e2:
                                print(f"❌ JSON修复失败: {e2}")
                
                break
        
    except Exception as e:
        print(f"❌ 获取数据失败: {str(e)}")

def analyze_data_structure(data, path, depth, max_depth=3):
    """分析数据结构"""
    if depth > max_depth:
        return
    
    indent = "  " * depth
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            if isinstance(value, (dict, list)):
                size_info = f" ({len(value)} items)" if isinstance(value, (dict, list)) else ""
                print(f"{indent}- {key}: {type(value).__name__}{size_info}")
                
                # 特别关注SKU和规格相关的键
                if any(keyword in key.lower() for keyword in ['sku', 'spec', 'prop', 'attr', 'option', 'variant']):
                    print(f"{indent}  🎯 [重要] 可能包含SKU/规格信息")
                    if depth < max_depth:
                        analyze_data_structure(value, current_path, depth + 1, max_depth)
                elif depth < max_depth - 1:
                    analyze_data_structure(value, current_path, depth + 1, max_depth)
            else:
                print(f"{indent}- {key}: {type(value).__name__} = {str(value)[:50]}...")
    
    elif isinstance(data, list) and data:
        print(f"{indent}List with {len(data)} items:")
        if len(data) > 0:
            print(f"{indent}  First item type: {type(data[0]).__name__}")
            if depth < max_depth:
                analyze_data_structure(data[0], f"{path}[0]", depth + 1, max_depth)

def find_sku_info(data, path):
    """查找SKU相关信息"""
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            # 检查键名是否包含SKU相关关键词
            if any(keyword in key.lower() for keyword in ['sku', 'spec', 'prop', 'attr', 'option', 'variant', 'model']):
                print(f"\n🎯 找到可能的SKU信息: {current_path}")
                print(f"   类型: {type(value).__name__}")
                
                if isinstance(value, dict):
                    print(f"   键: {list(value.keys())}")
                    # 打印前几个项目的详细信息
                    for i, (k, v) in enumerate(list(value.items())[:3]):
                        print(f"   [{i}] {k}: {type(v).__name__}")
                        if isinstance(v, (str, int, float)):
                            print(f"       值: {v}")
                        elif isinstance(v, dict):
                            print(f"       子键: {list(v.keys())}")
                        elif isinstance(v, list):
                            print(f"       列表长度: {len(v)}")
                            if v and isinstance(v[0], dict):
                                print(f"       第一项键: {list(v[0].keys())}")
                
                elif isinstance(value, list):
                    print(f"   列表长度: {len(value)}")
                    if value:
                        print(f"   第一项类型: {type(value[0]).__name__}")
                        if isinstance(value[0], dict):
                            print(f"   第一项键: {list(value[0].keys())}")
                        elif isinstance(value[0], str):
                            print(f"   前几项: {value[:5]}")
                
                else:
                    print(f"   值: {str(value)[:100]}...")
            
            # 递归查找
            if isinstance(value, (dict, list)):
                find_sku_info(value, current_path)
    
    elif isinstance(data, list):
        for i, item in enumerate(data):
            find_sku_info(item, f"{path}[{i}]")

def find_spec_info(data, path):
    """查找规格相关信息"""
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            # 检查键名是否包含规格相关关键词
            if any(keyword in key.lower() for keyword in ['color', 'size', 'model', '颜色', '尺寸', '型号', '规格']):
                print(f"\n🎯 找到可能的规格信息: {current_path}")
                print(f"   类型: {type(value).__name__}")
                print(f"   值: {str(value)[:200]}...")
            
            # 递归查找
            if isinstance(value, (dict, list)):
                find_spec_info(value, current_path)
    
    elif isinstance(data, list):
        for i, item in enumerate(data):
            find_spec_info(item, f"{path}[{i}]")

if __name__ == "__main__":
    debug_1688_raw_data()
