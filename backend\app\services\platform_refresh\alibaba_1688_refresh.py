"""
1688 Token刷新服务

专门处理1688平台的Token刷新功能
"""

import re
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple

from .base_refresh import BasePlatformRefresh


class Alibaba1688Refresh(BasePlatformRefresh):
    """1688 Token刷新服务"""

    def __init__(self):
        super().__init__("1688")

    def get_homepage_url(self) -> str:
        """获取1688主页URL"""
        return "https://www.1688.com"

    def extract_token_from_cookies(self, cookies: list) -> Tuple[Optional[str], Optional[datetime]]:
        """从1688 Cookie中提取Token和过期时间"""
        token = None
        token_expires_at = None

        for cookie in cookies:
            if cookie['name'] == '_m_h5_tk':
                m_h5_tk = cookie['value']
                if '_' in m_h5_tk:
                    token_parts = m_h5_tk.split('_')
                    token = token_parts[0]
                    
                    # 提取过期时间
                    if len(token_parts) > 1:
                        try:
                            expires_timestamp = int(token_parts[1]) / 1000
                            token_expires_at = datetime.fromtimestamp(expires_timestamp)
                        except (ValueError, IndexError):
                            # 如果无法解析过期时间，设置为24小时后过期
                            token_expires_at = datetime.now() + timedelta(hours=24)
                    break

        return token, token_expires_at

    def get_critical_cookies(self) -> list:
        """获取1688关键Cookie列表"""
        return [
            'leftMenuLastMode', 'leftMenuModeTip', 'keywordsHistory',
            'plugin_home_downLoad_cookie', 'JSESSIONID', 'EGG_SESS',
            '_m_h5_tk', 'cookie2', 'wk_cookie2'
        ]

    def filter_cookies(self, cookie_string: str) -> str:
        """过滤1688不必要的Cookie"""
        return cookie_string
        # if not cookie_string:
        #     return cookie_string
            
        # try:
        #     # 分割Cookie
        #     cookies = cookie_string.split(';')
        #     critical_cookies = self.get_critical_cookies()
            
        #     # 只保留关键Cookie
        #     filtered_cookies = []
        #     for cookie in cookies:
        #         cookie = cookie.strip()
        #         if '=' in cookie:
        #             name = cookie.split('=')[0].strip()
        #             if name in critical_cookies:
        #                 filtered_cookies.append(cookie)
            
        #     return '; '.join(filtered_cookies)
            
        # except Exception as e:
        #     print(f"过滤1688 Cookie失败: {str(e)}")
        #     return cookie_string
