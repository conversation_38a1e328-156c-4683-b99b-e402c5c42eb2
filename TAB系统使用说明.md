# AqentCrawler TAB系统使用说明

## 概述

我们已经成功将前端UI从传统的单页面切换模式升级为支持多TAB页面的模式，现在您可以同时打开多个页面并在它们之间快速切换，无需频繁的页面跳转。

## 主要功能

### 1. 多TAB支持
- 可以同时打开多个不同的功能页面
- 每个TAB都是独立的，保持各自的状态
- 支持TAB之间的快速切换

### 2. 智能TAB管理
- 点击侧边栏菜单项会打开对应的TAB
- 如果TAB已存在，会直接切换到该TAB
- 如果TAB不存在，会创建新的TAB

### 3. TAB关闭功能
- 每个TAB都有关闭按钮（×）
- 关闭当前TAB时会自动切换到相邻的TAB
- 至少保持一个TAB打开

### 4. 参数传递支持
- 支持向TAB页面传递参数
- 例如：商品详情页面可以接收商品URL参数
- 参数变化时会自动更新页面内容

## 使用方式

### 基本操作
1. **打开TAB**: 点击左侧菜单项
2. **切换TAB**: 点击顶部TAB标签
3. **关闭TAB**: 点击TAB右侧的×按钮

### 程序化操作
在组件中可以使用 `useTabNavigation` 组合式函数：

```javascript
import { useTabNavigation } from '../composables/useTabNavigation.js'

const { openTab, openProductDetail, openPlatforms } = useTabNavigation()

// 打开商品详情页面并传递URL参数
openProductDetail('https://item.taobao.com/item.htm?id=123456789')

// 打开平台管理页面
openPlatforms()

// 打开任意TAB页面
openTab('/crawler-config', { someParam: 'value' })
```

## 技术实现

### 1. 事件总线系统
- 使用 `eventBus.js` 实现组件间通信
- 支持TAB的打开、关闭、切换事件

### 2. 动态组件加载
- 使用 Vue 3 的 `defineAsyncComponent` 实现按需加载
- 每个TAB对应一个独立的组件实例

### 3. 状态管理
- 每个TAB维护独立的状态
- 支持参数传递和状态更新

## 已更新的页面

以下页面已经适配新的TAB系统：

1. **系统概览** (Dashboard)
   - 支持通过TAB导航打开商品详情页面
   
2. **商品详情** (ProductDetail)
   - 支持接收URL参数
   - 支持props和路由参数两种方式

3. **其他管理页面**
   - 平台管理
   - 爬虫配置
   - 爬虫池管理
   - 代理管理
   - 调用日志
   - 人工验证
   - 爬虫测试
   - 验证设置

## 优势

1. **提升用户体验**: 无需频繁的页面切换
2. **提高工作效率**: 可以同时查看多个页面的信息
3. **保持上下文**: 每个TAB保持独立状态，切换时不丢失数据
4. **灵活导航**: 支持多种打开TAB的方式

## 注意事项

1. TAB数量过多时会出现横向滚动条
2. 每个TAB都会占用一定的内存，建议及时关闭不需要的TAB
3. 页面刷新时会重置所有TAB，只保留默认的系统概览TAB

## 后续扩展

1. 可以添加TAB拖拽排序功能
2. 可以添加TAB收藏/固定功能
3. 可以添加TAB历史记录功能
4. 可以添加TAB分组功能
