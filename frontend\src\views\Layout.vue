<template>
  <div class="layout">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: collapsed }">
      <div class="sidebar-header">
        <h3 v-if="!collapsed">AqentCrawler</h3>
        <button @click="toggleSidebar" class="toggle-btn">
          {{ collapsed ? '→' : '←' }}
        </button>
      </div>

      <nav class="sidebar-nav">
        <a
          v-for="item in menuOptions"
          :key="item.key"
          @click="openTab(item)"
          class="nav-item"
          :class="{ active: hasActiveTab(item.key) }"
          :title="item.label"
        >
          <span class="nav-icon">{{ item.icon }}</span>
          <span v-if="!collapsed" class="nav-label">{{ item.label }}</span>
        </a>
      </nav>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 顶部导航 -->
      <header class="header">
        <h2>AqentCrawler 管理后台</h2>
        <div class="header-actions">
          <span class="username">{{ username }}</span>
          <button @click="logout" class="logout-btn">退出登录</button>
        </div>
      </header>

      <!-- TAB标签页区域 -->
      <div class="tabs-container" v-if="tabs.length > 0">
        <div class="tabs-wrapper">
          <div class="tabs-list">
            <div
              v-for="tab in tabs"
              :key="tab.key"
              class="tab-item"
              :class="{ active: activeTabKey === tab.key }"
              @click="switchTab(tab.key)"
            >
              <span class="tab-icon">{{ tab.icon }}</span>
              <span class="tab-label">{{ tab.label }}</span>
              <button
                v-if="tabs.length > 1"
                @click.stop="closeTab(tab.key)"
                class="tab-close"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <main class="content">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          v-show="activeTabKey === tab.key"
          class="tab-content"
        >
          <component
            :is="tab.component"
            :key="`${tab.key}-${JSON.stringify(tab.params || {})}`"
            v-bind="tab.params || {}"
          />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useEventBus, EVENTS } from '../utils/eventBus.js'

const route = useRoute()
const router = useRouter()
const { emit, on } = useEventBus()
const collapsed = ref(false)
const username = ref('')
const tabs = ref([])
const activeTabKey = ref('')

// 动态导入组件
const componentMap = {
  '/': defineAsyncComponent(() => import('./Dashboard.vue')),
  '/platforms': defineAsyncComponent(() => import('./Platforms.vue')),
  '/crawler-config': defineAsyncComponent(() => import('./CrawlerConfig.vue')),
  '/crawler-pool': defineAsyncComponent(() => import('./CrawlerPool.vue')),
  '/proxies': defineAsyncComponent(() => import('./Proxies.vue')),
  '/logs': defineAsyncComponent(() => import('./Logs.vue')),
  '/verification': defineAsyncComponent(() => import('./Verification.vue')),
  '/crawler-test': defineAsyncComponent(() => import('./CrawlerTest.vue')),
  '/product-detail': defineAsyncComponent(() => import('./ProductDetail.vue')),
  '/verification-settings': defineAsyncComponent(() => import('./VerificationSettings.vue'))
}

const menuOptions = [
  {
    label: '系统概览',
    key: '/',
    icon: '📊'
  },
  {
    label: '平台管理',
    key: '/platforms',
    icon: '🏪'
  },
  {
    label: '爬虫配置',
    key: '/crawler-config',
    icon: '🤖'
  },
  {
    label: '爬虫池管理',
    key: '/crawler-pool',
    icon: '🏊‍♂️'
  },
  {
    label: '代理管理',
    key: '/proxies',
    icon: '🌐'
  },
  {
    label: '调用日志',
    key: '/logs',
    icon: '📝'
  },
  {
    label: '人工验证',
    key: '/verification',
    icon: '🔐'
  },
  {
    label: '爬虫测试',
    key: '/crawler-test',
    icon: '🕷️'
  },
  {
    label: '商品详情',
    key: '/product-detail',
    icon: '📦'
  },
  {
    label: '验证设置',
    key: '/verification-settings',
    icon: '⚙️'
  }
]

// 检查是否有活跃的标签页
const hasActiveTab = (key) => {
  return tabs.value.some(tab => tab.key === key)
}

// 打开新标签页
const openTab = (menuItem, params = {}) => {
  // 检查标签页是否已存在
  const existingTab = tabs.value.find(tab => tab.key === menuItem.key)

  if (existingTab) {
    // 如果已存在，切换到该标签页
    activeTabKey.value = menuItem.key

    // 如果有参数，更新组件的props
    if (Object.keys(params).length > 0) {
      existingTab.params = params
    }
  } else {
    // 创建新标签页
    const newTab = {
      key: menuItem.key,
      label: menuItem.label,
      icon: menuItem.icon,
      component: componentMap[menuItem.key],
      params: params
    }

    tabs.value.push(newTab)
    activeTabKey.value = menuItem.key
  }
}

// 通过事件打开标签页
const openTabByEvent = (data) => {
  const { key, params = {} } = data
  const menuItem = menuOptions.find(item => item.key === key)

  if (menuItem) {
    openTab(menuItem, params)
  } else {
    console.warn(`未找到菜单项: ${key}`)
  }
}

// 切换标签页
const switchTab = (key) => {
  activeTabKey.value = key
}

// 关闭标签页
const closeTab = (key) => {
  const index = tabs.value.findIndex(tab => tab.key === key)
  if (index === -1) return

  // 移除标签页
  tabs.value.splice(index, 1)

  // 如果关闭的是当前活跃标签页，需要切换到其他标签页
  if (activeTabKey.value === key) {
    if (tabs.value.length > 0) {
      // 优先切换到右边的标签页，如果没有则切换到左边的
      const newIndex = index < tabs.value.length ? index : index - 1
      activeTabKey.value = tabs.value[newIndex].key
    } else {
      activeTabKey.value = ''
    }
  }
}

const toggleSidebar = () => {
  collapsed.value = !collapsed.value
}

const logout = () => {
  // 清除本地存储
  localStorage.removeItem('token')
  localStorage.removeItem('username')

  // 跳转到登录页
  router.push('/login')
}

// 事件监听器清理函数
let unsubscribeOpenTab = null

// 初始化
onMounted(() => {
  username.value = localStorage.getItem('username') || 'admin'

  // 确保侧边栏默认展开
  collapsed.value = false

  // 注册事件监听器
  unsubscribeOpenTab = on(EVENTS.OPEN_TAB, openTabByEvent)

  // 检查是否有特殊路由需要打开特定TAB
  if (route.meta?.openTab) {
    const targetMenu = menuOptions.find(item => item.key === route.meta.openTab)
    if (targetMenu) {
      openTab(targetMenu)
      return
    }
  }

  // 默认打开系统概览页面
  const dashboardMenu = menuOptions.find(item => item.key === '/')
  if (dashboardMenu) {
    openTab(dashboardMenu)
  }

  console.log('Layout初始化完成，侧边栏状态:', collapsed.value)
  console.log('菜单选项:', menuOptions)
})

// 清理
onUnmounted(() => {
  if (unsubscribeOpenTab) {
    unsubscribeOpenTab()
  }
})
</script>

<style scoped>
.layout {
  display: flex;
  min-height: 100vh;
  background: #f5f5f5;
}

.sidebar {
  width: 240px;
  background: #001529;
  color: white;
  transition: width 0.3s;
  z-index: 100;
  flex-shrink: 0;
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid #1f1f1f;
  position: relative;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.toggle-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  font-size: 14px;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
  padding: 16px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.65);
  text-decoration: none;
  transition: all 0.3s;
  cursor: pointer;
  border-left: 3px solid transparent;
  margin-bottom: 2px;
}

.nav-item:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  color: white;
  background: #1890ff;
  border-left-color: #40a9ff;
}

.nav-icon {
  font-size: 16px;
  margin-right: 12px;
  min-width: 16px;
  text-align: center;
}

.nav-label {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header {
  height: 64px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.username {
  color: #666;
  font-size: 14px;
}

.logout-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.logout-btn:hover {
  background: #ff7875;
}

.content {
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.tab-content {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

/* TAB标签页样式 */
.tabs-container {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.tabs-wrapper {
  padding: 0 24px;
}

.tabs-list {
  display: flex;
  align-items: center;
  gap: 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabs-list::-webkit-scrollbar {
  display: none;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  min-width: 120px;
  position: relative;
  color: #666;
  font-size: 14px;
}

.tab-item:hover {
  background: #f5f5f5;
  color: #333;
}

.tab-item.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
  background: #fafafa;
}

.tab-icon {
  font-size: 14px;
  margin-right: 8px;
}

.tab-label {
  flex: 1;
  text-align: left;
}

.tab-close {
  margin-left: 8px;
  background: none;
  border: none;
  color: #999;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.tab-close:hover {
  background: #ff4d4f;
  color: white;
}

.tab-item.active .tab-close:hover {
  background: #ff4d4f;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 64px;
  }

  .sidebar .nav-label {
    display: none;
  }

  .sidebar-header h3 {
    display: none;
  }

  .tabs-wrapper {
    padding: 0 12px;
  }

  .tab-item {
    min-width: 100px;
    padding: 8px 12px;
  }
}

/* 确保侧边栏在小屏幕上的可见性 */
@media (max-width: 480px) {
  .layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    order: 2;
  }

  .sidebar-nav {
    display: flex;
    overflow-x: auto;
    padding: 8px;
  }

  .nav-item {
    flex-direction: column;
    min-width: 60px;
    padding: 8px;
    text-align: center;
  }

  .nav-icon {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .nav-label {
    font-size: 12px;
    display: block !important;
  }
}
</style>
