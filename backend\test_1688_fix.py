#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试1688商品详情修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.crawler.platforms.alibaba_1688 import Alibaba1688Crawler

def test_1688_detail():
    """测试1688商品详情提取"""
    crawler = Alibaba1688Crawler()
    
    # 测试商品ID
    product_id = "591940079968"
    source_url = f"https://detail.1688.com/offer/{product_id}.html"
    
    print(f"🔍 开始测试1688商品详情提取: {product_id}")
    print(f"🔗 商品链接: {source_url}")
    
    try:
        # 读取保存的HTML文件
        html_file = f"../temp/1688_{product_id}_full.txt"
        if os.path.exists(html_file):
            print(f"✅ 从文件读取HTML: {html_file}")
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
        else:
            print(f"❌ HTML文件不存在: {html_file}")
            return
        
        # 解析商品详情
        result = crawler._parse_1688_product_detail(html_content, source_url)
        
        if result:
            print("✅ 商品详情解析成功!")
            print(f"📋 商品标题: {result.get('title', 'N/A')}")
            print(f"🏢 公司名称: {result.get('company_name', 'N/A')}")
            print(f"🖼️ 图片数量: {len(result.get('images', []))}")
            print(f"📦 SKU数量: {len(result.get('skus', []))}")
            print(f"💰 价格范围: {result.get('price_range', 'N/A')}")
            print(f"🚚 运费: {result.get('freight_cost', 'N/A')}")
            
            # 检查SKU属性
            skus = result.get('skus', [])
            if skus:
                print(f"\n📦 SKU详情 (前3个):")
                for i, sku in enumerate(skus[:3]):
                    print(f"  SKU {i+1}:")
                    print(f"    ID: {sku.get('id')}")
                    print(f"    价格: {sku.get('price')} 分")
                    print(f"    重量: {sku.get('weight')} 克")
                    print(f"    属性数量: {len(sku.get('properties', []))}")
                    
                    # 检查属性详情
                    properties = sku.get('properties', [])
                    for prop in properties:
                        print(f"      属性: {prop.get('propertyName')} = {prop.get('valueName')} (ID: {prop.get('propertyId')}, ValueID: {prop.get('valueId')})")
            
            print("\n✅ 测试完成!")
        else:
            print("❌ 商品详情解析失败!")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"❌ 详细错误:\n{traceback.format_exc()}")

if __name__ == "__main__":
    test_1688_detail()
