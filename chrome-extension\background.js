/**
 * 淘宝配置提取器 - 后台脚本
 * 处理扩展的后台逻辑
 */

class TaobaoExtractorBackground {
    constructor() {
        this.init();
    }

    init() {
        // 监听扩展安装
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstalled(details);
        });

        // 监听来自content script的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse)
                .then(result => {
                    if (result !== undefined) {
                        sendResponse(result);
                    }
                })
                .catch(error => {
                    console.error('处理消息失败:', error);
                    sendResponse({ success: false, error: error.message });
                });
            return true; // 表示异步响应
        });

        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdated(tabId, changeInfo, tab);
        });

        console.log('🕷️ 淘宝配置提取器后台服务已启动');
    }

    handleInstalled(details) {
        if (details.reason === 'install') {
            console.log('扩展首次安装');
            this.setDefaultSettings();
            this.showWelcomeNotification();
        } else if (details.reason === 'update') {
            console.log('扩展已更新');
            this.showUpdateNotification();
        }
    }

    async setDefaultSettings() {
        try {
            await chrome.storage.sync.set({
                backendUrl: 'http://localhost:8000',
                autoExtract: false,
                notificationEnabled: true,
                extractInterval: 3600000 // 1小时
            });
            console.log('默认设置已保存');
        } catch (error) {
            console.error('保存默认设置失败:', error);
        }
    }

    showWelcomeNotification() {
        try {
            if (chrome.notifications && chrome.notifications.create) {
                chrome.notifications.create({
                    type: 'basic',
                    iconUrl: 'icons/icon48.png',
                    title: '淘宝配置提取器',
                    message: '扩展安装成功！请在淘宝页面点击扩展图标开始使用。'
                });
            } else {
                console.log('通知权限不可用，跳过欢迎通知');
            }
        } catch (error) {
            console.error('显示欢迎通知失败:', error);
        }
    }

    showUpdateNotification() {
        try {
            if (chrome.notifications && chrome.notifications.create) {
                chrome.notifications.create({
                    type: 'basic',
                    iconUrl: 'icons/icon48.png',
                    title: '淘宝配置提取器',
                    message: '扩展已更新到最新版本！'
                });
            } else {
                console.log('通知权限不可用，跳过更新通知');
            }
        } catch (error) {
            console.error('显示更新通知失败:', error);
        }
    }

    async handleMessage(request, _sender, _sendResponse) {
        try {
            switch (request.action) {
                case 'getAllCookies':
                    return await this.getAllCookies(request.domain);

                case 'saveConfig':
                    await this.saveConfig(request.data);
                    return { success: true };

                case 'getConfig':
                    const config = await this.getConfig();
                    return { success: true, data: config };

                case 'sendToBackend':
                    const result = await this.sendToBackend(request.data);
                    return { success: true, data: result };

                case 'checkBackendStatus':
                    const status = await this.checkBackendStatus();
                    return { success: true, data: status };

                default:
                    return { success: false, error: '未知操作' };
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            return { success: false, error: error.message };
        }
    }

    handleTabUpdated(tabId, changeInfo, tab) {
        // 当标签页完成加载且是淘宝页面时，更新扩展图标
        if (changeInfo.status === 'complete' && tab.url) {
            if (tab.url.includes('taobao.com') || tab.url.includes('tmall.com')) {
                this.setActiveIcon(tabId);
            } else {
                this.setInactiveIcon(tabId);
            }
        }
    }

    setActiveIcon(tabId) {
        chrome.action.setIcon({
            tabId: tabId,
            path: {
                "16": "icons/icon16.png",
                "32": "icons/icon32.png",
                "48": "icons/icon48.png",
                "128": "icons/icon128.png"
            }
        });

        chrome.action.setBadgeText({
            tabId: tabId,
            text: '✓'
        });

        chrome.action.setBadgeBackgroundColor({
            tabId: tabId,
            color: '#28a745'
        });
    }

    setInactiveIcon(tabId) {
        chrome.action.setIcon({
            tabId: tabId,
            path: {
                "16": "icons/icon16-gray.png",
                "32": "icons/icon32-gray.png",
                "48": "icons/icon48-gray.png",
                "128": "icons/icon128-gray.png"
            }
        });

        chrome.action.setBadgeText({
            tabId: tabId,
            text: ''
        });
    }

    async saveConfig(configData) {
        try {
            const timestamp = Date.now();
            const configWithMeta = {
                ...configData,
                savedAt: timestamp,
                version: '1.0.0'
            };

            // 保存到本地存储
            await chrome.storage.local.set({
                [`config_${timestamp}`]: configWithMeta,
                lastConfig: configWithMeta
            });

            // 保存到同步存储（最近的配置）
            await chrome.storage.sync.set({
                recentConfig: configWithMeta
            });

            console.log('配置已保存:', configWithMeta);
            return configWithMeta;

        } catch (error) {
            console.error('保存配置失败:', error);
            throw error;
        }
    }

    async getConfig() {
        try {
            const result = await chrome.storage.local.get(['lastConfig']);
            return result.lastConfig || null;
        } catch (error) {
            console.error('获取配置失败:', error);
            throw error;
        }
    }

    async sendToBackend(configData) {
        try {
            const settings = await chrome.storage.sync.get(['backendUrl']);
            const backendUrl = settings.backendUrl || 'http://localhost:8000';

            const response = await fetch(`${backendUrl}/api/v1/config/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    platform: 'taobao',
                    username: configData.username,
                    cookie: configData.cookie,
                    token: configData.token,
                    user_agent: configData.userAgent,
                    extracted_at: configData.extractedAt || new Date().toISOString(),
                    page_url: configData.pageUrl
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            if (result.code !== 200) {
                throw new Error(result.message || '后端处理失败');
            }

            console.log('配置发送成功:', result);
            
            // 显示成功通知
            this.showSuccessNotification();
            
            return result;

        } catch (error) {
            console.error('发送到后端失败:', error);
            
            // 显示失败通知
            this.showErrorNotification(error.message);
            
            throw error;
        }
    }

    async checkBackendStatus() {
        try {
            const settings = await chrome.storage.sync.get(['backendUrl']);
            const backendUrl = settings.backendUrl || 'http://localhost:8000';

            const response = await fetch(`${backendUrl}/api/v1/health`, {
                method: 'GET',
                timeout: 5000
            });

            return {
                online: response.ok,
                status: response.status,
                url: backendUrl
            };

        } catch (error) {
            console.error('检查后端状态失败:', error);
            return {
                online: false,
                error: error.message,
                url: settings.backendUrl || 'http://localhost:8000'
            };
        }
    }

    showSuccessNotification() {
        try {
            if (chrome.notifications && chrome.notifications.create) {
                chrome.notifications.create({
                    type: 'basic',
                    iconUrl: 'icons/icon48.png',
                    title: '配置提取成功',
                    message: '淘宝配置已成功提取并发送到后端！'
                });
            } else {
                console.log('通知权限不可用，跳过成功通知');
            }
        } catch (error) {
            console.error('显示成功通知失败:', error);
        }
    }

    showErrorNotification(errorMessage) {
        try {
            if (chrome.notifications && chrome.notifications.create) {
                chrome.notifications.create({
                    type: 'basic',
                    iconUrl: 'icons/icon48.png',
                    title: '配置提取失败',
                    message: `提取失败: ${errorMessage}`
                });
            } else {
                console.log('通知权限不可用，跳过错误通知');
            }
        } catch (error) {
            console.error('显示错误通知失败:', error);
        }
    }

    async getAllCookies(domain) {
        try {
            console.log('🍪 开始获取所有Cookie，域名:', domain);

            // 获取指定域名的所有Cookie
            const cookies = await chrome.cookies.getAll({
                domain: domain
            });

            // 同时获取父域名的Cookie（如.taobao.com）
            const parentDomainCookies = await chrome.cookies.getAll({
                domain: '.' + domain.split('.').slice(-2).join('.')
            });

            // 合并Cookie
            const allCookies = [...cookies, ...parentDomainCookies];

            // 去重（基于name和domain）
            const uniqueCookies = [];
            const seen = new Set();

            for (const cookie of allCookies) {
                const key = `${cookie.name}:${cookie.domain}`;
                if (!seen.has(key)) {
                    seen.add(key);
                    uniqueCookies.push(cookie);
                }
            }

            console.log(`🍪 获取到 ${uniqueCookies.length} 个唯一Cookie`);

            // 按照淘宝Cookie的重要性排序
            const sortedCookies = this.sortCookiesByImportance(uniqueCookies);

            // 转换为Cookie字符串
            const cookieString = sortedCookies
                .map(cookie => `${cookie.name}=${cookie.value}`)
                .join('; ');

            console.log('🍪 生成Cookie字符串长度:', cookieString.length);
            console.log('🍪 Cookie前200字符:', cookieString.substring(0, 200));

            return {
                success: true,
                cookie: cookieString,
                count: sortedCookies.length
            };

        } catch (error) {
            console.error('获取Cookie失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    sortCookiesByImportance(cookies) {
        // 定义淘宝Cookie的重要性顺序
        const importanceOrder = [
            'cookie2', 't', '_tb_token_', 'thw', 'xlly_s', 'sca', '_samesite_flag_',
            'wk_cookie2', 'wk_unb', 'env_bak', 'sdkSilent', 'aui', 'mt', 'cna',
            '3PcFlag', 'unb', 'lgc', 'cancelledSubSites', 'cookie17', 'dnk',
            'tracknick', '_l_g_', 'sg', '_nk_', 'cookie1', 'sgcookie',
            'havana_lgc2_0', '_hvn_lgc_', 'havana_lgc_exp', 'cookie3_bak',
            'cookie3_bak_exp', 'uc1', 'uc3', 'csg', 'skt', 'existShop',
            'uc4', '_cc_', 'havana_sdkSilent', 'mtop_partitioned_detect',
            '_m_h5_tk', '_m_h5_tk_enc', 'tfstk', 'isg'
        ];

        // 创建重要性映射
        const importanceMap = {};
        importanceOrder.forEach((name, index) => {
            importanceMap[name] = index;
        });

        // 排序Cookie
        return cookies.sort((a, b) => {
            const aImportance = importanceMap[a.name] ?? 999;
            const bImportance = importanceMap[b.name] ?? 999;

            if (aImportance !== bImportance) {
                return aImportance - bImportance;
            }

            // 如果重要性相同，按名称排序
            return a.name.localeCompare(b.name);
        });
    }

    // 定期检查配置状态
    async scheduleConfigCheck() {
        const settings = await chrome.storage.sync.get(['extractInterval', 'autoExtract']);
        
        if (settings.autoExtract) {
            const interval = settings.extractInterval || 3600000; // 默认1小时
            
            setInterval(async () => {
                await this.checkAndExtractConfig();
            }, interval);
        }
    }

    async checkAndExtractConfig() {
        try {
            // 获取所有淘宝标签页
            const tabs = await chrome.tabs.query({
                url: ["*://*.taobao.com/*", "*://*.tmall.com/*"]
            });

            for (const tab of tabs) {
                try {
                    // 检查标签页状态
                    const response = await chrome.tabs.sendMessage(tab.id, {
                        action: 'checkStatus'
                    });

                    if (response && response.success && response.data.isLoggedIn) {
                        // 自动提取配置
                        const configResponse = await chrome.tabs.sendMessage(tab.id, {
                            action: 'extractConfig'
                        });

                        if (configResponse && configResponse.success) {
                            await this.sendToBackend(configResponse.data);
                            console.log(`自动提取配置成功: ${tab.url}`);
                        }
                    }
                } catch (error) {
                    console.warn(`标签页 ${tab.id} 自动提取失败:`, error);
                }
            }
        } catch (error) {
            console.error('定期检查配置失败:', error);
        }
    }
}

// 初始化后台服务
const background = new TaobaoExtractorBackground();
