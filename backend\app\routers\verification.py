"""
人工验证相关API路由
"""
from fastapi import APIRouter, Request, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import time
from datetime import datetime

from ..database import get_db
from ..models import Platform

router = APIRouter(prefix="/api/v1/verification", tags=["verification"])

@router.get("/pending")
async def get_pending_verifications():
    """获取待处理的人工验证任务"""
    try:
        # 模拟返回待处理任务
        # 实际应用中这里会从验证处理器获取真实的待处理任务
        pending_tasks = [
            {
                "task_id": "task_001",
                "platform": "taobao",
                "verification_type": "qrcode",
                "username": "dingji<PERSON><PERSON>",
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "timeout": 300,
                "data": {
                    "qr_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
                    "instruction": "请使用手机淘宝扫描二维码完成登录"
                }
            },
            {
                "task_id": "task_002", 
                "platform": "jd",
                "verification_type": "sms",
                "username": "13800138000",
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "timeout": 180,
                "data": {
                    "phone_number": "138****8000",
                    "instruction": "请输入发送到手机的6位验证码"
                }
            },
            {
                "task_id": "task_003",
                "platform": "pdd", 
                "verification_type": "slider",
                "username": "testuser",
                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "timeout": 120,
                "data": {
                    "background_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
                    "slider_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
                    "instruction": "请拖动滑块完成验证"
                }
            }
        ]
        
        return {
            "code": 200,
            "message": "success",
            "data": pending_tasks
        }
        
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取待处理验证任务失败: {str(e)}",
            "data": []
        }

@router.post("/complete")
async def complete_verification(request: Request):
    """完成人工验证任务"""
    try:
        body = await request.json()
        task_id = body.get("task_id")
        result = body.get("result")
        verification_type = body.get("verification_type")
        
        if not task_id or not result:
            return {
                "code": 400,
                "message": "缺少必要参数：task_id 和 result",
                "data": None
            }
        
        # 验证结果格式
        if verification_type == "sms" and not result.isdigit():
            return {
                "code": 400,
                "message": "短信验证码必须是数字",
                "data": None
            }
        
        if verification_type == "slider":
            try:
                distance = int(result)
                if distance < 0 or distance > 500:
                    return {
                        "code": 400,
                        "message": "滑块距离必须在0-500像素之间",
                        "data": None
                    }
            except ValueError:
                return {
                    "code": 400,
                    "message": "滑块距离必须是数字",
                    "data": None
                }
        
        # 这里需要调用验证处理器完成任务
        # 实际应用中会调用：
        # from ..crawler.auth.verification_handler import verification_handler
        # success = await verification_handler.manual_queue.complete_task(task_id, result)
        
        # 模拟完成任务
        success = True
        
        if success:
            return {
                "code": 200,
                "message": "验证任务完成",
                "data": {
                    "task_id": task_id,
                    "result": result,
                    "completed_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            }
        else:
            return {
                "code": 404,
                "message": "任务不存在或已过期",
                "data": None
            }
        
    except Exception as e:
        return {
            "code": 500,
            "message": f"完成验证任务失败: {str(e)}",
            "data": None
        }

@router.get("/types")
async def get_verification_types():
    """获取支持的验证类型"""
    verification_types = [
        {
            "type": "password",
            "name": "密码验证",
            "description": "基础的用户名密码验证",
            "auto_supported": True
        },
        {
            "type": "captcha", 
            "name": "图形验证码",
            "description": "需要识别图片中的字符或数字",
            "auto_supported": True
        },
        {
            "type": "slider",
            "name": "滑块验证",
            "description": "需要拖动滑块到指定位置",
            "auto_supported": False
        },
        {
            "type": "puzzle",
            "name": "拼图验证", 
            "description": "需要拖动拼图块到正确位置",
            "auto_supported": False
        },
        {
            "type": "click",
            "name": "点击验证",
            "description": "需要按顺序点击指定图片",
            "auto_supported": False
        },
        {
            "type": "sms",
            "name": "短信验证码",
            "description": "需要输入手机收到的验证码",
            "auto_supported": False
        },
        {
            "type": "qrcode",
            "name": "扫码登录",
            "description": "需要使用手机APP扫描二维码",
            "auto_supported": False
        },
        {
            "type": "email",
            "name": "邮箱验证",
            "description": "需要点击邮箱中的验证链接",
            "auto_supported": False
        }
    ]
    
    return {
        "code": 200,
        "message": "success",
        "data": verification_types
    }

@router.get("/stats")
async def get_verification_stats():
    """获取验证统计信息"""
    try:
        # 模拟统计数据
        # 实际应用中会从数据库或缓存中获取真实统计
        stats = {
            "total_tasks": 156,
            "pending_tasks": 3,
            "completed_tasks": 142,
            "failed_tasks": 11,
            "success_rate": 92.8,
            "avg_completion_time": 45.6,  # 秒
            "by_type": {
                "qrcode": {"total": 89, "success": 85, "rate": 95.5},
                "sms": {"total": 34, "success": 31, "rate": 91.2},
                "slider": {"total": 23, "success": 18, "rate": 78.3},
                "captcha": {"total": 10, "success": 8, "rate": 80.0}
            },
            "by_platform": {
                "taobao": {"total": 78, "success": 74, "rate": 94.9},
                "jd": {"total": 45, "success": 41, "rate": 91.1},
                "pdd": {"total": 23, "success": 19, "rate": 82.6},
                "1688": {"total": 10, "success": 8, "rate": 80.0}
            }
        }
        
        return {
            "code": 200,
            "message": "success", 
            "data": stats
        }
        
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取验证统计失败: {str(e)}",
            "data": {}
        }

@router.post("/test")
async def test_verification_system():
    """测试验证系统"""
    try:
        # 模拟创建一个测试验证任务
        test_task = {
            "task_id": f"test_{int(time.time())}",
            "platform": "test",
            "verification_type": "captcha",
            "username": "test_user",
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data": {
                "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
                "instruction": "这是一个测试验证码，请输入：TEST"
            }
        }
        
        return {
            "code": 200,
            "message": "验证系统测试任务已创建",
            "data": test_task
        }
        
    except Exception as e:
        return {
            "code": 500,
            "message": f"测试验证系统失败: {str(e)}",
            "data": None
        }
