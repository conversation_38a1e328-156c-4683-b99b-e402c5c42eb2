#!/bin/bash
# CentOS 7 环境准备脚本
# 安装 AqentCrawler 所需的所有依赖软件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_step "检查系统版本..."
    
    if [ ! -f /etc/redhat-release ]; then
        log_error "此脚本仅支持 CentOS 7"
        exit 1
    fi
    
    version=$(cat /etc/redhat-release | grep -oE '[0-9]+\.[0-9]+' | head -1)
    major_version=$(echo $version | cut -d. -f1)
    
    if [ "$major_version" != "7" ]; then
        log_error "此脚本仅支持 CentOS 7，当前版本: $version"
        exit 1
    fi
    
    log_info "系统版本: CentOS $version"
}

# 更新系统
update_system() {
    log_step "更新系统..."
    
    yum update -y
    yum install -y epel-release
    yum groupinstall -y "Development Tools"
    
    log_info "系统更新完成"
}

# 安装Python 3.8
install_python() {
    log_step "安装Python 3.8..."
    
    # 检查是否已安装
    if command -v python3.8 &> /dev/null; then
        log_info "Python 3.8 已安装"
        return 0
    fi
    
    # 安装Python 3.8
    yum install -y python38 python38-pip python38-devel
    
    # 创建软链接
    ln -sf /usr/bin/python3.8 /usr/bin/python3
    ln -sf /usr/bin/pip3.8 /usr/bin/pip3
    
    # 验证安装
    python3 --version
    pip3 --version
    
    log_info "Python 3.8 安装完成"
}

# 安装Node.js
install_nodejs() {
    log_step "安装Node.js 16..."
    
    # 检查是否已安装
    if command -v node &> /dev/null; then
        node_version=$(node --version | grep -oE '[0-9]+' | head -1)
        if [ "$node_version" -ge 16 ]; then
            log_info "Node.js $node_version 已安装"
            return 0
        fi
    fi
    
    # 安装NodeSource仓库
    curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -
    
    # 安装Node.js
    yum install -y nodejs
    
    # 验证安装
    node --version
    npm --version
    
    log_info "Node.js 安装完成"
}

# 安装MySQL 8.0
install_mysql() {
    log_step "安装MySQL 8.0..."
    
    # 检查是否已安装
    if systemctl is-enabled mysqld &> /dev/null; then
        log_info "MySQL 已安装"
        return 0
    fi
    
    # 下载MySQL仓库
    if [ ! -f mysql80-community-release-el7-3.noarch.rpm ]; then
        wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
    fi
    
    rpm -ivh mysql80-community-release-el7-3.noarch.rpm
    
    # 安装MySQL
    yum install -y mysql-server
    
    # 启动MySQL服务
    systemctl start mysqld
    systemctl enable mysqld
    
    # 获取临时密码
    temp_password=$(grep 'temporary password' /var/log/mysqld.log | awk '{print $NF}')
    
    log_info "MySQL 安装完成"
    log_warn "临时密码: $temp_password"
    log_warn "请运行 mysql_secure_installation 进行安全配置"
}

# 安装Redis
install_redis() {
    log_step "安装Redis..."
    
    # 检查是否已安装
    if systemctl is-enabled redis &> /dev/null; then
        log_info "Redis 已安装"
        return 0
    fi
    
    # 安装Redis
    yum install -y redis
    
    # 启动Redis服务
    systemctl start redis
    systemctl enable redis
    
    # 验证安装
    redis-cli ping
    
    log_info "Redis 安装完成"
}

# 安装Nginx
install_nginx() {
    log_step "安装Nginx..."
    
    # 检查是否已安装
    if systemctl is-enabled nginx &> /dev/null; then
        log_info "Nginx 已安装"
        return 0
    fi
    
    # 安装Nginx
    yum install -y nginx
    
    # 启动Nginx服务
    systemctl start nginx
    systemctl enable nginx
    
    # 配置防火墙
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --reload
    
    log_info "Nginx 安装完成"
}

# 安装Chrome浏览器
install_chrome() {
    log_step "安装Chrome浏览器..."
    
    # 检查是否已安装
    if command -v google-chrome &> /dev/null; then
        log_info "Chrome 已安装"
        return 0
    fi
    
    # 添加Google Chrome仓库
    tee /etc/yum.repos.d/google-chrome.repo <<EOF
[google-chrome]
name=google-chrome
baseurl=http://dl.google.com/linux/chrome/rpm/stable/x86_64
enabled=1
gpgcheck=1
gpgkey=https://dl.google.com/linux/linux_signing_key.pub
EOF
    
    # 安装Chrome
    yum install -y google-chrome-stable
    
    # 安装中文字体支持
    yum install -y wqy-microhei-fonts wqy-zenhei-fonts
    
    # 验证安装
    google-chrome --version
    
    log_info "Chrome 安装完成"
}

# 创建应用用户
create_app_user() {
    log_step "创建应用用户..."
    
    APP_USER="aqentcrawler"
    
    # 检查用户是否已存在
    if id "$APP_USER" &>/dev/null; then
        log_info "用户 $APP_USER 已存在"
        return 0
    fi
    
    # 创建用户
    useradd -m -s /bin/bash "$APP_USER"
    usermod -aG wheel "$APP_USER"
    
    # 创建基础目录
    sudo -u "$APP_USER" mkdir -p "/home/<USER>/logs"
    sudo -u "$APP_USER" mkdir -p "/home/<USER>/backup"
    sudo -u "$APP_USER" mkdir -p "/home/<USER>/chrome_data"
    
    log_info "用户 $APP_USER 创建完成"
    log_warn "请为用户设置密码: passwd $APP_USER"
}

# 配置防火墙
setup_firewall() {
    log_step "配置防火墙..."
    
    # 启动防火墙服务
    systemctl start firewalld
    systemctl enable firewalld
    
    # 开放必要端口
    firewall-cmd --permanent --add-port=8000/tcp  # API端口
    firewall-cmd --permanent --add-port=3306/tcp  # MySQL端口
    firewall-cmd --permanent --add-port=6379/tcp  # Redis端口
    firewall-cmd --reload
    
    log_info "防火墙配置完成"
}

# 优化系统参数
optimize_system() {
    log_step "优化系统参数..."
    
    # 文件描述符限制
    tee -a /etc/security/limits.conf <<EOF
* soft nofile 65536
* hard nofile 65536
aqentcrawler soft nofile 65536
aqentcrawler hard nofile 65536
EOF
    
    # 内核参数优化
    tee -a /etc/sysctl.conf <<EOF
# 网络优化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000

# 内存优化
vm.swappiness = 10
EOF
    
    # 应用内核参数
    sysctl -p
    
    log_info "系统参数优化完成"
}

# 安装额外工具
install_tools() {
    log_step "安装额外工具..."
    
    # 安装常用工具
    yum install -y \
        wget \
        curl \
        vim \
        htop \
        iotop \
        net-tools \
        lsof \
        unzip \
        zip \
        git
    
    log_info "额外工具安装完成"
}

# 验证安装
verify_installation() {
    log_step "验证安装..."
    
    # 检查服务状态
    services=("mysqld" "redis" "nginx" "firewalld")
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log_info "$service 服务运行正常"
        else
            log_warn "$service 服务未运行"
        fi
    done
    
    # 检查命令
    commands=("python3" "pip3" "node" "npm" "mysql" "redis-cli" "nginx" "google-chrome")
    
    for cmd in "${commands[@]}"; do
        if command -v "$cmd" &> /dev/null; then
            log_info "$cmd 命令可用"
        else
            log_warn "$cmd 命令不可用"
        fi
    done
    
    log_info "安装验证完成"
}

# 显示安装结果
show_result() {
    log_step "安装完成！"
    
    echo
    log_info "🎉 CentOS 7 环境准备完成！"
    echo
    log_info "📦 已安装的软件:"
    log_info "  ✅ Python 3.8"
    log_info "  ✅ Node.js 16"
    log_info "  ✅ MySQL 8.0"
    log_info "  ✅ Redis 6.0"
    log_info "  ✅ Nginx"
    log_info "  ✅ Chrome浏览器"
    echo
    log_info "👤 已创建用户: aqentcrawler"
    echo
    log_warn "⚠️  下一步操作:"
    log_warn "1. 为 aqentcrawler 用户设置密码: passwd aqentcrawler"
    log_warn "2. 运行 MySQL 安全配置: mysql_secure_installation"
    log_warn "3. 上传并部署 AqentCrawler 应用"
    echo
    log_info "🔧 服务管理命令:"
    log_info "  MySQL: systemctl start/stop/restart mysqld"
    log_info "  Redis: systemctl start/stop/restart redis"
    log_info "  Nginx: systemctl start/stop/restart nginx"
    echo
    log_info "📝 配置文件位置:"
    log_info "  MySQL: /etc/my.cnf"
    log_info "  Redis: /etc/redis.conf"
    log_info "  Nginx: /etc/nginx/nginx.conf"
}

# 主函数
main() {
    log_info "🚀 开始准备 CentOS 7 环境..."
    
    check_root
    check_system
    update_system
    install_python
    install_nodejs
    install_mysql
    install_redis
    install_nginx
    install_chrome
    create_app_user
    setup_firewall
    optimize_system
    install_tools
    verify_installation
    show_result
    
    log_info "✅ 环境准备完成！"
}

# 错误处理
trap 'log_error "环境准备过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"
