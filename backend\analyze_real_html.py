#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json

def analyze_real_html():
    """分析真实HTML内容，查找价格和重量数据"""
    
    try:
        with open('temp/debug_real_html_content.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print(f"📏 HTML内容长度: {len(html_content)} 字符")
        
        # 查找价格相关字段
        print("\n🔍 查找价格相关字段:")
        price_keywords = [
            'price', 'Price', 'finalPriceModel', 'skuMapOriginal', 
            'offerPriceRanges', 'tradeWithoutPromotion', 'priceModel',
            'originalPricesWithoutPromotion', 'unitPrice', 'salePrice',
            'beginAmount', 'offerBeginAmount', 'skuPrice'
        ]
        
        for keyword in price_keywords:
            matches = list(re.finditer(keyword, html_content, re.IGNORECASE))
            if matches:
                print(f"✅ 找到 {keyword}: {len(matches)} 次")
                # 显示前3个匹配的上下文
                for i, match in enumerate(matches[:3]):
                    start = max(0, match.start() - 100)
                    end = min(len(html_content), match.end() + 100)
                    context = html_content[start:end].replace('\n', ' ').replace('\r', ' ')
                    print(f"    匹配 {i+1}: ...{context}...")
                    print()
            else:
                print(f"❌ 未找到 {keyword}")
        
        # 查找重量相关字段
        print("\n🔍 查找重量相关字段:")
        weight_keywords = [
            'weight', 'Weight', 'unitWeight', 'skuWeight', 'totalCost',
            'freightInfo', 'logistics', 'shipping', 'freight'
        ]
        
        for keyword in weight_keywords:
            matches = list(re.finditer(keyword, html_content, re.IGNORECASE))
            if matches:
                print(f"✅ 找到 {keyword}: {len(matches)} 次")
                # 显示前2个匹配的上下文
                for i, match in enumerate(matches[:2]):
                    start = max(0, match.start() - 100)
                    end = min(len(html_content), match.end() + 100)
                    context = html_content[start:end].replace('\n', ' ').replace('\r', ' ')
                    print(f"    匹配 {i+1}: ...{context}...")
                    print()
            else:
                print(f"❌ 未找到 {keyword}")
        
        # 查找组件类型
        print("\n🔍 查找组件类型:")
        component_pattern = r'"componentType"\s*:\s*"([^"]+)"'
        component_matches = re.findall(component_pattern, html_content)
        if component_matches:
            print(f"✅ 找到 {len(component_matches)} 个组件类型:")
            unique_components = list(set(component_matches))
            for comp in sorted(unique_components):
                print(f"    - {comp}")
        else:
            print("❌ 未找到组件类型")
        
        # 查找包含价格的JSON对象
        print("\n🔍 查找包含价格的JSON对象:")
        price_json_pattern = r'\{[^{}]*"[^"]*[Pp]rice[^"]*"[^{}]*\}'
        price_json_matches = re.findall(price_json_pattern, html_content)
        if price_json_matches:
            print(f"✅ 找到 {len(price_json_matches)} 个包含价格的JSON对象:")
            for i, match in enumerate(price_json_matches[:5]):  # 只显示前5个
                print(f"    JSON {i+1}: {match}")
                print()
        else:
            print("❌ 未找到包含价格的JSON对象")
        
        # 查找数字价格模式
        print("\n🔍 查找数字价格模式:")
        number_price_pattern = r'"[^"]*[Pp]rice[^"]*"\s*:\s*(\d+(?:\.\d+)?)'
        number_price_matches = re.findall(number_price_pattern, html_content)
        if number_price_matches:
            print(f"✅ 找到 {len(number_price_matches)} 个数字价格:")
            unique_prices = list(set(number_price_matches))
            for price in sorted(unique_prices, key=float):
                print(f"    - {price}")
        else:
            print("❌ 未找到数字价格")

        # 提取tradeWithoutPromotion数据
        print("\n🔍 提取tradeWithoutPromotion数据:")
        trade_pattern = r'"tradeWithoutPromotion"\s*:\s*(\{[^{}]*"skuMapOriginal"[^}]*\})'
        trade_matches = re.findall(trade_pattern, html_content)
        if trade_matches:
            print(f"✅ 找到 {len(trade_matches)} 个tradeWithoutPromotion对象:")
            for i, match in enumerate(trade_matches):
                print(f"    对象 {i+1}: {match[:200]}...")
                try:
                    trade_data = json.loads(match)
                    if 'skuMapOriginal' in trade_data:
                        sku_map = trade_data['skuMapOriginal']
                        print(f"        包含 {len(sku_map)} 个SKU价格")
                        for j, sku in enumerate(sku_map[:3]):  # 只显示前3个
                            print(f"        SKU {j+1}: {sku}")
                except json.JSONDecodeError:
                    print(f"        JSON解析失败")
                print()
        else:
            print("❌ 未找到tradeWithoutPromotion数据")

        # 提取freightInfo数据
        print("\n🔍 提取freightInfo数据:")
        freight_pattern = r'"freightInfo"\s*:\s*(\{[^{}]*"unitWeight"[^}]*\})'
        freight_matches = re.findall(freight_pattern, html_content)
        if freight_matches:
            print(f"✅ 找到 {len(freight_matches)} 个freightInfo对象:")
            for i, match in enumerate(freight_matches):
                print(f"    对象 {i+1}: {match}")
                try:
                    freight_data = json.loads(match)
                    print(f"        unitWeight: {freight_data.get('unitWeight', 'N/A')}")
                    print(f"        skuWeight: {freight_data.get('skuWeight', 'N/A')}")
                except json.JSONDecodeError:
                    print(f"        JSON解析失败")
                print()
        else:
            print("❌ 未找到freightInfo数据")

    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")

if __name__ == "__main__":
    analyze_real_html()
