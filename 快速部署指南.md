# AqentCrawler 快速部署指南

## 🚀 一键部署流程

### 1. Windows 开发环境打包

```bash
# 在Windows开发环境中执行
cd AqentCrawler
python scripts/package.py
```

打包完成后会生成：
- `build/aqentcrawler-YYYYMMDD-HHMMSS.zip` - 部署包

### 2. 上传到Linux服务器

```bash
# 使用SCP上传
scp aqentcrawler-YYYYMMDD-HHMMSS.zip root@your-server:/root/

# 或使用FTP/SFTP工具上传
```

### 3. Linux服务器环境准备

```bash
# 登录服务器
ssh root@your-server

# 下载并运行环境准备脚本
wget https://raw.githubusercontent.com/your-repo/AqentCrawler/main/scripts/setup_centos7.sh
chmod +x setup_centos7.sh
./setup_centos7.sh

# 设置用户密码
passwd aqentcrawler

# 配置MySQL安全设置
mysql_secure_installation
```

### 4. 部署应用

```bash
# 切换到应用用户
su - aqentcrawler

# 解压部署包
cd /home/<USER>
unzip /root/aqentcrawler-YYYYMMDD-HHMMSS.zip
cd aqentcrawler-YYYYMMDD-HHMMSS

# 运行部署脚本
chmod +x scripts/deploy_linux.sh
./scripts/deploy_linux.sh
```

### 5. 配置和启动

```bash
# 编辑配置文件
vim /home/<USER>/aqentcrawler/backend/.env

# 重启服务
sudo systemctl restart aqentcrawler

# 检查状态
sudo systemctl status aqentcrawler
```

## ⚙️ 详细配置步骤

### 1. 必须修改的配置项

编辑 `/home/<USER>/aqentcrawler/backend/.env`：

```bash
# 数据库密码（必须修改）
MYSQL_PASSWORD=your_secure_password

# JWT密钥（必须修改为32位随机字符串）
JWT_SECRET_KEY=your-super-secret-jwt-key-32-chars

# 加密密钥（必须修改为32位随机字符串）
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# 管理员密码（建议修改）
ADMIN_PASSWORD=your_secure_admin_password
```

### 2. 生成安全密钥

```bash
# 生成JWT密钥
openssl rand -base64 32

# 生成加密密钥
openssl rand -hex 16
```

### 3. 创建数据库

```bash
# 登录MySQL
mysql -u root -p

# 执行SQL命令
CREATE DATABASE aqentcrawler CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'aqentcrawler'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON aqentcrawler.* TO 'aqentcrawler'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

## 🔍 验证部署

### 1. 检查服务状态

```bash
# 检查应用服务
sudo systemctl status aqentcrawler

# 检查依赖服务
sudo systemctl status mysqld
sudo systemctl status redis
sudo systemctl status nginx
```

### 2. 测试API接口

```bash
# 健康检查
curl http://localhost:8000/health

# API文档
curl http://localhost:8000/docs
```

### 3. 测试前端访问

```bash
# 访问前端
curl http://localhost/

# 获取服务器IP
ip addr show | grep 'inet ' | grep -v '127.0.0.1'
```

## 🌐 访问地址

部署完成后可以通过以下地址访问：

- **前端界面**: `http://your-server-ip/`
- **API文档**: `http://your-server-ip/docs`
- **健康检查**: `http://your-server-ip/health`

## 🔧 常用管理命令

### 服务管理

```bash
# 启动服务
sudo systemctl start aqentcrawler

# 停止服务
sudo systemctl stop aqentcrawler

# 重启服务
sudo systemctl restart aqentcrawler

# 查看状态
sudo systemctl status aqentcrawler

# 查看日志
sudo journalctl -u aqentcrawler -f
```

### 环境管理

```bash
# 切换环境
cd /home/<USER>/aqentcrawler
python3 scripts/env_manager.py prod

# 验证环境
python3 scripts/env_manager.py validate

# 查看环境信息
python3 scripts/env_manager.py info
```

### 数据库管理

```bash
# 备份数据库
mysqldump -u aqentcrawler -p aqentcrawler > backup_$(date +%Y%m%d).sql

# 恢复数据库
mysql -u aqentcrawler -p aqentcrawler < backup_20250120.sql
```

## 🚨 故障排除

### 1. 服务启动失败

```bash
# 查看详细错误
sudo journalctl -u aqentcrawler --no-pager

# 检查配置文件
cd /home/<USER>/aqentcrawler/backend
python3 -c "from app.database import test_connection; test_connection()"
```

### 2. 数据库连接失败

```bash
# 检查MySQL服务
sudo systemctl status mysqld

# 测试连接
mysql -u aqentcrawler -p -h localhost aqentcrawler

# 检查防火墙
sudo firewall-cmd --list-ports
```

### 3. 前端访问失败

```bash
# 检查Nginx配置
sudo nginx -t

# 重载Nginx
sudo systemctl reload nginx

# 检查端口占用
netstat -tulpn | grep :80
```

### 4. Chrome启动失败

```bash
# 检查Chrome
google-chrome --version

# 测试Chrome
google-chrome --headless --no-sandbox --disable-gpu --dump-dom https://www.baidu.com

# 检查权限
ls -la /home/<USER>/chrome_data
```

## 📊 性能优化

### 1. 调整Worker数量

编辑 `/etc/systemd/system/aqentcrawler.service`：

```ini
# 根据CPU核心数调整
ExecStart=/usr/bin/python3 -m gunicorn app.main:app -w 8 -k uvicorn.workers.UvicornWorker --bind 127.0.0.1:8000
```

### 2. 优化数据库

编辑 `/etc/my.cnf`：

```ini
[mysqld]
innodb_buffer_pool_size = 1G
query_cache_size = 256M
max_connections = 200
```

### 3. 优化Redis

编辑 `/etc/redis.conf`：

```ini
maxmemory 512mb
maxmemory-policy allkeys-lru
```

## 🔒 安全配置

### 1. 配置SSL证书

```bash
# 安装Certbot
yum install -y certbot python2-certbot-nginx

# 获取证书
certbot --nginx -d your-domain.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

### 2. 配置防火墙

```bash
# 只开放必要端口
sudo firewall-cmd --permanent --remove-service=ssh
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 📈 监控设置

### 1. 设置定时任务

```bash
# 编辑crontab
crontab -e

# 添加监控任务
*/5 * * * * /home/<USER>/aqentcrawler/scripts/health_check.sh
0 2 * * * /home/<USER>/aqentcrawler/scripts/backup_database.sh
```

### 2. 日志轮转

```bash
# 创建日志轮转配置
sudo tee /etc/logrotate.d/aqentcrawler <<EOF
/home/<USER>/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 aqentcrawler aqentcrawler
}
EOF
```

## 📞 技术支持

如遇到问题，请：

1. 查看日志文件：`/home/<USER>/logs/`
2. 检查服务状态：`sudo systemctl status aqentcrawler`
3. 查看系统日志：`sudo journalctl -u aqentcrawler`
4. 联系技术支持

---

**版本**: v1.0.0  
**更新时间**: 2025-01-20  
**适用系统**: CentOS 7.x
