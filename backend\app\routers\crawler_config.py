"""
爬虫配置管理API路由
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime, timedelta
from sqlalchemy import and_

from app.database import get_db
from app.models import CrawlerAccount, Platform
from app.services.config_service import ConfigService
from app.services.browser_login_service import BrowserLoginService
from app.services.account_monitor_service import account_monitor_service
from app.services.token_refresh_service import token_refresh_service
from app.utils.response import success_response, error_response
from app.utils.crypto import encrypt_password, decrypt_password

router = APIRouter(prefix="/api/v1/crawler", tags=["爬虫配置"])

# 辅助函数：获取平台信息
def get_platform_info(db: Session, platform_id: int):
    """获取平台信息"""
    platform = db.query(Platform).filter(Platform.id == platform_id).first()
    if platform:
        return {
            "id": platform.id,
            "code": platform.code,
            "name": platform.name
        }
    return None


class CrawlerAccountResponse(BaseModel):
    """爬虫账号响应模型"""
    id: int
    platform_id: int
    platform: Optional[dict] = None
    username: str
    display_name: Optional[str] = None
    status: str
    priority: int
    max_requests_per_hour: int
    current_requests_count: int
    success_rate: float
    total_requests: int
    success_requests: int
    error_count: int
    last_error_message: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    last_used_at: Optional[str] = None
    extracted_from: Optional[str] = None
    notes: Optional[str] = None

    class Config:
        from_attributes = True


class CrawlerStatsResponse(BaseModel):
    """爬虫统计响应模型"""
    total_accounts: int
    active_accounts: int
    inactive_accounts: int
    error_accounts: int
    total_requests: int
    total_success: int
    avg_success_rate: float


@router.get("/accounts")
async def get_crawler_accounts(
    platform: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取爬虫账号列表"""
    try:
        query = db.query(CrawlerAccount)

        # 筛选条件
        if platform:
            # 先查找平台ID
            platform_obj = db.query(Platform).filter(Platform.code == platform).first()
            if platform_obj:
                query = query.filter(CrawlerAccount.platform_id == platform_obj.id)
            else:
                query = query.filter(CrawlerAccount.platform_id == -1)  # 不存在的平台
        if status:
            query = query.filter(CrawlerAccount.status == status)
        
        accounts = query.all()
        
        # 转换为响应格式
        result = []
        for account in accounts:
            # 计算Token剩余时间
            token_remaining_time = None
            token_remaining_hours = None
            token_remaining_minutes = None
            token_status = "unknown"

            if hasattr(account, 'token_expires_at') and account.token_expires_at:
                now = datetime.now()
                if account.token_expires_at > now:
                    # Token未过期
                    remaining_delta = account.token_expires_at - now
                    total_seconds = remaining_delta.total_seconds()
                    token_remaining_hours = int(total_seconds // 3600)
                    token_remaining_minutes = int((total_seconds % 3600) // 60)
                    token_remaining_time = f"{token_remaining_hours}小时{token_remaining_minutes}分钟"
                    token_status = "valid"
                else:
                    # Token已过期
                    token_remaining_time = "已过期"
                    token_remaining_hours = 0
                    token_remaining_minutes = 0
                    token_status = "expired"
            else:
                # 没有Token过期时间
                token_remaining_time = "无Token"
                token_status = "no_token"

            account_dict = {
                "id": account.id,
                "platform_id": account.platform_id,
                "platform": get_platform_info(db, account.platform_id),
                "username": account.username,
                "display_name": account.display_name,
                "status": account.status,
                "is_enabled": getattr(account, 'is_enabled', True),  # 兼容旧数据
                "pool_status": account.pool_status,
                "can_use": account.can_use,
                "login_status": getattr(account, 'login_status', 'not_logged_in'),
                "priority": account.priority,
                "max_requests_per_hour": account.max_requests_per_hour,
                "current_requests_count": account.current_requests_count,
                "success_rate": float(account.success_rate or 0),
                "total_requests": account.total_requests or 0,
                "success_requests": account.success_requests or 0,
                "error_count": account.error_count or 0,
                "last_error_message": account.last_error_message,
                "created_at": account.created_at.isoformat() if account.created_at else None,
                "updated_at": account.updated_at.isoformat() if account.updated_at else None,
                "last_used_at": account.last_used_at.isoformat() if account.last_used_at else None,
                "last_login_at": account.last_login_at.isoformat() if hasattr(account, 'last_login_at') and account.last_login_at else None,
                "token_expires_at": account.token_expires_at.isoformat() if hasattr(account, 'token_expires_at') and account.token_expires_at else None,
                "token_remaining_time": token_remaining_time,
                "token_remaining_hours": token_remaining_hours,
                "token_remaining_minutes": token_remaining_minutes,
                "token_status": token_status,
                "extracted_from": account.extracted_from,
                "notes": account.notes
            }
            result.append(account_dict)
        
        return success_response(result)
        
    except Exception as e:
        return error_response(f"获取爬虫账号失败: {str(e)}")


@router.get("/stats")
async def get_crawler_stats(
    platform: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取爬虫统计信息"""
    try:
        config_service = ConfigService(db)
        stats = config_service.get_account_stats(platform)
        
        return success_response(stats)
        
    except Exception as e:
        return error_response(f"获取统计信息失败: {str(e)}")


class CrawlerAccountCreate(BaseModel):
    """创建爬虫账号请求模型"""
    platform_id: int
    username: str
    password: Optional[str] = None  # 明文密码，将在后端加密存储
    display_name: Optional[str] = None
    status: Optional[str] = "active"
    priority: Optional[int] = 1
    max_requests_per_hour: Optional[int] = 100
    notes: Optional[str] = None


@router.post("/accounts")
async def create_crawler_account(
    account_data: CrawlerAccountCreate,
    db: Session = Depends(get_db)
):
    """创建爬虫账号"""
    try:
        # 检查平台是否存在
        platform = db.query(Platform).filter(Platform.id == account_data.platform_id).first()
        if not platform:
            return error_response("平台不存在")

        # 检查用户名是否已存在
        existing = db.query(CrawlerAccount).filter(
            CrawlerAccount.platform_id == account_data.platform_id,
            CrawlerAccount.username == account_data.username
        ).first()
        if existing:
            return error_response("该平台下用户名已存在")

        # 加密密码（如果提供了密码）
        encrypted_password = None
        if account_data.password:
            encrypted_password = encrypt_password(account_data.password)
            print(f"[CREATE_ACCOUNT] 密码已加密存储")

        # 创建新账号
        new_account = CrawlerAccount(
            platform_id=account_data.platform_id,
            username=account_data.username,
            password=encrypted_password,
            display_name=account_data.display_name,
            status=account_data.status,
            priority=account_data.priority,
            max_requests_per_hour=account_data.max_requests_per_hour,
            notes=account_data.notes,
            login_status='pending'
        )

        db.add(new_account)
        db.commit()
        db.refresh(new_account)

        return success_response({
            "id": new_account.id,
            "message": "账号创建成功"
        })

    except Exception as e:
        db.rollback()
        return error_response(f"创建账号失败: {str(e)}")



@router.delete("/accounts/{account_id}")
async def delete_crawler_account(
    account_id: int,
    db: Session = Depends(get_db)
):
    """删除爬虫账号"""
    try:
        # 获取账号
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        if not account:
            return error_response("账号不存在")
        
        # 删除账号
        db.delete(account)
        db.commit()
        
        return success_response({
            "account_id": account_id,
            "message": "账号删除成功"
        })
        
    except Exception as e:
        db.rollback()
        return error_response(f"删除账号失败: {str(e)}")


@router.post("/accounts/{account_id}/toggle")
async def toggle_crawler_account(account_id: int, db: Session = Depends(get_db)):
    """切换爬虫账号启用状态"""
    try:
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        if not account:
            return error_response("账号不存在")

        # 切换启用状态
        account.is_enabled = not account.is_enabled
        account.updated_at = datetime.now()
        db.commit()

        status_text = "启用" if account.is_enabled else "禁用"

        # 返回更新后的状态信息
        toggle_data = {
            "account_id": account_id,
            "is_enabled": account.is_enabled,
            "pool_status": account.pool_status,
            "can_use": account.can_use
        }

        return success_response(toggle_data, f"账号已{status_text}")

    except Exception as e:
        db.rollback()
        return error_response(f"切换账号状态失败: {str(e)}")


@router.get("/accounts/{account_id}")
async def get_crawler_account(
    account_id: int,
    db: Session = Depends(get_db)
):
    """获取单个爬虫账号详情"""
    try:
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        if not account:
            return error_response("账号不存在")
        
        # 解密密码（如果存在）
        decrypted_password = None
        if account.password:
            try:
                decrypted_password = decrypt_password(account.password)
                print(f"[GET_ACCOUNT] 密码解密成功，长度: {len(decrypted_password) if decrypted_password else 0}")
            except Exception as e:
                print(f"[GET_ACCOUNT] 密码解密失败: {str(e)}")
                decrypted_password = ""

        account_dict = {
            "id": account.id,
            "platform_id": account.platform_id,
            "platform": get_platform_info(db, account.platform_id),
            "username": account.username,
            "password": decrypted_password,  # 返回解密后的密码
            "display_name": account.display_name,
            "cookie": account.cookie,  # 详情页面显示完整信息
            "token": account.token,
            "user_agent": account.user_agent,
            "proxy_id": account.proxy_id,  # 添加代理ID字段
            "status": account.status,
            "priority": account.priority,
            "max_requests_per_hour": account.max_requests_per_hour,
            "current_requests_count": account.current_requests_count,
            "success_rate": float(account.success_rate or 0),
            "total_requests": account.total_requests or 0,
            "success_requests": account.success_requests or 0,
            "error_count": account.error_count or 0,
            "last_error_message": account.last_error_message,
            "created_at": account.created_at.isoformat() if account.created_at else None,
            "updated_at": account.updated_at.isoformat() if account.updated_at else None,
            "last_used_at": account.last_used_at.isoformat() if account.last_used_at else None,
            "extracted_from": account.extracted_from,
            "notes": account.notes
        }
        
        return success_response(account_dict)
        
    except Exception as e:
        return error_response(f"获取账号详情失败: {str(e)}")


@router.put("/accounts/{account_id}")
async def update_crawler_account(
    account_id: int,
    account_data: dict,
    db: Session = Depends(get_db)
):
    """更新爬虫账号"""
    try:
        # 获取账号
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        if not account:
            return error_response("账号不存在")
        
        # 更新字段
        print(f"[UPDATE_ACCOUNT] 收到更新数据: {account_data}")

        # 特殊处理的字段
        special_fields = {
            'proxy_id': lambda v: None if v == '' or v == 'null' or v == 'undefined' else v,
            'password': lambda v: encrypt_password(v) if v and v.strip() else account.password
        }

        for key, value in account_data.items():
            if hasattr(account, key):
                # 跳过不应该更新的字段
                if key in ['id', 'created_at', 'updated_at']:
                    continue

                # 特殊字段处理
                if key in special_fields:
                    processed_value = special_fields[key](value)
                    print(f"[UPDATE_ACCOUNT] 字段 {key}: {value} -> {processed_value}")
                    setattr(account, key, processed_value)
                # 普通字段处理
                elif value is not None:
                    setattr(account, key, value)
                    print(f"[UPDATE_ACCOUNT] 更新字段 {key}: {value}")

        # 更新时间戳
        account.updated_at = datetime.now()
        
        db.commit()
        
        return success_response({
            "account_id": account_id,
            "message": "账号更新成功"
        })
        
    except Exception as e:
        db.rollback()
        return error_response(f"更新账号失败: {str(e)}")


@router.post("/reset-hourly-limits")
async def reset_hourly_limits(db: Session = Depends(get_db)):
    """重置每小时请求限制"""
    try:
        config_service = ConfigService(db)
        config_service.reset_hourly_limits()
        
        return success_response({
            "message": "每小时限制重置成功"
        })
        
    except Exception as e:
        return error_response(f"重置失败: {str(e)}")


@router.post("/cleanup-expired")
async def cleanup_expired_accounts(db: Session = Depends(get_db)):
    """清理过期账号"""
    try:
        config_service = ConfigService(db)
        config_service.cleanup_expired_accounts()
        
        return success_response({
            "message": "过期账号清理完成"
        })
        
    except Exception as e:
        return error_response(f"清理失败: {str(e)}")


class AccountLoginRequest(BaseModel):
    """账号登录请求模型"""
    username: str
    platform_id: int
    proxy_id: Optional[int] = None
    auto_refresh_enabled: bool = True
    force_relogin: bool = False  # 强制重新登录，即使Token有效


@router.post("/accounts/login")
async def login_account(request: AccountLoginRequest, db: Session = Depends(get_db)):
    """账号自动登录"""
    account_id = None
    try:
        print(f"[LOGIN] 收到登录请求: username={request.username}, platform_id={request.platform_id}")

        # 验证平台是否存在
        platform = db.query(Platform).filter(Platform.id == request.platform_id).first()
        if not platform:
            return error_response("指定的平台不存在")

        print(f"[LOGIN] 平台验证通过: {platform.name} ({platform.code})")

        # 检查账号是否已存在
        existing_account = db.query(CrawlerAccount).filter(
            CrawlerAccount.platform_id == request.platform_id,
            CrawlerAccount.username == request.username
        ).first()

        if existing_account:
            print(f"[LOGIN] 找到现有账号，ID: {existing_account.id}")
            # 检查账号当前状态
            if existing_account.login_status == 'logged_in' and not request.force_relogin:
                # 检查token是否仍然有效
                if existing_account.token_expires_at and existing_account.token_expires_at > datetime.now():
                    hours_left = (existing_account.token_expires_at - datetime.now()).total_seconds() / 3600
                    print(f"[LOGIN] 账号已登录且Token有效，剩余 {hours_left:.1f} 小时")
                    return success_response({
                        "success": True,
                        "message": f"账号已登录且Token有效，剩余 {hours_left:.1f} 小时。如需重新登录，请使用强制登录选项。",
                        "data": {
                            "account_id": existing_account.id,
                            "login_status": "already_logged_in",
                            "token_expires_at": existing_account.token_expires_at.isoformat(),
                            "hours_left": round(hours_left, 1)
                        }
                    })

            if request.force_relogin:
                print(f"[LOGIN] 强制重新登录模式，忽略现有Token状态")

            # 更新现有账号配置
            existing_account.proxy_id = request.proxy_id
            existing_account.auto_refresh_enabled = request.auto_refresh_enabled
            existing_account.login_status = 'pending'
            existing_account.updated_at = datetime.now()
            db.commit()
            account_id = existing_account.id
        else:
            print(f"[LOGIN] 创建新账号")
            # 创建新账号
            new_account = CrawlerAccount(
                platform_id=request.platform_id,
                username=request.username,
                proxy_id=request.proxy_id,
                auto_refresh_enabled=request.auto_refresh_enabled,
                login_status='pending',
                status='active'
            )
            db.add(new_account)
            db.commit()
            db.refresh(new_account)
            account_id = new_account.id
            print(f"[LOGIN] 新账号创建成功，ID: {account_id}")

        # 更新登录状态为进行中
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        account.login_status = 'logging_in'
        account.last_login_at = datetime.now()
        db.commit()

        # 执行自动登录
        print(f"[LOGIN] 开始调用登录服务, account_id={account_id}")
        login_service = BrowserLoginService(db)
        result = await login_service.login_account(account_id)
        print(f"[LOGIN] 登录服务返回结果: {result}")

        # 更新最终登录状态
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        if result.get("success"):
            account.login_status = 'logged_in'
            account.last_error_message = None
        else:
            account.login_status = 'error'
            account.last_error_message = result.get("message", "登录失败")

        account.updated_at = datetime.now()
        db.commit()

        return success_response(result)

    except Exception as e:
        print(f"[LOGIN] 登录过程发生异常: {str(e)}")
        print(f"[LOGIN] 异常类型: {type(e).__name__}")
        import traceback
        print(f"[LOGIN] 详细错误信息:\n{traceback.format_exc()}")

        # 更新账号状态为错误
        if account_id:
            try:
                # 重新获取数据库会话，避免事务问题
                db.rollback()  # 先回滚当前事务
                account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
                if account:
                    # 确保使用有效的状态值
                    account.login_status = 'login_failed'  # 使用数据库中定义的有效值
                    account.last_error_message = str(e)[:1000]  # 限制错误信息长度
                    account.updated_at = datetime.now()
                    db.commit()
                    print(f"[LOGIN] 账号状态已更新为 login_failed")
            except Exception as update_error:
                print(f"[LOGIN] 更新账号状态失败: {str(update_error)}")
                try:
                    db.rollback()
                except:
                    pass

        return error_response(f"账号登录失败: {str(e)}")


class TokenRefreshRequest(BaseModel):
    """Token刷新请求模型"""
    username: str
    platform_id: int
    proxy_id: Optional[int] = None


@router.post("/accounts/refresh-token")
async def refresh_account_token(request: TokenRefreshRequest, db: Session = Depends(get_db)):
    """刷新账号Token"""
    try:
        print(f"[TOKEN_REFRESH] 收到Token刷新请求: username={request.username}, platform_id={request.platform_id}")

        # 验证平台是否存在
        platform = db.query(Platform).filter(Platform.id == request.platform_id).first()
        if not platform:
            return error_response("指定的平台不存在")

        print(f"[TOKEN_REFRESH] 平台验证通过: {platform.name} ({platform.code})")

        # 查找账号
        account = db.query(CrawlerAccount).filter(
            CrawlerAccount.platform_id == request.platform_id,
            CrawlerAccount.username == request.username
        ).first()

        if not account:
            # 如果账号不存在，创建新账号
            print(f"[TOKEN_REFRESH] 创建新账号")
            new_account = CrawlerAccount(
                platform_id=request.platform_id,
                username=request.username,
                proxy_id=request.proxy_id,
                login_status='pending',
                status='active'
            )
            db.add(new_account)
            db.commit()
            db.refresh(new_account)
            account = new_account
            print(f"[TOKEN_REFRESH] 新账号创建成功，ID: {account.id}")

        print(f"[TOKEN_REFRESH] 找到账号，ID: {account.id}")

        # 更新账号状态为刷新中
        account.login_status = 'refreshing'
        account.updated_at = datetime.now()
        db.commit()

        # 执行Token刷新
        print(f"[TOKEN_REFRESH] 开始调用Token刷新服务, account_id={account.id}")
        result = token_refresh_service.refresh_account_token_sync(account.id)
        print(f"[TOKEN_REFRESH] Token刷新服务返回结果: {result}")

        # 更新最终状态
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account.id).first()
        if result.get("success"):
            account.login_status = 'logged_in'
            account.last_error_message = None
            account.updated_at = datetime.now()
            db.commit()
        elif result.get("cancelled") or result.get("timeout"):
            # 用户取消或超时，恢复到原来的状态，不记录错误
            account.login_status = 'logged_in' if account.login_status == 'refreshing' else account.login_status
            account.updated_at = datetime.now()
            db.commit()
        else:
            # 真正的错误才记录
            account.login_status = 'error'
            account.last_error_message = result.get("message", "Token刷新失败")
            account.updated_at = datetime.now()
            db.commit()

        return success_response(result)

    except Exception as e:
        print(f"[TOKEN_REFRESH] Token刷新过程发生异常: {str(e)}")
        import traceback
        print(f"[TOKEN_REFRESH] 详细错误信息:\n{traceback.format_exc()}")

        # 更新账号状态为错误（只有在真正异常时才更新）
        try:
            db.rollback()
            account = db.query(CrawlerAccount).filter(
                CrawlerAccount.platform_id == request.platform_id,
                CrawlerAccount.username == request.username
            ).first()
            if account:
                # 只有在非用户取消的情况下才记录错误
                if "操作被取消" not in str(e) and "超时" not in str(e):
                    account.login_status = 'error'
                    account.last_error_message = str(e)[:1000]
                else:
                    # 用户取消或超时，恢复原状态
                    account.login_status = 'logged_in' if account.login_status == 'refreshing' else account.login_status
                account.updated_at = datetime.now()
                db.commit()
        except Exception as update_error:
            print(f"[TOKEN_REFRESH] 更新账号状态失败: {str(update_error)}")

        return error_response(f"Token刷新失败: {str(e)}")


@router.get("/accounts/{account_id}/login-status")
async def get_account_login_status(account_id: int, db: Session = Depends(get_db)):
    """获取账号详细登录状态"""
    try:
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        if not account:
            return error_response("账号不存在")

        # 计算token状态
        token_status = "none"
        token_expires_in_hours = None

        if account.token:
            if account.token_expires_at:
                now = datetime.now()
                if account.token_expires_at > now:
                    token_expires_in_hours = (account.token_expires_at - now).total_seconds() / 3600
                    if token_expires_in_hours <= 2:
                        token_status = "expiring_soon"
                    else:
                        token_status = "valid"
                else:
                    token_status = "expired"
            else:
                token_status = "valid"  # 没有过期时间的token认为有效

        status_info = {
            "account_id": account.id,
            "platform": get_platform_info(db, account.platform_id),
            "username": account.username,
            "login_status": account.login_status,
            "last_login_at": account.last_login_at.isoformat() if account.last_login_at else None,
            "token_status": token_status,
            "token_expires_at": account.token_expires_at.isoformat() if account.token_expires_at else None,
            "token_expires_in_hours": round(token_expires_in_hours, 2) if token_expires_in_hours else None,
            "auto_refresh_enabled": account.auto_refresh_enabled,
            "has_token": bool(account.token),
            "has_cookie": bool(account.cookie),
            "cookie_count": len(account.cookie.split(';')) if account.cookie else 0,
            "last_error_message": account.last_error_message,
            "success_rate": float(account.success_rate or 0),
            "total_requests": account.total_requests or 0,
            "last_used_at": account.last_used_at.isoformat() if account.last_used_at else None
        }

        return success_response(status_info)

    except Exception as e:
        return error_response(f"获取登录状态失败: {str(e)}")


@router.get("/accounts/login-status")
async def get_all_accounts_login_status(
    platform: Optional[str] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取所有账号的登录状态概览"""
    try:
        query = db.query(CrawlerAccount)

        # 筛选条件
        if platform:
            # 先查找平台ID
            platform_obj = db.query(Platform).filter(Platform.code == platform).first()
            if platform_obj:
                query = query.filter(CrawlerAccount.platform_id == platform_obj.id)
            else:
                query = query.filter(CrawlerAccount.platform_id == -1)  # 不存在的平台
        if status:
            query = query.filter(CrawlerAccount.login_status == status)

        accounts = query.all()

        # 统计信息
        total_accounts = len(accounts)
        logged_in_count = sum(1 for acc in accounts if acc.login_status == 'logged_in')
        error_count = sum(1 for acc in accounts if acc.login_status in ['login_failed', 'error'])
        pending_count = sum(1 for acc in accounts if acc.login_status == 'pending')

        # 即将过期的token数量
        now = datetime.now()
        expiring_soon_count = 0
        for acc in accounts:
            if acc.token_expires_at and acc.token_expires_at > now:
                hours_left = (acc.token_expires_at - now).total_seconds() / 3600
                if hours_left <= 2:
                    expiring_soon_count += 1

        status_overview = {
            "total_accounts": total_accounts,
            "logged_in_count": logged_in_count,
            "error_count": error_count,
            "pending_count": pending_count,
            "expiring_soon_count": expiring_soon_count,
            "accounts": [
                {
                    "id": acc.id,
                    "username": acc.username,
                    "platform_code": get_platform_info(db, acc.platform_id)["code"] if get_platform_info(db, acc.platform_id) else "unknown",
                    "login_status": acc.login_status,
                    "has_token": bool(acc.token),
                    "token_expires_at": acc.token_expires_at.isoformat() if acc.token_expires_at else None,
                    "last_error_message": acc.last_error_message
                }
                for acc in accounts
            ]
        }

        return success_response(status_overview)

    except Exception as e:
        return error_response(f"获取登录状态概览失败: {str(e)}")


class BatchLoginRequest(BaseModel):
    """批量登录请求模型"""
    account_ids: List[int]
    auto_refresh_enabled: bool = True


@router.post("/accounts/batch-login")
async def batch_login_accounts(request: BatchLoginRequest, db: Session = Depends(get_db)):
    """批量账号登录"""
    try:
        if not request.account_ids:
            return error_response("账号ID列表不能为空")

        if len(request.account_ids) > 10:
            return error_response("单次批量登录最多支持10个账号")

        # 验证所有账号是否存在
        accounts = db.query(CrawlerAccount).filter(CrawlerAccount.id.in_(request.account_ids)).all()
        found_ids = {acc.id for acc in accounts}
        missing_ids = set(request.account_ids) - found_ids

        if missing_ids:
            return error_response(f"以下账号ID不存在: {list(missing_ids)}")

        # 更新所有账号状态为待登录
        for account in accounts:
            account.login_status = 'pending'
            account.auto_refresh_enabled = request.auto_refresh_enabled
            account.updated_at = datetime.now()
        db.commit()

        # 执行批量登录（串行执行，避免资源冲突）
        login_service = BrowserLoginService(db)
        results = []

        for account in accounts:
            try:
                print(f"[BATCH_LOGIN] 开始登录账号: {account.username} (ID: {account.id})")
                account.login_status = 'logging_in'
                db.commit()

                result = await login_service.login_account(account.id)

                # 更新账号状态
                if result.get("success"):
                    account.login_status = 'logged_in'
                    account.last_error_message = None
                else:
                    account.login_status = 'login_failed'
                    account.last_error_message = result.get("message", "登录失败")

                account.updated_at = datetime.now()
                db.commit()

                results.append({
                    "account_id": account.id,
                    "username": account.username,
                    "success": result.get("success", False),
                    "message": result.get("message", "未知错误")
                })

                print(f"[BATCH_LOGIN] 账号 {account.username} 登录完成: {result.get('success')}")

            except Exception as e:
                print(f"[BATCH_LOGIN] 账号 {account.username} 登录异常: {str(e)}")
                account.login_status = 'login_failed'
                account.last_error_message = str(e)[:1000]  # 限制错误信息长度
                account.updated_at = datetime.now()
                try:
                    db.commit()
                except Exception as commit_error:
                    print(f"[BATCH_LOGIN] 提交数据库失败: {str(commit_error)}")
                    db.rollback()

                results.append({
                    "account_id": account.id,
                    "username": account.username,
                    "success": False,
                    "message": str(e)
                })

        # 统计结果
        success_count = sum(1 for r in results if r["success"])
        total_count = len(results)

        return success_response({
            "total_accounts": total_count,
            "success_count": success_count,
            "failed_count": total_count - success_count,
            "results": results
        })

    except Exception as e:
        print(f"[BATCH_LOGIN] 批量登录异常: {str(e)}")
        db.rollback()
        return error_response(f"批量登录失败: {str(e)}")





# ==================== 监控相关接口 ====================

@router.get("/monitor/status")
async def get_monitor_status():
    """获取监控服务状态"""
    try:
        status = await account_monitor_service.get_monitor_status()
        return success_response(status)
    except Exception as e:
        return error_response(f"获取监控状态失败: {str(e)}")


@router.post("/monitor/start")
async def start_monitor():
    """启动监控服务"""
    try:
        if account_monitor_service.running:
            return success_response({"message": "监控服务已在运行中"})

        # 在后台启动监控服务
        import asyncio
        asyncio.create_task(account_monitor_service.start_monitoring())

        return success_response({"message": "监控服务启动成功"})
    except Exception as e:
        return error_response(f"启动监控服务失败: {str(e)}")


@router.post("/monitor/stop")
async def stop_monitor():
    """停止监控服务"""
    try:
        account_monitor_service.stop_monitoring()
        return success_response({"message": "监控服务停止成功"})
    except Exception as e:
        return error_response(f"停止监控服务失败: {str(e)}")


class MonitorSettingsUpdate(BaseModel):
    """监控设置更新模型"""
    monitor_interval: Optional[int] = None
    token_warning_hours: Optional[int] = None
    auto_refresh_advance_hours: Optional[int] = None
    health_check_interval: Optional[int] = None


@router.put("/monitor/settings")
async def update_monitor_settings(settings: MonitorSettingsUpdate):
    """更新监控设置"""
    try:
        if settings.monitor_interval is not None:
            account_monitor_service.monitor_interval = settings.monitor_interval
        if settings.token_warning_hours is not None:
            account_monitor_service.token_warning_hours = settings.token_warning_hours
        if settings.auto_refresh_advance_hours is not None:
            account_monitor_service.auto_refresh_advance_hours = settings.auto_refresh_advance_hours
        if settings.health_check_interval is not None:
            account_monitor_service.health_check_interval = settings.health_check_interval

        return success_response({"message": "监控设置更新成功"})
    except Exception as e:
        return error_response(f"更新监控设置失败: {str(e)}")



