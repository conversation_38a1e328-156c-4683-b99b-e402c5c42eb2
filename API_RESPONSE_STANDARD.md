# AqentCrawler API 响应格式标准

## 📋 统一响应格式规范

### 🎯 标准响应格式

所有API端点必须严格遵循以下响应格式：

```json
{
  "code": 200,           // 业务状态码 (200=成功, 400=客户端错误, 500=服务器错误)
  "message": "操作成功",  // 响应消息
  "data": {}             // 实际数据 (可以是对象、数组或null)
}
```

### ✅ 成功响应示例

#### 1. 列表数据
```json
{
  "code": 200,
  "message": "获取平台列表成功",
  "data": [
    {
      "id": 1,
      "name": "淘宝",
      "code": "taobao"
    }
  ]
}
```

#### 2. 单个对象
```json
{
  "code": 200,
  "message": "获取平台详情成功",
  "data": {
    "id": 1,
    "name": "淘宝",
    "code": "taobao"
  }
}
```

#### 3. 操作结果
```json
{
  "code": 200,
  "message": "平台创建成功",
  "data": {
    "id": 5,
    "name": "新平台"
  }
}
```

#### 4. 删除操作
```json
{
  "code": 200,
  "message": "平台删除成功",
  "data": null
}
```

### ❌ 错误响应示例

#### 1. 客户端错误 (400)
```json
{
  "code": 400,
  "message": "平台代码已存在",
  "data": null
}
```

#### 2. 服务器错误 (500)
```json
{
  "code": 500,
  "message": "数据库连接失败",
  "data": null
}
```

### 🔧 前端调用规范

前端必须按以下方式处理响应：

```javascript
// 正确的调用方式
const response = await api.platforms.getAll()
if (response.data.code === 200) {
  const platforms = response.data.data  // 获取实际数据
  console.log('成功:', response.data.message)
} else {
  console.error('错误:', response.data.message)
}
```

### 📝 实施要求

1. **后端要求**：
   - 所有API端点必须返回标准格式
   - 不允许直接返回数组或对象
   - 必须包含 code, message, data 三个字段

2. **前端要求**：
   - 必须检查 response.data.code === 200
   - 从 response.data.data 获取实际数据
   - 显示 response.data.message 作为用户提示

3. **禁止事项**：
   - 禁止随意修改响应格式
   - 禁止省略任何必需字段
   - 禁止在不同端点使用不同格式

### 🚨 重要提醒

此标准一旦确定，严禁随意修改！所有开发人员必须严格遵守此规范。
