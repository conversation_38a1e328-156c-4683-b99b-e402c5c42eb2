#!/usr/bin/env python3
"""
扩展代理池表结构
添加代理服务器详细信息和第三方API支持
"""

import os
import sys
import pymysql
from datetime import datetime

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, backend_dir)

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host='*************',
        port=3306,
        user='root',
        password='123456',
        database='agent_crawler',
        charset='utf8mb4'
    )

def extend_proxy_pools_table():
    """扩展proxy_pools表结构"""
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            print("开始扩展proxy_pools表结构...")
            
            # 1. 添加代理服务器基本信息字段
            alter_statements = [
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN host VARCHAR(255) NULL COMMENT '代理服务器地址' 
                AFTER provider_name
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN port INT NULL COMMENT '代理端口' 
                AFTER host
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN username VARCHAR(100) NULL COMMENT '代理用户名' 
                AFTER port
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN password VARCHAR(255) NULL COMMENT '代理密码（加密存储）' 
                AFTER username
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN proxy_source ENUM('self_hosted', 'third_party_api', 'static') 
                DEFAULT 'static' COMMENT '代理来源类型' 
                AFTER proxy_type
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN api_config JSON NULL COMMENT '第三方API配置' 
                AFTER proxy_source
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN test_url VARCHAR(255) 
                DEFAULT 'https://httpbin.org/ip' COMMENT '测试URL' 
                AFTER api_config
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN country VARCHAR(50) NULL COMMENT '代理所在国家' 
                AFTER test_url
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN region VARCHAR(100) NULL COMMENT '代理所在地区' 
                AFTER country
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN last_test_at TIMESTAMP NULL COMMENT '最后测试时间' 
                AFTER last_checked_at
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN last_success_at TIMESTAMP NULL COMMENT '最后成功时间' 
                AFTER last_test_at
                """,
                """
                ALTER TABLE proxy_pools 
                ADD COLUMN consecutive_failures INT DEFAULT 0 COMMENT '连续失败次数' 
                AFTER last_success_at
                """
            ]
            
            for i, statement in enumerate(alter_statements, 1):
                try:
                    cursor.execute(statement)
                    print(f"✓ 执行第{i}个ALTER语句成功")
                except pymysql.Error as e:
                    if "Duplicate column name" in str(e):
                        print(f"⚠ 第{i}个字段已存在，跳过")
                    else:
                        print(f"✗ 执行第{i}个ALTER语句失败: {e}")
                        raise
            
            # 2. 添加新的索引
            index_statements = [
                """
                CREATE INDEX idx_host_port ON proxy_pools(host, port)
                """,
                """
                CREATE INDEX idx_proxy_source ON proxy_pools(proxy_source)
                """,
                """
                CREATE INDEX idx_last_test ON proxy_pools(last_test_at)
                """,
                """
                CREATE INDEX idx_consecutive_failures ON proxy_pools(consecutive_failures)
                """
            ]
            
            for i, statement in enumerate(index_statements, 1):
                try:
                    cursor.execute(statement)
                    print(f"✓ 创建第{i}个索引成功")
                except pymysql.Error as e:
                    if "Duplicate key name" in str(e):
                        print(f"⚠ 第{i}个索引已存在，跳过")
                    else:
                        print(f"✗ 创建第{i}个索引失败: {e}")
            
            connection.commit()
            print("✓ proxy_pools表结构扩展完成")
            
    except Exception as e:
        connection.rollback()
        print(f"✗ 扩展proxy_pools表失败: {e}")
        raise
    finally:
        connection.close()

def create_proxy_test_logs_table():
    """创建代理测试日志表"""
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            print("开始创建proxy_test_logs表...")
            
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS proxy_test_logs (
                id BIGINT PRIMARY KEY AUTO_INCREMENT,
                proxy_id BIGINT NOT NULL COMMENT '代理ID',
                test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '测试时间',
                test_result ENUM('success', 'failed', 'timeout') NOT NULL COMMENT '测试结果',
                response_time_ms INT NULL COMMENT '响应时间（毫秒）',
                error_message TEXT NULL COMMENT '错误信息',
                test_ip VARCHAR(45) NULL COMMENT '测试获得的IP地址',
                test_url VARCHAR(255) NULL COMMENT '测试使用的URL',
                user_agent VARCHAR(500) NULL COMMENT '测试使用的User-Agent',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_proxy_time (proxy_id, test_time),
                INDEX idx_test_result (test_result),
                INDEX idx_test_time (test_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
            COMMENT='代理测试日志表'
            """
            
            cursor.execute(create_table_sql)
            connection.commit()
            print("✓ proxy_test_logs表创建完成")
            
    except Exception as e:
        connection.rollback()
        print(f"✗ 创建proxy_test_logs表失败: {e}")
        raise
    finally:
        connection.close()

def main():
    """主函数"""
    print("=" * 50)
    print("代理池表结构扩展脚本")
    print("=" * 50)
    
    try:
        # 1. 扩展proxy_pools表
        extend_proxy_pools_table()
        
        # 2. 创建proxy_test_logs表
        create_proxy_test_logs_table()
        
        print("\n" + "=" * 50)
        print("✓ 所有数据库结构更新完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n✗ 数据库结构更新失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
