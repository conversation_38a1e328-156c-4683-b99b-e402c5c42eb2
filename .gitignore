# 忽略所有 IDE 自动生成的文件和目录
*.pyc
__pycache__/
.vscode/
.idea/

# 忽略 Python 虚拟环境和相关文件
venv/
env/
*.egg-info/
dist/
build/
*.egg
temp/

# 忽略 Node.js 相关文件和目录
frontend/node_modules/
package-lock.json

# 忽略数据库文件和日志文件
agent_crawler.db
*.log

# 忽略环境变量配置文件
# .env

# 忽略测试数据和临时文件
# test_frontend_data.py
# test_final_frontend.py
# test_frontend_pages.py

# 忽略 Docker 相关文件（如果需要保留 docker-compose.yml，则移除该行）
.dockerignore
.dockerenv

# 忽略其他特定文件和目录
*.DS_Store
Thumbs.db
*.tmp
*.swp
*.swo
*.swn
*.bak