#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试服务器状态
"""

import requests

def test_server():
    """测试服务器状态"""
    try:
        # 测试根路径
        response = requests.get("http://localhost:8000/")
        print(f"根路径状态: {response.status_code}")
        
        # 测试docs路径
        response = requests.get("http://localhost:8000/docs")
        print(f"文档路径状态: {response.status_code}")
        
        # 测试API路径
        response = requests.get("http://localhost:8000/api/v1/")
        print(f"API路径状态: {response.status_code}")
        
        # 测试上游API路径
        response = requests.get("http://localhost:8000/api/v1/upstream/")
        print(f"上游API路径状态: {response.status_code}")

        # 测试商品详情API
        data = {
            "product_url": "https://detail.1688.com/offer/932222752479.html",
            "language": "zh"
        }
        response = requests.post("http://localhost:8000/api/v1/upstream/product/detail", json=data)
        print(f"商品详情API状态: {response.status_code}")
        if response.status_code != 200:
            print(f"响应内容: {response.text}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    test_server()
