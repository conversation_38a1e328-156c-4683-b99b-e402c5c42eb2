<template>
  <div class="proxies-page">
    <div class="page-header">
      <h2>代理管理</h2>
      <div class="header-actions">
        <button @click="batchTest" class="btn-secondary" :disabled="batchTesting">
          {{ batchTesting ? '批量测试中...' : '🧪 批量测试' }}
        </button>
        <button @click="showAddModal = true" class="btn-primary">
          ➕ 添加代理
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <h3>总代理数</h3>
        <div class="stat-value">{{ stats.total_proxies }}</div>
      </div>
      <div class="stat-card">
        <h3>活跃代理</h3>
        <div class="stat-value">{{ stats.active_proxies }}</div>
      </div>
      <div class="stat-card">
        <h3>平均成功率</h3>
        <div class="stat-value">{{ stats.avg_success_rate }}%</div>
      </div>
      <div class="stat-card">
        <h3>平均响应时间</h3>
        <div class="stat-value">{{ stats.avg_response_time }}ms</div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-item">
        <label>状态:</label>
        <select v-model="filters.status" @change="loadProxies">
          <option value="">全部状态</option>
          <option value="active">活跃</option>
          <option value="inactive">非活跃</option>
          <option value="error">错误</option>
          <option value="testing">测试中</option>
        </select>
      </div>
      <div class="filter-item">
        <label>类型:</label>
        <select v-model="filters.proxy_type" @change="loadProxies">
          <option value="">全部类型</option>
          <option value="residential">住宅代理</option>
          <option value="datacenter">数据中心</option>
          <option value="mobile">移动代理</option>
        </select>
      </div>
      <div class="filter-item">
        <label>来源:</label>
        <select v-model="filters.proxy_source" @change="loadProxies">
          <option value="">全部来源</option>
          <option value="self_hosted">自建服务器</option>
          <option value="third_party_api">第三方API</option>
          <option value="static">静态配置</option>
        </select>
      </div>
      <div class="filter-item">
        <label>
          <input type="checkbox" v-model="filters.enabled_only" @change="loadProxies">
          仅显示启用的
        </label>
      </div>
      <div class="filter-item">
        <button @click="refreshProxies" class="btn-secondary">🔄 刷新</button>
      </div>
    </div>

    <!-- 代理列表 -->
    <div class="proxies-table">
      <div class="table-header">
        <div class="col">ID</div>
        <div class="col">提供商</div>
        <div class="col">地址</div>
        <div class="col">类型</div>
        <div class="col">来源</div>
        <div class="col">状态</div>
        <div class="col">成功率</div>
        <div class="col">响应时间</div>
        <div class="col">连续失败</div>
        <div class="col">优先级</div>
        <div class="col">启用状态</div>
        <div class="col">最后测试</div>
        <div class="col">操作</div>
      </div>

      <div v-if="loading" class="loading">加载中...</div>

      <div v-else-if="proxies.length === 0" class="empty">
        暂无代理数据
      </div>

      <div v-else>
        <div v-for="proxy in proxies" :key="proxy.id" class="table-row">
          <div class="col">{{ proxy.id }}</div>
          <div class="col">{{ proxy.provider_name }}</div>
          <div class="col">
            <span v-if="proxy.host && proxy.port" class="address-info">
              {{ proxy.host }}:{{ proxy.port }}
            </span>
            <span v-else class="address-info">-</span>
          </div>
          <div class="col">
            <span class="type-tag" :class="'type-' + proxy.proxy_type">
              {{ getTypeName(proxy.proxy_type) }}
            </span>
          </div>
          <div class="col">
            <span class="source-tag" :class="'source-' + proxy.proxy_source">
              {{ getSourceName(proxy.proxy_source) }}
            </span>
          </div>
          <div class="col">
            <span class="status-tag" :class="proxy.status">
              {{ getStatusName(proxy.status) }}
            </span>
          </div>
          <div class="col">{{ proxy.success_rate.toFixed(1) }}%</div>
          <div class="col">{{ proxy.avg_response_time }}ms</div>
          <div class="col">
            <span :class="{ 'error-count': proxy.consecutive_failures > 0 }">
              {{ proxy.consecutive_failures }}
            </span>
          </div>
          <div class="col">{{ proxy.priority }}</div>
          <div class="col">
            <button @click="toggleProxy(proxy)" class="toggle-btn" :class="{ enabled: proxy.is_enabled }">
              {{ proxy.is_enabled ? '✓ 启用' : '✗ 禁用' }}
            </button>
          </div>
          <div class="col">{{ formatDate(proxy.last_test_at) }}</div>
          <div class="col actions">
            <button @click="testProxy(proxy)" class="btn-test" :disabled="testing === proxy.id">
              {{ testing === proxy.id ? '测试中...' : '🧪 测试' }}
            </button>
            <button @click="viewTestLogs(proxy)" class="btn-logs">📊 日志</button>
            <button @click="editProxy(proxy)" class="btn-edit">✏️ 编辑</button>
            <button @click="deleteProxy(proxy)" class="btn-delete">🗑️ 删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="totalPages > 1">
      <button @click="changePage(currentPage - 1)" :disabled="currentPage === 1">上一页</button>
      <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
      <button @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">下一页</button>
    </div>

    <!-- 添加代理模态框 -->
    <div v-if="showAddModal" class="modal-overlay" @click="closeAddModal">
      <div class="modal add-modal" @click.stop>
        <div class="modal-header">
          <h3>添加代理</h3>
          <button @click="closeAddModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitAddForm">
            <!-- 基本信息行 -->
            <div class="form-row">
              <div class="form-group">
                <label>提供商名称 *</label>
                <input type="text" v-model="proxyForm.provider_name" required placeholder="如：快代理、自建代理等">
              </div>
              <div class="form-group">
                <label>代理来源 *</label>
                <select v-model="proxyForm.proxy_source" required @change="onSourceChange">
                  <option value="static">静态配置</option>
                  <option value="self_hosted">自建服务器</option>
                  <option value="third_party_api">第三方API</option>
                </select>
              </div>
            </div>

            <!-- 服务器配置 -->
            <div v-if="proxyForm.proxy_source === 'static' || proxyForm.proxy_source === 'self_hosted'" class="proxy-server-config">
              <div class="form-row">
                <div class="form-group">
                  <label>服务器地址 *</label>
                  <input type="text" v-model="proxyForm.host" required placeholder="如：*************">
                </div>
                <div class="form-group">
                  <label>端口 *</label>
                  <input type="number" v-model.number="proxyForm.port" required placeholder="如：13128">
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>用户名</label>
                  <input type="text" v-model="proxyForm.username" placeholder="代理认证用户名（可选）">
                </div>
                <div class="form-group">
                  <label>密码</label>
                  <input type="password" v-model="proxyForm.password" placeholder="代理认证密码（可选）">
                </div>
              </div>
            </div>

            <!-- 配置信息行 -->
            <div class="form-row">
              <div class="form-group">
                <label>代理类型 *</label>
                <select v-model="proxyForm.proxy_type" required>
                  <option value="datacenter">数据中心</option>
                  <option value="residential">住宅代理</option>
                  <option value="mobile">移动代理</option>
                </select>
              </div>
              <div class="form-group">
                <label>优先级</label>
                <input type="number" v-model.number="proxyForm.priority" min="1" max="10" placeholder="1-10">
              </div>
            </div>

            <!-- 地理位置行 -->
            <div class="form-row">
              <div class="form-group">
                <label>国家</label>
                <input type="text" v-model="proxyForm.country" placeholder="如：中国">
              </div>
              <div class="form-group">
                <label>地区</label>
                <input type="text" v-model="proxyForm.region" placeholder="如：北京">
              </div>
            </div>

            <!-- 测试URL -->
            <div class="form-group">
              <label>测试URL</label>
              <input type="url" v-model="proxyForm.test_url" placeholder="用于测试代理连通性的URL">
            </div>

            <!-- 启用状态 -->
            <div class="form-group checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" v-model="proxyForm.is_enabled">
                <span class="checkmark"></span>
                启用代理
              </label>
            </div>

            <div class="form-actions">
              <button type="button" @click="closeAddModal" class="btn-secondary">取消</button>
              <button type="submit" class="btn-primary" :disabled="saving">
                {{ saving ? '添加中...' : '确认添加' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 编辑代理模态框 -->
    <div v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
      <div class="modal edit-modal" @click.stop>
        <div class="modal-header">
          <h3>编辑代理</h3>
          <button @click="closeEditModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitEditForm">
            <!-- 基本信息行 -->
            <div class="form-row">
              <div class="form-group">
                <label>提供商名称 *</label>
                <input type="text" v-model="proxyForm.provider_name" required>
              </div>
              <div class="form-group">
                <label>代理来源 *</label>
                <select v-model="proxyForm.proxy_source" required>
                  <option value="static">静态配置</option>
                  <option value="self_hosted">自建服务器</option>
                  <option value="third_party_api">第三方API</option>
                </select>
              </div>
            </div>

            <!-- 服务器配置行 -->
            <div v-if="proxyForm.proxy_source === 'static' || proxyForm.proxy_source === 'self_hosted'" class="proxy-server-config">
              <div class="form-row">
                <div class="form-group">
                  <label>服务器地址 *</label>
                  <input type="text" v-model="proxyForm.host" required>
                </div>
                <div class="form-group">
                  <label>端口 *</label>
                  <input type="number" v-model.number="proxyForm.port" required>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label>用户名</label>
                  <input type="text" v-model="proxyForm.username">
                </div>
                <div class="form-group">
                  <label>密码</label>
                  <input type="password" v-model="proxyForm.password">
                </div>
              </div>
            </div>

            <!-- 配置信息行 -->
            <div class="form-row">
              <div class="form-group">
                <label>代理类型 *</label>
                <select v-model="proxyForm.proxy_type" required>
                  <option value="datacenter">数据中心</option>
                  <option value="residential">住宅代理</option>
                  <option value="mobile">移动代理</option>
                </select>
              </div>
              <div class="form-group">
                <label>优先级</label>
                <input type="number" v-model.number="proxyForm.priority" min="1" max="10">
              </div>
            </div>

            <!-- 地理位置行 -->
            <div class="form-row">
              <div class="form-group">
                <label>国家</label>
                <input type="text" v-model="proxyForm.country" placeholder="如：中国">
              </div>
              <div class="form-group">
                <label>地区</label>
                <input type="text" v-model="proxyForm.region" placeholder="如：北京">
              </div>
            </div>

            <!-- 测试URL -->
            <div class="form-group">
              <label>测试URL</label>
              <input type="url" v-model="proxyForm.test_url" placeholder="用于测试代理连通性的URL">
            </div>

            <!-- 启用状态 -->
            <div class="form-group checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" v-model="proxyForm.is_enabled">
                <span class="checkmark"></span>
                启用代理
              </label>
            </div>

            <div class="form-actions">
              <button type="button" @click="closeEditModal" class="btn-secondary">取消</button>
              <button type="submit" class="btn-primary" :disabled="saving">
                {{ saving ? '更新中...' : '确认更新' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import api from '../api'

// 响应式数据
const proxies = ref([])
const stats = ref({
  total_proxies: 0,
  active_proxies: 0,
  avg_success_rate: 0,
  avg_response_time: 0
})
const loading = ref(false)
const testing = ref(null)
const batchTesting = ref(false)
const saving = ref(false)
const showAddModal = ref(false)
const showEditModal = ref(false)
const currentPage = ref(1)
const totalPages = ref(1)
const pageSize = 20

// 筛选器
const filters = ref({
  status: '',
  proxy_type: '',
  proxy_source: '',
  enabled_only: false
})

// 代理表单
const proxyForm = ref({
  id: null,
  provider_name: '',
  host: '',
  port: null,
  username: '',
  password: '',
  proxy_type: 'datacenter',
  proxy_source: 'static',
  api_config: null,
  test_url: 'https://httpbin.org/ip',
  country: '',
  region: '',
  priority: 1,
  is_enabled: true
})

// 类型名称映射
const getTypeName = (type) => {
  const names = {
    'residential': '住宅代理',
    'datacenter': '数据中心',
    'mobile': '移动代理'
  }
  return names[type] || type
}

// 状态名称映射
const getStatusName = (status) => {
  const names = {
    'active': '活跃',
    'inactive': '非活跃',
    'error': '错误',
    'testing': '测试中'
  }
  return names[status] || status
}

// 来源名称映射
const getSourceName = (source) => {
  const names = {
    'self_hosted': '自建服务器',
    'third_party_api': '第三方API',
    'static': '静态配置'
  }
  return names[source] || source
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 加载代理列表
const loadProxies = async () => {
  loading.value = true
  try {
    const response = await api.proxies.getAll()
    if (response.code === 200) {
      proxies.value = response.data
      totalPages.value = Math.ceil(proxies.value.length / pageSize)
    } else {
      proxies.value = []
    }
  } catch (error) {
    console.error('加载代理失败:', error)
    showMessage('错误', '加载代理失败')
    proxies.value = []
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await api.stats.getOverview()
    if (response.code === 200) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 刷新代理列表
const refreshProxies = () => {
  currentPage.value = 1
  loadProxies()
  loadStats()
}

// 分页
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadProxies()
  }
}

// 简单的消息提示
const showMessage = (type, text) => {
  alert(`${type}: ${text}`)
}

// 测试单个代理
const testProxy = async (proxy) => {
  testing.value = proxy.id
  try {
    console.log('开始测试代理:', proxy.id)
    const response = await api.proxies.test(proxy.id)
    console.log('代理测试响应:', response)

    if (response.code === 200) {
      // 检查响应数据结构
      const testData = response.data
      console.log('测试数据:', testData)

      if (testData && testData.success) {
        const responseTime = testData.response_time_ms || 'N/A'
        const testIP = testData.test_ip || '未知'
        showMessage('成功', `代理测试成功！\n响应时间: ${responseTime}ms\n测试IP: ${testIP}`)
      } else {
        const errorMsg = testData?.error_message || '代理测试失败'
        showMessage('失败', errorMsg)
      }
    } else {
      showMessage('错误', response.message || '代理测试失败')
    }
    loadProxies()
    loadStats()
  } catch (error) {
    console.error('测试代理失败:', error)
    if (error.response) {
      showMessage('错误', `测试失败: ${error.response.status} ${error.response.statusText}`)
    } else {
      showMessage('错误', '测试代理失败')
    }
  } finally {
    testing.value = null
  }
}

// 批量测试代理
const batchTest = async () => {
  batchTesting.value = true
  try {
    const response = await api.proxies.batchTest()
    if (response.code === 200) {
      const { tested_count, success_count, failed_count } = response.data
      showMessage('成功', `批量测试完成！测试了 ${tested_count} 个代理，成功 ${success_count} 个，失败 ${failed_count} 个`)
    } else {
      showMessage('错误', response.message || '批量测试失败')
    }
    loadProxies()
    loadStats()
  } catch (error) {
    console.error('批量测试失败:', error)
    showMessage('错误', '批量测试失败')
  } finally {
    batchTesting.value = false
  }
}

// 切换代理启用状态
const toggleProxy = async (proxy) => {
  try {
    const response = await api.proxies.toggle(proxy.id)
    if (response.code === 200) {
      showMessage('成功', response.message)
      loadProxies()
    } else {
      showMessage('错误', response.message || '切换代理状态失败')
    }
  } catch (error) {
    console.error('切换代理状态失败:', error)
    showMessage('错误', '切换代理状态失败')
  }
}

// 编辑代理
const editProxy = (proxy) => {
  proxyForm.value = {
    id: proxy.id,
    provider_name: proxy.provider_name,
    host: proxy.host || '',
    port: proxy.port || null,
    username: proxy.username || '',
    password: '', // 不显示现有密码
    proxy_type: proxy.proxy_type,
    proxy_source: proxy.proxy_source,
    api_config: proxy.api_config,
    test_url: proxy.test_url,
    country: proxy.country || '',
    region: proxy.region || '',
    priority: proxy.priority,
    is_enabled: proxy.is_enabled
  }
  showEditModal.value = true
}

// 来源变化处理
const onSourceChange = () => {
  if (proxyForm.value.proxy_source === 'third_party_api') {
    // 清空服务器配置
    proxyForm.value.host = ''
    proxyForm.value.port = null
    proxyForm.value.username = ''
    proxyForm.value.password = ''
  }
}

// 提交添加表单
const submitAddForm = async () => {
  saving.value = true
  try {
    const response = await api.proxies.create(proxyForm.value)
    if (response.code === 200) {
      showMessage('成功', '代理添加成功！')
      closeAddModal()
      loadProxies()
      loadStats()
    } else {
      showMessage('错误', response.message || '添加失败')
    }
  } catch (error) {
    console.error('添加代理失败:', error)
    showMessage('错误', '添加代理失败')
  } finally {
    saving.value = false
  }
}

// 提交编辑表单
const submitEditForm = async () => {
  saving.value = true
  try {
    const { id, ...updateData } = proxyForm.value
    const response = await api.proxies.update(id, updateData)
    if (response.code === 200) {
      showMessage('成功', '代理更新成功！')
      closeEditModal()
      loadProxies()
      loadStats()
    } else {
      showMessage('错误', response.message || '更新失败')
    }
  } catch (error) {
    console.error('更新代理失败:', error)
    showMessage('错误', '更新代理失败')
  } finally {
    saving.value = false
  }
}

// 关闭添加模态框
const closeAddModal = () => {
  showAddModal.value = false
  proxyForm.value = {
    id: null,
    provider_name: '',
    host: '',
    port: null,
    username: '',
    password: '',
    proxy_type: 'datacenter',
    proxy_source: 'static',
    api_config: null,
    test_url: 'https://httpbin.org/ip',
    country: '',
    region: '',
    priority: 1,
    is_enabled: true
  }
}

// 关闭编辑模态框
const closeEditModal = () => {
  showEditModal.value = false
  proxyForm.value = {
    id: null,
    provider_name: '',
    host: '',
    port: null,
    username: '',
    password: '',
    proxy_type: 'datacenter',
    proxy_source: 'static',
    api_config: null,
    test_url: 'https://httpbin.org/ip',
    country: '',
    region: '',
    priority: 1,
    is_enabled: true
  }
}

// 查看测试日志
const viewTestLogs = async (proxy) => {
  try {
    console.log('获取测试日志，代理ID:', proxy.id)
    const response = await api.proxies.getTestLogs(proxy.id, { page: 1, size: 10 })
    console.log('日志API响应:', response)

    // 检查响应格式
    if (response && response.data) {
      const logs = response.data.logs || []
      console.log('获取到的日志:', logs)

      if (logs.length === 0) {
        showMessage('提示', '暂无测试日志')
      } else {
        // 构建日志信息字符串
        let logInfo = `代理测试日志 (${proxy.provider_name})\n\n`

        logs.slice(0, 5).forEach((log, index) => {
          logInfo += `${index + 1}. 测试时间: ${formatDate(log.test_time)}\n`
          logInfo += `   结果: ${log.test_result}\n`
          logInfo += `   响应时间: ${log.response_time_ms || 'N/A'}ms\n`
          if (log.test_ip) {
            logInfo += `   测试IP: ${log.test_ip}\n`
          }
          if (log.error_message) {
            logInfo += `   错误: ${log.error_message}\n`
          }
          logInfo += '\n'
        })

        if (logs.length > 5) {
          logInfo += `... 还有 ${logs.length - 5} 条记录`
        }

        alert(logInfo)
      }
    } else {
      console.error('API响应格式错误:', response)
      showMessage('错误', '获取测试日志失败：响应格式错误')
    }
  } catch (error) {
    console.error('获取测试日志失败:', error)
    // 检查是否是网络错误
    if (error.response) {
      showMessage('错误', `获取测试日志失败: ${error.response.status} ${error.response.statusText}`)
    } else if (error.request) {
      showMessage('错误', '获取测试日志失败: 网络请求失败')
    } else {
      showMessage('错误', `获取测试日志失败: ${error.message}`)
    }
  }
}

// 删除代理
const deleteProxy = async (proxy) => {
  if (!confirm(`确定要删除代理 "${proxy.provider_name}" 吗？`)) {
    return
  }

  try {
    const response = await api.proxies.delete(proxy.id)
    if (response.code === 200) {
      showMessage('成功', '代理删除成功')
      loadProxies()
      loadStats()
    } else {
      showMessage('错误', response.message || '删除代理失败')
    }
  } catch (error) {
    console.error('删除代理失败:', error)
    showMessage('错误', '删除代理失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadProxies()
  loadStats()
})
</script>

<style scoped>
.proxies-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  font-weight: normal;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.filter-item select {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.proxies-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  overflow-x: auto;
}

.table-header {
  display: grid;
  grid-template-columns: 40px 100px 100px 70px 70px 70px 70px 80px 70px 50px 80px 100px 160px;
  background: #f5f5f5;
  border-bottom: 1px solid #eee;
  font-weight: bold;
  color: #333;
  min-width: 1000px;
}

.table-row {
  display: grid;
  grid-template-columns: 40px 100px 100px 70px 70px 70px 70px 80px 70px 50px 80px 100px 160px;
  border-bottom: 1px solid #eee;
  min-width: 1000px;
}

.table-row:hover {
  background: #f9f9f9;
}

.col {
  padding: 12px 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.type-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.type-tag.type-residential {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.type-tag.type-datacenter {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.type-tag.type-mobile {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.source-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.source-tag.source-self_hosted {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.source-tag.source-third_party_api {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.source-tag.source-static {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.address-info {
  font-family: monospace;
  font-size: 12px;
  color: #666;
}

.error-count {
  color: #ff4d4f;
  font-weight: bold;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.status-tag.active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-tag.inactive {
  background: #f5f5f5;
  color: #999;
  border: 1px solid #d9d9d9;
}

.status-tag.error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-tag.testing {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.toggle-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f5f5f5;
  cursor: pointer;
  font-size: 12px;
  color: #666;
}

.toggle-btn.enabled {
  background: #f6ffed;
  color: #52c41a;
  border-color: #b7eb8f;
}

.toggle-btn:hover {
  opacity: 0.8;
}

.actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.actions button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
}

.btn-test {
  background: #1890ff;
  color: white;
}

.btn-test:hover {
  background: #40a9ff;
}

.btn-test:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-edit {
  background: #52c41a;
  color: white;
}

.btn-edit:hover {
  background: #73d13d;
}

.btn-delete {
  background: #ff4d4f;
  color: white;
}

.btn-delete:hover {
  background: #ff7875;
}

.btn-primary {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary:hover {
  background: #40a9ff;
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  padding: 8px 16px;
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-secondary:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

.btn-secondary:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

.loading, .empty {
  text-align: center;
  padding: 40px;
  color: #666;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.pagination button:hover {
  background: #f5f5f5;
}

.pagination button:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-height: 90vh;
  overflow-y: auto;
}

.add-modal, .edit-modal {
  width: 700px;
  max-width: 95vw;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-row .form-group {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #333;
  font-size: 13px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.location-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin: 0;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.btn-primary {
  background: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary:hover {
  background: #40a9ff;
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-secondary:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.proxy-server-config {
  background: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
  margin: 16px 0;
}

.btn-logs {
  background: #722ed1;
  color: white;
}

.btn-logs:hover {
  background: #9254de;
}
</style>
