"""
数据库连接配置
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import redis
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 导入SQL日志工具
from app.utils.sql_logger import get_echo_setting, setup_sql_logging, log_database_connection

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "mysql+pymysql://root:password@localhost:3306/aqent_crawler")
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")

# 获取SQL调试设置
SQL_ECHO = get_echo_setting()

# 记录数据库连接信息
log_database_connection(DATABASE_URL)

# MySQL数据库
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False  # 禁用SQLAlchemy原生日志，使用我们的自定义日志
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 设置SQL日志监听器
setup_sql_logging(engine)

# Redis连接
try:
    redis_client = redis.from_url(REDIS_URL, decode_responses=True)
    # 测试连接
    redis_client.ping()
    # 只在主进程中打印连接信息，避免污染独立脚本的stdout
    import sys
    if __name__ == "__main__" or "standalone_browser_login" not in sys.argv[0]:
        print("Redis连接成功")
except Exception as e:
    import sys
    if __name__ == "__main__" or "standalone_browser_login" not in sys.argv[0]:
        print(f"Redis连接失败: {e}")
    redis_client = None

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """初始化数据库"""
    try:
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        raise

def test_database_connection():
    """测试数据库连接"""
    try:
        from sqlalchemy import text
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
            print("MySQL数据库连接成功")
            return True
    except Exception as e:
        print(f"MySQL数据库连接失败: {e}")
        return False
