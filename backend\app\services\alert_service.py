"""
告警服务模块
提供系统告警功能，包括邮件告警、告警去重等
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from enum import Enum

from .email_service import email_service

logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "danger"
    CRITICAL = "danger"

class AlertType(Enum):
    """告警类型"""
    SYSTEM = "system"
    CRAWLER = "crawler"
    PROXY = "proxy"
    API = "api"
    DATABASE = "database"

class AlertService:
    """告警服务类"""
    
    def __init__(self):
        """初始化告警服务"""
        self.enabled = os.getenv('EMAIL_ALERTS_ENABLED', 'false').lower() == 'true'
        self.recipients = self._parse_recipients(os.getenv('ALERT_EMAIL_RECIPIENTS', ''))
        self.cooldown_minutes = int(os.getenv('ALERT_COOLDOWN_MINUTES', '30'))
        
        # 告警去重缓存 {alert_key: last_sent_time}
        self.alert_cache = {}
        
        logger.info(f"告警服务初始化: enabled={self.enabled}, recipients={len(self.recipients)}")
    
    def _parse_recipients(self, recipients_str: str) -> List[str]:
        """解析收件人列表"""
        if not recipients_str:
            return []
        
        recipients = []
        for email in recipients_str.split(','):
            email = email.strip()
            if email and '@' in email:
                recipients.append(email)
        
        return recipients
    
    def _get_alert_key(self, alert_type: AlertType, title: str) -> str:
        """生成告警去重键"""
        return f"{alert_type.value}:{title}"
    
    def _should_send_alert(self, alert_key: str) -> bool:
        """检查是否应该发送告警（去重逻辑）"""
        if not self.enabled or not self.recipients:
            return False
        
        now = datetime.now()
        last_sent = self.alert_cache.get(alert_key)
        
        if last_sent is None:
            # 首次发送
            self.alert_cache[alert_key] = now
            return True
        
        # 检查冷却时间
        if now - last_sent >= timedelta(minutes=self.cooldown_minutes):
            self.alert_cache[alert_key] = now
            return True
        
        return False
    
    async def send_system_alert(
        self,
        title: str,
        message: str,
        level: AlertLevel = AlertLevel.WARNING,
        details: Optional[Dict[str, Any]] = None,
        affected_services: Optional[List[Dict[str, Any]]] = None,
        metrics: Optional[List[Dict[str, Any]]] = None,
        recommendations: Optional[List[str]] = None
    ) -> bool:
        """
        发送系统告警
        
        Args:
            title: 告警标题
            message: 告警消息
            level: 告警级别
            details: 告警详情
            affected_services: 受影响的服务
            metrics: 系统指标
            recommendations: 建议操作
        
        Returns:
            bool: 发送是否成功
        """
        try:
            alert_key = self._get_alert_key(AlertType.SYSTEM, title)
            
            if not self._should_send_alert(alert_key):
                logger.debug(f"告警被去重跳过: {alert_key}")
                return False
            
            template_data = {
                'alert_title': title,
                'alert_message': message,
                'alert_type': level.value,
                'alert_details': details or {},
                'affected_services': affected_services or [],
                'metrics': metrics or [],
                'recommendations': recommendations or [],
                'alert_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'alert_id': f"SYS-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            }
            
            success = await email_service.send_template_email(
                to_emails=self.recipients,
                template_name='system_alert',
                subject=f"[{level.name}告警] {title}",
                template_data=template_data
            )
            
            if success:
                logger.info(f"系统告警发送成功: {title}")
            else:
                logger.error(f"系统告警发送失败: {title}")
            
            return success
            
        except Exception as e:
            logger.error(f"发送系统告警失败: {e}")
            return False
    
    async def send_crawler_alert(
        self,
        title: str,
        message: str,
        level: AlertLevel = AlertLevel.WARNING,
        crawler_accounts: Optional[List[Dict[str, Any]]] = None,
        token_info: Optional[List[Dict[str, Any]]] = None,
        failed_operations: Optional[List[Dict[str, Any]]] = None,
        statistics: Optional[List[Dict[str, Any]]] = None,
        next_actions: Optional[List[str]] = None
    ) -> bool:
        """
        发送爬虫告警
        
        Args:
            title: 告警标题
            message: 告警消息
            level: 告警级别
            crawler_accounts: 爬虫账号信息
            token_info: Token信息
            failed_operations: 失败操作
            statistics: 统计信息
            next_actions: 建议操作
        
        Returns:
            bool: 发送是否成功
        """
        try:
            alert_key = self._get_alert_key(AlertType.CRAWLER, title)
            
            if not self._should_send_alert(alert_key):
                logger.debug(f"告警被去重跳过: {alert_key}")
                return False
            
            template_data = {
                'notification_title': title,
                'notification_message': message,
                'status_type': level.value,
                'crawler_accounts': crawler_accounts or [],
                'token_info': token_info or [],
                'failed_operations': failed_operations or [],
                'statistics': statistics or [],
                'next_actions': next_actions or [],
                'notification_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_accounts': len(crawler_accounts) if crawler_accounts else 0,
                'active_accounts': len([acc for acc in (crawler_accounts or []) if acc.get('status') == 'active'])
            }
            
            success = await email_service.send_template_email(
                to_emails=self.recipients,
                template_name='crawler_status',
                subject=f"[{level.name}告警] {title}",
                template_data=template_data
            )
            
            if success:
                logger.info(f"爬虫告警发送成功: {title}")
            else:
                logger.error(f"爬虫告警发送失败: {title}")
            
            return success
            
        except Exception as e:
            logger.error(f"发送爬虫告警失败: {e}")
            return False
    
    async def send_proxy_alert(
        self,
        title: str,
        message: str,
        level: AlertLevel = AlertLevel.WARNING,
        proxy_info: Optional[List[Dict[str, Any]]] = None,
        statistics: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """
        发送代理告警
        
        Args:
            title: 告警标题
            message: 告警消息
            level: 告警级别
            proxy_info: 代理信息
            statistics: 统计信息
        
        Returns:
            bool: 发送是否成功
        """
        try:
            alert_key = self._get_alert_key(AlertType.PROXY, title)
            
            if not self._should_send_alert(alert_key):
                logger.debug(f"告警被去重跳过: {alert_key}")
                return False
            
            # 使用通用通知模板
            template_data = {
                'title': title,
                'message': message,
                'notification_type': level.value,
                'content': f'<p>{message}</p>',
                'key_value_data': {
                    'title': '代理统计信息',
                    'items': statistics or []
                } if statistics else None,
                'data_table': {
                    'title': '代理详情',
                    'headers': ['代理地址', '端口', '状态', '响应时间', '最后检查'],
                    'rows': [[
                        proxy.get('host', ''),
                        str(proxy.get('port', '')),
                        proxy.get('status', ''),
                        f"{proxy.get('response_time', 0)}ms",
                        proxy.get('last_check', '')
                    ] for proxy in (proxy_info or [])]
                } if proxy_info else None
            }
            
            success = await email_service.send_template_email(
                to_emails=self.recipients,
                template_name='notification',
                subject=f"[{level.name}告警] {title}",
                template_data=template_data
            )
            
            if success:
                logger.info(f"代理告警发送成功: {title}")
            else:
                logger.error(f"代理告警发送失败: {title}")
            
            return success
            
        except Exception as e:
            logger.error(f"发送代理告警失败: {e}")
            return False
    
    async def send_api_alert(
        self,
        title: str,
        message: str,
        level: AlertLevel = AlertLevel.WARNING,
        api_info: Optional[Dict[str, Any]] = None,
        error_details: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        发送API告警
        
        Args:
            title: 告警标题
            message: 告警消息
            level: 告警级别
            api_info: API信息
            error_details: 错误详情
        
        Returns:
            bool: 发送是否成功
        """
        try:
            alert_key = self._get_alert_key(AlertType.API, title)
            
            if not self._should_send_alert(alert_key):
                logger.debug(f"告警被去重跳过: {alert_key}")
                return False
            
            # 使用通用通知模板
            template_data = {
                'title': title,
                'message': message,
                'notification_type': level.value,
                'content': f'<p>{message}</p>',
                'key_value_data': {
                    'title': 'API信息',
                    'items': [
                        {'key': k, 'value': str(v)} for k, v in (api_info or {}).items()
                    ]
                } if api_info else None,
                'list_data': {
                    'title': '错误详情',
                    'items': [
                        f"{k}: {v}" for k, v in (error_details or {}).items()
                    ]
                } if error_details else None
            }
            
            success = await email_service.send_template_email(
                to_emails=self.recipients,
                template_name='notification',
                subject=f"[{level.name}告警] {title}",
                template_data=template_data
            )
            
            if success:
                logger.info(f"API告警发送成功: {title}")
            else:
                logger.error(f"API告警发送失败: {title}")
            
            return success
            
        except Exception as e:
            logger.error(f"发送API告警失败: {e}")
            return False
    
    def clear_alert_cache(self):
        """清理告警缓存"""
        self.alert_cache.clear()
        logger.info("告警缓存已清理")
    
    def get_alert_status(self) -> Dict[str, Any]:
        """获取告警服务状态"""
        return {
            'enabled': self.enabled,
            'recipients_count': len(self.recipients),
            'cooldown_minutes': self.cooldown_minutes,
            'cached_alerts': len(self.alert_cache),
            'email_service_configured': email_service.is_configured if hasattr(email_service, 'is_configured') else False
        }

# 全局告警服务实例
alert_service = AlertService()
