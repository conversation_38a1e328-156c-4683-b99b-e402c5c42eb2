"""
浏览器自动化登录服务

提供账号自动登录、token提取和自动刷新功能
"""

import asyncio
import json
import re
import sys
import time
import threading
import concurrent.futures
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
from urllib.parse import urlparse

from playwright.sync_api import sync_playwright
from sqlalchemy.orm import Session

from app.models import CrawlerAccount, ProxyPool, Platform
from app.utils.encryption import encrypt_password, decrypt_password


class BrowserLoginService:
    """浏览器登录服务"""

    def __init__(self, db: Session):
        self.db = db
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=1)
    
    def _sync_login_account(self, account_id: int) -> Dict[str, any]:
        """同步版本的账号登录（在线程中运行）"""
        try:
            # 获取账号信息（现在有了platform关系）
            account = self.db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
            if not account:
                return {"success": False, "message": "账号不存在"}

            # 如果没有密码，跳过密码验证（支持仅使用<PERSON>ie的账号）
            print(f"开始登录账号: {account.username}, 平台: {account.platform.name}")

            # 使用同步版本的Playwright
            with sync_playwright() as playwright:
                # 浏览器启动选项
                launch_options = {
                    "headless": False,  # 显示浏览器以便人工验证
                    "args": [
                        "--no-sandbox",
                        "--disable-setuid-sandbox",
                        "--disable-dev-shm-usage",
                        "--disable-blink-features=AutomationControlled",
                        "--exclude-switches=enable-automation",
                        "--incognito",  # 无痕模式
                        "--disable-gpu",
                        "--disable-software-rasterizer",
                        "--no-first-run"
                    ]
                }

                # 配置代理
                if account.proxy_id:
                    proxy = self.db.query(ProxyPool).filter(ProxyPool.id == account.proxy_id).first()
                    if proxy and proxy.is_enabled and proxy.host and proxy.port:
                        proxy_config = {
                            "server": f"http://{proxy.host}:{proxy.port}"
                        }

                        # 添加代理认证
                        if proxy.username and proxy.password:
                            from app.utils.crypto import decrypt_password
                            decrypted_password = decrypt_password(proxy.password)
                            if decrypted_password:
                                proxy_config["username"] = proxy.username
                                proxy_config["password"] = decrypted_password

                        launch_options["proxy"] = proxy_config
                        print(f"使用代理: {proxy.host}:{proxy.port}")

                print(f"🚀 启动Chrome浏览器...")
                browser = playwright.chromium.launch(**launch_options)

                # 创建无痕浏览器上下文
                context_options = {
                    "viewport": {"width": 1920, "height": 1080},
                    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                }

                context = browser.new_context(**context_options)

                # 应用反检测脚本
                context.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });

                    window.chrome = {
                        runtime: {},
                        loadTimes: function() {},
                        csi: function() {},
                        app: {}
                    };

                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5],
                    });

                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['zh-CN', 'zh', 'en'],
                    });
                """)

                page = context.new_page()

                # 根据平台执行登录
                platform_code = account.platform.code
                if platform_code == "taobao":
                    result = self._sync_login_taobao(page, account)
                elif platform_code == "1688":
                    result = self._sync_login_1688(page, account)
                elif platform_code == "jingdong":
                    result = self._sync_login_jingdong(page, account)
                else:
                    result = {"success": False, "message": f"暂不支持平台: {platform_code}"}

                # 关闭浏览器 - 在Windows环境下可能导致问题，暂时注释
                # browser.close()

                return result

        except Exception as e:
            return {"success": False, "message": f"登录失败: {str(e)}"}
    
    async def login_account(self, account_id: int) -> Dict[str, any]:
        """登录账号并提取token/cookie（使用独立进程）"""
        try:
            import subprocess
            import json
            import os

            # 获取独立登录脚本的路径
            script_path = os.path.join(
                os.path.dirname(__file__),
                "standalone_browser_login.py"
            )

            print(f"启动独立登录进程，账号ID: {account_id}")

            # 使用subprocess运行独立脚本
            result = subprocess.run(
                [sys.executable, script_path, str(account_id)],
                capture_output=True,
                timeout=600,  # 10分钟超时，给用户足够时间完成登录
                cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__)))  # 设置工作目录为backend
            )

            print(f"[LOGIN] 独立进程返回码: {result.returncode}")

            # 安全地解码输出
            try:
                stdout_text = result.stdout.decode('utf-8', errors='ignore').strip() if result.stdout else ""
                stderr_text = result.stderr.decode('utf-8', errors='ignore').strip() if result.stderr else ""
            except Exception as decode_error:
                print(f"[LOGIN] 输出解码失败: {decode_error}")
                stdout_text = ""
                stderr_text = ""

            print(f"[LOGIN] stdout: {stdout_text}")
            if stderr_text:
                print(f"[LOGIN] stderr: {stderr_text}")

            if result.returncode == 0:
                # stdout可能包含多行，需要找到JSON行
                if stdout_text:
                    # 按行分割，找到JSON行
                    lines = stdout_text.split('\n')
                    json_result = None

                    # 从后往前找第一个有效的JSON
                    for line in reversed(lines):
                        line = line.strip()
                        if line and line.startswith('{'):
                            try:
                                json_result = json.loads(line)
                                print(f"[LOGIN] 成功解析JSON结果: {json_result}")
                                return json_result
                            except json.JSONDecodeError:
                                continue

                    # 如果没有找到有效JSON，返回错误
                    print(f"[LOGIN] 未找到有效JSON")
                    print(f"[LOGIN] 原始输出: {repr(stdout_text)}")
                    return {"success": False, "message": "未找到有效的JSON结果"}
                else:
                    return {"success": False, "message": "独立进程没有输出结果"}
            else:
                error_msg = stderr_text or stdout_text or "未知错误"
                return {"success": False, "message": f"登录进程失败: {error_msg}"}

        except subprocess.TimeoutExpired:
            return {"success": False, "message": "登录超时（10分钟）"}
        except Exception as e:
            return {"success": False, "message": f"登录失败: {str(e)}"}
    
    def _sync_login_taobao(self, page, account: CrawlerAccount) -> Dict[str, any]:
        """淘宝登录流程（同步版本）"""
        try:
            # 访问淘宝登录页面
            print("🌐 访问淘宝登录页面...")
            page.goto("https://login.taobao.com/member/login.jhtml", timeout=60000)
            page.wait_for_load_state("networkidle", timeout=60000)

            # 等待登录表单加载 - 尝试多个可能的选择器
            login_selectors = [
                "#fm-login-id",  # 旧版选择器
                "input[name='loginId']",  # 新版选择器
                "input[placeholder*='手机号']",  # 手机号输入框
                "input[placeholder*='邮箱']",  # 邮箱输入框
                ".login-input input",  # 通用登录输入框
            ]

            username_input = None
            for selector in login_selectors:
                try:
                    page.wait_for_selector(selector, timeout=3000)
                    username_input = selector
                    print(f"找到用户名输入框: {selector}")
                    break
                except:
                    continue

            if not username_input:
                return {"success": False, "message": "未找到用户名输入框，请检查淘宝登录页面"}

            # 输入用户名
            print(f"输入用户名: {account.username}")
            page.fill(username_input, account.username)
            page.wait_for_timeout(1000)

            # 查找密码输入框
            password_selectors = [
                "#fm-login-password",  # 旧版选择器
                "input[name='password']",  # 新版选择器
                "input[type='password']",  # 密码类型输入框
                ".login-input input[type='password']",  # 通用密码输入框
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    if page.query_selector(selector):
                        password_input = selector
                        print(f"找到密码输入框: {selector}")
                        break
                except:
                    continue

            if not password_input:
                return {"success": False, "message": "未找到密码输入框，请检查淘宝登录页面"}

            # 输入密码
            password = decrypt_password(account.password)
            print("输入密码...")
            page.fill(password_input, password)
            page.wait_for_timeout(1000)

            # 查找登录按钮
            login_button_selectors = [
                "#login-form .fm-button",  # 旧版选择器
                "button[type='submit']",  # 提交按钮
                ".login-btn",  # 登录按钮类
                "button:has-text('登录')",  # 包含"登录"文本的按钮
                ".fm-button",  # 通用按钮
            ]

            login_button = None
            for selector in login_button_selectors:
                try:
                    if page.query_selector(selector):
                        login_button = selector
                        print(f"找到登录按钮: {selector}")
                        break
                except:
                    continue

            if not login_button:
                return {"success": False, "message": "未找到登录按钮，请检查淘宝登录页面"}

            # 点击登录按钮
            print("点击登录按钮...")
            page.click(login_button)

            # 等待登录结果（可能需要验证码或其他验证）
            print("请在浏览器中完成登录验证（验证码、滑块、手机验证码等）...")
            print("系统将等待300秒（5分钟）检测登录状态...")
            print("重要：如果需要手机验证码，请耐心等待，系统会等待您完成整个验证过程")

            # 等待跳转到主页或检测登录成功
            login_success = False
            max_wait_time = 300  # 300秒（5分钟）
            check_interval = 5   # 每5秒检查一次
            elapsed_time = 0

            while elapsed_time < max_wait_time and not login_success:
                try:
                    current_url = page.url
                    print(f" 检查登录状态: {current_url} (已等待{elapsed_time}秒)")

                    # 检测是否在验证过程中
                    verification_indicators = [
                        "checkcode", "verify", "challenge", "captcha",
                        "slider", "sms", "phone", "mobile"
                    ]
                    is_in_verification = any(indicator in current_url.lower() for indicator in verification_indicators)

                    # 检查成功页面
                    success_indicators = [
                        "www.taobao.com", "i.taobao.com", "member.taobao.com"
                    ]
                    login_flow_indicators = [
                        "login.taobao.com", "passport.taobao.com", "/login", "/signin"
                    ]

                    is_success_page = any(indicator in current_url for indicator in success_indicators)
                    is_still_in_login = any(indicator in current_url for indicator in login_flow_indicators)

                    if is_success_page and not is_still_in_login:
                        login_success = True
                        print("检测到登录成功，开始提取cookie和token...")
                        break
                    elif is_in_verification:
                        print(f"正在验证中，请完成验证... (已等待{elapsed_time}秒)")
                    else:
                        print(f"等待登录完成... (已等待{elapsed_time}秒)")

                    page.wait_for_timeout(check_interval * 1000)
                    elapsed_time += check_interval

                except Exception as e:
                    print(f"检测登录状态时出错: {str(e)}")
                    if "closed" in str(e).lower():
                        break
                    page.wait_for_timeout(check_interval * 1000)
                    elapsed_time += check_interval

            if not login_success:
                return {"success": False, "message": "登录超时，请在5分钟内完成登录操作（包括手机验证码验证）"}

            # 提取cookie和token
            cookies = page.context.cookies()
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}

            # 提取淘宝token
            token = None
            token_expires_at = None

            if '_m_h5_tk' in cookie_dict:
                m_h5_tk = cookie_dict['_m_h5_tk']
                if '_' in m_h5_tk:
                    token_parts = m_h5_tk.split('_')
                    token = token_parts[0]
                    if len(token_parts) > 1:
                        try:
                            expires_timestamp = int(token_parts[1]) / 1000
                            token_expires_at = datetime.fromtimestamp(expires_timestamp)
                        except:
                            pass

            # 格式化cookie字符串
            cookie_string = "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])

            # 获取User-Agent
            user_agent = page.evaluate("navigator.userAgent")

            print(f"提取到 {len(cookie_dict)} 个Cookie")
            print(f"Token: {token[:10] if token else 'None'}...")

            # 更新账号信息
            account.cookie = cookie_string
            account.token = token
            account.user_agent = user_agent
            account.login_status = 'logged_in'
            account.token_expires_at = token_expires_at
            account.last_login_at = datetime.now()
            account.updated_at = datetime.now()

            self.db.commit()

            return {
                "success": True,
                "message": "淘宝登录成功",
                "data": {
                    "token": token,
                    "token_expires_at": token_expires_at.isoformat() if token_expires_at else None,
                    "cookie_count": len(cookie_dict),
                    "user_agent": user_agent
                }
            }

        except Exception as e:
            print(f" 淘宝登录失败: {str(e)}")
            return {"success": False, "message": f"淘宝登录失败: {str(e)}"}

    def _sync_login_1688(self, page, account: CrawlerAccount) -> Dict[str, any]:
        """1688登录流程（同步版本）"""
        try:
            # 访问1688登录页面
            print("🌐 访问1688登录页面...")
            page.goto("https://login.1688.com/member/signin.htm", timeout=60000)
            page.wait_for_load_state("networkidle", timeout=60000)

            # 等待登录表单加载 - 尝试多个可能的选择器
            login_selectors = [
                "#fm-login-id",  # 旧版选择器
                "input[name='loginId']",  # 新版选择器
                "input[placeholder*='手机号']",  # 手机号输入框
                "input[placeholder*='邮箱']",  # 邮箱输入框
                "input[placeholder*='会员名']",  # 会员名输入框
                ".login-input input",  # 通用登录输入框
                "#loginId",  # 1688特有选择器
            ]

            username_input = None
            for selector in login_selectors:
                try:
                    page.wait_for_selector(selector, timeout=3000)
                    username_input = selector
                    print(f" 找到用户名输入框: {selector}")
                    break
                except:
                    continue

            if not username_input:
                return {"success": False, "message": "未找到用户名输入框，请检查1688登录页面"}

            # 输入用户名
            print(f" 输入用户名: {account.username}")
            page.fill(username_input, account.username)
            page.wait_for_timeout(1000)

            # 查找密码输入框
            password_selectors = [
                "#fm-login-password",  # 旧版选择器
                "input[name='password']",  # 新版选择器
                "input[type='password']",  # 密码类型输入框
                ".login-input input[type='password']",  # 通用密码输入框
                "#password",  # 1688特有选择器
            ]

            password_input = None
            for selector in password_selectors:
                try:
                    if page.query_selector(selector):
                        password_input = selector
                        print(f" 找到密码输入框: {selector}")
                        break
                except:
                    continue

            if not password_input:
                return {"success": False, "message": "未找到密码输入框，请检查1688登录页面"}

            # 输入密码
            password = decrypt_password(account.password)
            print(" 输入密码...")
            page.fill(password_input, password)
            page.wait_for_timeout(1000)

            # 查找登录按钮
            login_button_selectors = [
                "#login-form .fm-button",  # 旧版选择器
                "button[type='submit']",  # 提交按钮
                ".login-btn",  # 登录按钮类
                "button:has-text('登录')",  # 包含"登录"文本的按钮
                ".fm-button",  # 通用按钮
                "#fm-login-submit",  # 1688特有选择器
            ]

            login_button = None
            for selector in login_button_selectors:
                try:
                    if page.query_selector(selector):
                        login_button = selector
                        print(f" 找到登录按钮: {selector}")
                        break
                except:
                    continue

            if not login_button:
                return {"success": False, "message": "未找到登录按钮，请检查1688登录页面"}

            # 点击登录按钮
            print(" 点击登录按钮...")
            page.click(login_button)

            # 等待登录结果（可能需要验证码或其他验证）
            print(" 请在浏览器中完成登录验证（验证码、滑块、手机验证码等）...")
            print(" 系统将等待300秒（5分钟）检测登录状态...")
            print(" 重要：如果需要手机验证码，请耐心等待，系统会等待您完成整个验证过程")

            # 等待跳转到主页或检测登录成功
            login_success = False
            max_wait_time = 300  # 300秒（5分钟）
            check_interval = 5   # 每5秒检查一次
            elapsed_time = 0

            while elapsed_time < max_wait_time and not login_success:
                try:
                    current_url = page.url
                    print(f" 检查登录状态: {current_url} (已等待{elapsed_time}秒)")

                    # 检测是否在验证过程中
                    verification_indicators = [
                        "checkcode", "verify", "challenge", "captcha",
                        "slider", "sms", "phone", "mobile"
                    ]
                    is_in_verification = any(indicator in current_url.lower() for indicator in verification_indicators)

                    # 检查成功页面
                    success_indicators = [
                        "www.1688.com", "work.1688.com", "member.1688.com", "i.1688.com"
                    ]
                    login_flow_indicators = [
                        "login.1688.com", "passport.1688.com", "/login", "/signin"
                    ]

                    is_success_page = any(indicator in current_url for indicator in success_indicators)
                    is_still_in_login = any(indicator in current_url for indicator in login_flow_indicators)

                    if is_success_page and not is_still_in_login:
                        login_success = True
                        print(" 检测到登录成功，开始提取cookie和token...")
                        break
                    elif is_in_verification:
                        print(f" 正在验证中，请完成验证... (已等待{elapsed_time}秒)")
                    else:
                        print(f" 等待登录完成... (已等待{elapsed_time}秒)")

                    page.wait_for_timeout(check_interval * 1000)
                    elapsed_time += check_interval

                except Exception as e:
                    print(f"检测登录状态时出错: {str(e)}")
                    if "closed" in str(e).lower():
                        break
                    page.wait_for_timeout(check_interval * 1000)
                    elapsed_time += check_interval

            if not login_success:
                return {"success": False, "message": "登录超时，请在5分钟内完成登录操作（包括手机验证码验证）"}

            # 提取cookie和token
            cookies = page.context.cookies()
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}

            # 提取1688 token（与淘宝相同的逻辑）
            token = None
            token_expires_at = None

            if '_m_h5_tk' in cookie_dict:
                m_h5_tk = cookie_dict['_m_h5_tk']
                if '_' in m_h5_tk:
                    token_parts = m_h5_tk.split('_')
                    token = token_parts[0]
                    if len(token_parts) > 1:
                        try:
                            expires_timestamp = int(token_parts[1]) / 1000
                            token_expires_at = datetime.fromtimestamp(expires_timestamp)
                        except:
                            pass

            # 格式化cookie字符串
            cookie_string = "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])

            # 获取User-Agent
            user_agent = page.evaluate("navigator.userAgent")

            print(f" 提取到 {len(cookie_dict)} 个Cookie")
            print(f" Token: {token[:10] if token else 'None'}...")

            # 更新账号信息
            account.cookie = cookie_string
            account.token = token
            account.user_agent = user_agent
            account.login_status = 'logged_in'
            account.token_expires_at = token_expires_at
            account.last_login_at = datetime.now()
            account.updated_at = datetime.now()

            self.db.commit()

            return {
                "success": True,
                "message": "1688登录成功",
                "data": {
                    "token": token,
                    "token_expires_at": token_expires_at.isoformat() if token_expires_at else None,
                    "cookie_count": len(cookie_dict),
                    "user_agent": user_agent
                }
            }

        except Exception as e:
            print(f" 1688登录失败: {str(e)}")
            return {"success": False, "message": f"1688登录失败: {str(e)}"}
    
    def _sync_login_jingdong(self, page, account: CrawlerAccount) -> Dict[str, any]:
        """京东登录流程（同步版本）"""
        try:
            # 访问京东登录页面
            print("🌐 访问京东登录页面...")
            page.goto("https://passport.jd.com/new/login.aspx", timeout=60000)
            page.wait_for_load_state("networkidle", timeout=60000)

            # 切换到账号密码登录
            try:
                page.click(".login-tab-r")
                page.wait_for_timeout(1000)
            except:
                pass

            # 输入用户名
            print(f" 输入用户名: {account.username}")
            page.fill("#loginname", account.username)
            page.wait_for_timeout(1000)

            # 输入密码
            password = decrypt_password(account.password)
            print(" 输入密码...")
            page.fill("#nloginpwd", password)
            page.wait_for_timeout(1000)

            # 点击登录按钮
            print(" 点击登录按钮...")
            page.click("#loginsubmit")

            print(" 请在浏览器中完成京东登录验证...")
            print(" 系统将等待60秒检测登录状态...")

            # 等待登录成功
            try:
                page.wait_for_url("**/www.jd.com/**", timeout=120000)  # 改为120秒
                print(" 检测到京东登录成功，开始提取cookie...")
            except:
                page.wait_for_timeout(5000)
                current_url = page.url
                if "passport.jd.com" in current_url:
                    return {"success": False, "message": "京东登录超时或失败，请检查账号密码或完成验证"}

            # 提取cookie
            cookies = page.context.cookies()
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
            cookie_string = "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])

            # 获取User-Agent
            user_agent = page.evaluate("navigator.userAgent")

            print(f" 提取到 {len(cookie_dict)} 个Cookie")

            # 更新账号信息
            account.cookie = cookie_string
            account.user_agent = user_agent
            account.login_status = 'logged_in'
            account.last_login_at = datetime.now()
            account.updated_at = datetime.now()

            self.db.commit()

            return {
                "success": True,
                "message": "京东登录成功",
                "data": {
                    "cookie_count": len(cookie_dict),
                    "user_agent": user_agent
                }
            }

        except Exception as e:
            print(f" 京东登录失败: {str(e)}")
            return {"success": False, "message": f"京东登录失败: {str(e)}"}

