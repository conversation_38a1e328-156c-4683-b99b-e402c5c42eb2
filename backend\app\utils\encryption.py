"""
加密工具

提供密码加密和解密功能
"""

import base64
import os
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


# 从环境变量获取加密密钥，如果没有则使用默认值
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY", "your-secret-key-here-change-in-production")


def _get_fernet():
    """获取Fernet加密实例"""
    # 使用PBKDF2从密钥生成Fernet密钥
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=b'salt_',  # 在生产环境中应该使用随机salt
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(ENCRYPTION_KEY.encode()))
    return Fernet(key)


def encrypt_password(password: str) -> str:
    """加密密码"""
    if not password:
        return ""
    
    try:
        f = _get_fernet()
        encrypted = f.encrypt(password.encode())
        return base64.urlsafe_b64encode(encrypted).decode()
    except Exception as e:
        print(f"密码加密失败: {str(e)}")
        return password  # 如果加密失败，返回原密码


def decrypt_password(encrypted_password: str) -> str:
    """解密密码"""
    if not encrypted_password:
        return ""
    
    try:
        f = _get_fernet()
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_password.encode())
        decrypted = f.decrypt(encrypted_bytes)
        return decrypted.decode()
    except Exception as e:
        print(f"密码解密失败: {str(e)}")
        return encrypted_password  # 如果解密失败，返回原字符串
